{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/endpoint.ts"], "sourcesContent": ["// import config from \"@/config/config\";\n\nconst URL = process.env.NEXT_PUBLIC_BASE_URL;\n\nconst endpoint = {\n  auth: {\n    SIGNIN: `${URL}/auth/sign-in`,\n    VERIFY_OTP: `${URL}/auth/verify-otp`,\n    RESEND_OTP: `${URL}/auth/resend-otp`,\n    FORGOT_PASSWORD: `${URL}/auth/forgot-password`,\n    RESET_PASSWORD: `${URL}/auth/reset-password`,\n    DELETE_SESSION: `${URL}/auth/delete-session`,\n    UPDATE_TIMEZONE: `${URL}/auth/update-timezone`,\n  },\n  interview: {\n    UPDATE_OR_SCHEDULE_INTERVIEW: `${URL}/interview/update-or-schedule-interview`,\n    GET_INTERVIEWS: `${URL}/interview/get-interviews`,\n    GET_INTERVIEWERS: `${URL}/interview/get-interviewers`,\n    GET_MY_INTERVIEWS: `${URL}/interview/get-my-interviews`,\n    UPDATE_INTERVIEW_ANSWERS: `${URL}/interview/update-interview-answers`,\n\n    GET_UPCOMING_OR_PAST_INTERVIEW: `${URL}/interview/get-upcoming-or-past-interviews`,\n    GET_INTERVIEW_SKILL_QUESTIONS: `${URL}/interview/get-interview-skill-questions`,\n    UPDATE_INTERVIEW_SKILL_QUESTION: `${URL}/interview/update-interview-skill-question`,\n    ADD_INTERVIEW_SKILL_QUESTION: `${URL}/interview/add-interview-skill-question`,\n    GET_COMPLETED_SKILLS: `${URL}/interview/get-completed-skills`,\n\n    GET_JOB_LIST: `${URL}/interview/get-job-list`,\n    GET_CANDIDATE_LIST: `${URL}/interview/get-candidate-list`,\n    END_INTERVIEW: `${URL}/interview/end-interview`,\n    CONDUCT_INTERVIEW_STATIC_INFORMATION: `${URL}/interview/conduct-interview-static-information`,\n  },\n  common: {\n    REMOVE_ATTACHMENTS_FROM_S3: `${URL}/remove-attachments-from-s3`,\n    GENERATE_PRESIGNED_URL: `${URL}/generate-presignedurl`,\n  },\n  jobRequirements: {\n    GENERATE_SKILL: `${URL}/jobs/generate-skills`,\n    UPLOAD_URL: `${URL}/jobs/upload-url`,\n    PARSE_PDF: `${URL}/jobs/parse-pdf`,\n    GET_ALL_SKILLS: `${URL}/jobs/get-all-skills`,\n    GENERATE_JOB_REQUIREMENT: `${URL}/jobs/generate-job-requirement`,\n    SAVE_JOB_DETAILS: `${URL}/jobs/save-job-details`,\n    GET_JOBS_META: `${URL}/jobs/get-jobs-meta`, // <-- Full URL here\n    UPDATE_JOB_STATUS: \"/jobs/updateJob\",\n    GET_JOB_HTML_DESCRIPTION: `${URL}/jobs/get-job-html-description`,\n    UPDATE_JOB_DESCRIPTION: `${URL}/jobs/update-job-description`,\n    GENERATE_PDF: `${URL}/jobs/generate-pdf`,\n  },\n  Dashboard: {\n    GET_DASHBOARD_COUNTS: `${URL}/jobs/dashboard-counts`,\n  },\n  resumeScreen: {\n    MANUAL_CANDIDATE_UPLOAD: `${URL}/resume-screen/manual-candidate-upload`,\n    GET_PRESIGNED_URL: `${URL}/resume-screen/get-presigned-url`,\n    GET_ALL_PENDING_JOB_APPLICATIONS: `${URL}/resume-screen/get-all-pending-job-applications`,\n    CHANGE_APPLICATION_STATUS: `${URL}/resume-screen/change-application-status`,\n  },\n  employee: {\n    ADD_EMPLOYEES: `${URL}/employee-management/add-hiring-employee`,\n    GET_EMPLOYEES: `${URL}/employee-management`,\n    GET_EMPLOYEES_BY_DEPARTMENT: `${URL}/employee-management/employees`,\n    UPDATE_EMPLOYEE_ROLE: `${URL}/employee-management/employee/:employeeId/role`,\n    UPDATE_EMPLOYEE_STATUS: `${URL}/employee-management/employee/change-status/:employeeId`,\n    // this task is for future use\n    // DELETE_EMPLOYEE: `${URL}/employee-management/employee/:employeeId`, // original\n    DELETE_EMPLOYEE: `${URL}/employee-management/dummy`, //dummy\n\n    UPDATE_EMPLOYEE_INTERVIEW_ORDER: `${URL}/employee-management/employee/:employeeId/interview-order`,\n  },\n  userprofile: {\n    GET_MY_PROFILE: `${URL}/user-profile/get-my-profile`,\n    UPDATE_MY_PROFILE: `${URL}/user-profile/update-my-profile`,\n  },\n\n  roles: {\n    GET_ROLES_WITH_PAGINATION: `${URL}/access-management/user-roles-pagination`,\n    GET_ROLES: `${URL}/access-management/user-roles`,\n    ADD_USER_ROLE: `${URL}/access-management/add-user-role`,\n    UPDATE_USER_ROLE: `${URL}/access-management/user-role`,\n    DELETE_USER_ROLE: `${URL}/access-management/user-role`,\n    GET_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions`,\n    GET_ROLE_PERMISSIONS_BY_ID: `${URL}/access-management/role-permissions/:roleId`,\n    UPDATE_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions/:roleId`,\n    USER_PERMISSIONS: `${URL}/access-management/user-permissions`,\n  },\n  notification: {\n    UPDATE_NOTIFICATION: `${URL}/notifications/mark-as-watched`,\n    GET_NOTIFICATIONS: `${URL}/notifications/get-notifications`,\n    DELETE_ALL_NOTIFICATIONS: `${URL}/notifications/delete-users-all-notifications`,\n    GET_UNREAD_NOTIFICATIONS_COUNT: `${URL}/notifications/get-unread-notifications-count`,\n  },\n\n  departments: {\n    GET_DEPARTMENTS: `${URL}/employee-management/departments`,\n    ADD_DEPARTMENT: `${URL}/employee-management/add-department`,\n    UPDATE_DEPARTMENT: `${URL}/employee-management/update-department/:departmentId`,\n    DELETE_DEPARTMENT: `${URL}/employee-management/delete-department/:departmentId`,\n  },\n\n  assessment: {\n    CREATE_FINAL_ASSESSMENT: `${URL}/final-assessment/create-final-assessment`,\n    GET_FINAL_ASSESSMENT_QUESTIONS: `${URL}/final-assessment/assessment/questions`,\n    CREATE_ASSESSMENT_QUESTION: `${URL}/final-assessment/assessment/create-question`,\n    SUBMIT_CANDIDATE_ANSWERS: `${URL}/final-assessment/candidate/:candidateId/submit`,\n    SHARE_ASSESSMENT: `${URL}/final-assessment/assessment/share`,\n    GET_FINAL_ASSESSMENT_BY_CANDIDATE: `${URL}/final-assessment/candidate/assessment`,\n    SUBMIT_ASSESSMENT: `${URL}/final-assessment/candidate/assessment/submit`,\n    GET_ASSESSMENT_STATUS: `${URL}/final-assessment/assessment-status`,\n    VERIFY_CANDIDATE_EMAIL: `${URL}/final-assessment/candidate/verify-email`,\n    GENERATE_ASSESSMENT_TOKEN: `${URL}/final-assessment/assessment/generate-token`,\n  },\n  candidatesApplication: {\n    ADDITIONAL_INFO: `${URL}/candidates/add-applicant-additional-info`,\n    PROMOTE_DEMOTE_CANDIDATE: `${URL}/candidates/update-candidate-rank-status`, // Base URL for candidates-related endpoints\n    GET_TOP_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/top-candidates`,\n    GET_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/get-candidates`,\n    ARCHIVE_ACTIVE_APPLICATION: `${URL}/candidates/archive-active-application/:applicationId`,\n    GET_CANDIDATE_DETAILS: `${URL}/candidates/get-candidate-details`, // New endpoint for individual candidate details\n    UPDATE_JOB_APPLICATION_STATUS: `${URL}/candidates/update-job-application-status/:jobApplicationId`, // Endpoint for updating job application status\n    GET_CANDIDATE_INTERVIEW_HISTORY: `${URL}/candidates/get-candidate-interview-history/:applicationId`,\n    GET_APPLICATION_FINAL_SUMMARY: `${URL}/candidates/application-final-summary/:jobApplicationId`,\n    GET_APPLICATION_SKILL_SCORE_DATA: `${URL}/candidates/application-skill-score-data/:jobApplicationId`,\n    GENERATE_FINAL_SUMMARY: `${URL}/candidates/generate-final-summary`, // Endpoint for generating final summary\n  },\n  subscription: {\n    GET_ALL_PLANS: `${URL}/subscription/all`,\n    GET_CURRENT_SUBSCRIPTION: `${URL}/subscription/current`,\n    CANCEL_SUBSCRIPTION: `${URL}/subscription/cancel`,\n    GET_TRANSACTIONS: `${URL}/subscription/transactions`,\n    BUY_SUBSCRIPTION: `${URL}/subscription/buy-subscription`,\n  },\n};\n\nexport default endpoint;\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;AAExC,MAAM;AAEN,MAAM,WAAW;IACf,MAAM;QACJ,QAAQ,GAAG,IAAI,aAAa,CAAC;QAC7B,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;QAC9C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;IAChD;IACA,WAAW;QACT,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,gBAAgB,GAAG,IAAI,yBAAyB,CAAC;QACjD,kBAAkB,GAAG,IAAI,2BAA2B,CAAC;QACrD,mBAAmB,GAAG,IAAI,4BAA4B,CAAC;QACvD,0BAA0B,GAAG,IAAI,mCAAmC,CAAC;QAErE,gCAAgC,GAAG,IAAI,0CAA0C,CAAC;QAClF,+BAA+B,GAAG,IAAI,wCAAwC,CAAC;QAC/E,iCAAiC,GAAG,IAAI,0CAA0C,CAAC;QACnF,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,sBAAsB,GAAG,IAAI,+BAA+B,CAAC;QAE7D,cAAc,GAAG,IAAI,uBAAuB,CAAC;QAC7C,oBAAoB,GAAG,IAAI,6BAA6B,CAAC;QACzD,eAAe,GAAG,IAAI,wBAAwB,CAAC;QAC/C,sCAAsC,GAAG,IAAI,+CAA+C,CAAC;IAC/F;IACA,QAAQ;QACN,4BAA4B,GAAG,IAAI,2BAA2B,CAAC;QAC/D,wBAAwB,GAAG,IAAI,sBAAsB,CAAC;IACxD;IACA,iBAAiB;QACf,gBAAgB,GAAG,IAAI,qBAAqB,CAAC;QAC7C,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,WAAW,GAAG,IAAI,eAAe,CAAC;QAClC,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,kBAAkB,GAAG,IAAI,sBAAsB,CAAC;QAChD,eAAe,GAAG,IAAI,mBAAmB,CAAC;QAC1C,mBAAmB;QACnB,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,wBAAwB,GAAG,IAAI,4BAA4B,CAAC;QAC5D,cAAc,GAAG,IAAI,kBAAkB,CAAC;IAC1C;IACA,WAAW;QACT,sBAAsB,GAAG,IAAI,sBAAsB,CAAC;IACtD;IACA,cAAc;QACZ,yBAAyB,GAAG,IAAI,sCAAsC,CAAC;QACvE,mBAAmB,GAAG,IAAI,gCAAgC,CAAC;QAC3D,kCAAkC,GAAG,IAAI,+CAA+C,CAAC;QACzF,2BAA2B,GAAG,IAAI,wCAAwC,CAAC;IAC7E;IACA,UAAU;QACR,eAAe,GAAG,IAAI,wCAAwC,CAAC;QAC/D,eAAe,GAAG,IAAI,oBAAoB,CAAC;QAC3C,6BAA6B,GAAG,IAAI,8BAA8B,CAAC;QACnE,sBAAsB,GAAG,IAAI,8CAA8C,CAAC;QAC5E,wBAAwB,GAAG,IAAI,uDAAuD,CAAC;QACvF,8BAA8B;QAC9B,kFAAkF;QAClF,iBAAiB,GAAG,IAAI,0BAA0B,CAAC;QAEnD,iCAAiC,GAAG,IAAI,yDAAyD,CAAC;IACpG;IACA,aAAa;QACX,gBAAgB,GAAG,IAAI,4BAA4B,CAAC;QACpD,mBAAmB,GAAG,IAAI,+BAA+B,CAAC;IAC5D;IAEA,OAAO;QACL,2BAA2B,GAAG,IAAI,wCAAwC,CAAC;QAC3E,WAAW,GAAG,IAAI,6BAA6B,CAAC;QAChD,eAAe,GAAG,IAAI,gCAAgC,CAAC;QACvD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,sBAAsB,GAAG,IAAI,mCAAmC,CAAC;QACjE,4BAA4B,GAAG,IAAI,2CAA2C,CAAC;QAC/E,yBAAyB,GAAG,IAAI,2CAA2C,CAAC;QAC5E,kBAAkB,GAAG,IAAI,mCAAmC,CAAC;IAC/D;IACA,cAAc;QACZ,qBAAqB,GAAG,IAAI,8BAA8B,CAAC;QAC3D,mBAAmB,GAAG,IAAI,gCAAgC,CAAC;QAC3D,0BAA0B,GAAG,IAAI,6CAA6C,CAAC;QAC/E,gCAAgC,GAAG,IAAI,6CAA6C,CAAC;IACvF;IAEA,aAAa;QACX,iBAAiB,GAAG,IAAI,gCAAgC,CAAC;QACzD,gBAAgB,GAAG,IAAI,mCAAmC,CAAC;QAC3D,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;QAC/E,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;IACjF;IAEA,YAAY;QACV,yBAAyB,GAAG,IAAI,yCAAyC,CAAC;QAC1E,gCAAgC,GAAG,IAAI,sCAAsC,CAAC;QAC9E,4BAA4B,GAAG,IAAI,4CAA4C,CAAC;QAChF,0BAA0B,GAAG,IAAI,+CAA+C,CAAC;QACjF,kBAAkB,GAAG,IAAI,kCAAkC,CAAC;QAC5D,mCAAmC,GAAG,IAAI,sCAAsC,CAAC;QACjF,mBAAmB,GAAG,IAAI,6CAA6C,CAAC;QACxE,uBAAuB,GAAG,IAAI,mCAAmC,CAAC;QAClE,wBAAwB,GAAG,IAAI,wCAAwC,CAAC;QACxE,2BAA2B,GAAG,IAAI,2CAA2C,CAAC;IAChF;IACA,uBAAuB;QACrB,iBAAiB,GAAG,IAAI,yCAAyC,CAAC;QAClE,0BAA0B,GAAG,IAAI,wCAAwC,CAAC;QAC1E,sCAAsC,GAAG,IAAI,0BAA0B,CAAC;QACxE,kCAAkC,GAAG,IAAI,0BAA0B,CAAC;QACpE,4BAA4B,GAAG,IAAI,qDAAqD,CAAC;QACzF,uBAAuB,GAAG,IAAI,iCAAiC,CAAC;QAChE,+BAA+B,GAAG,IAAI,2DAA2D,CAAC;QAClG,iCAAiC,GAAG,IAAI,0DAA0D,CAAC;QACnG,+BAA+B,GAAG,IAAI,uDAAuD,CAAC;QAC9F,kCAAkC,GAAG,IAAI,0DAA0D,CAAC;QACpG,wBAAwB,GAAG,IAAI,kCAAkC,CAAC;IACpE;IACA,cAAc;QACZ,eAAe,GAAG,IAAI,iBAAiB,CAAC;QACxC,0BAA0B,GAAG,IAAI,qBAAqB,CAAC;QACvD,qBAAqB,GAAG,IAAI,oBAAoB,CAAC;QACjD,kBAAkB,GAAG,IAAI,0BAA0B,CAAC;QACpD,kBAAkB,GAAG,IAAI,8BAA8B,CAAC;IAC1D;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/routes.ts"], "sourcesContent": ["const ROUTES = {\n  LOGIN: \"/login\",\n  FORGOT_PASSWORD: \"/forgot-password\",\n  VERIFY: \"/verify\",\n  RESET_PASSWORD: \"/reset-password\",\n  CANDIDATE_ASSESSMENT: \"/candidate-assessment\",\n  DASHBOARD: \"/dashboard\",\n  HOME: \"/\",\n  BUY_SUBSCRIPTION: \"/buy-subscription\",\n  PROFILE: {\n    MY_PROFILE: \"/my-profile\",\n  },\n  SUBSCRIPTIONS: {\n    SUCCESS: \"/subscriptions/success\",\n    CANCEL: \"/subscriptions/cancel\",\n  },\n  JOBS: {\n    CAREER_BASED_SKILLS: \"/career-based-skills\",\n    ROLE_BASED_SKILLS: \"/role-based-skills\",\n    CULTURE_BASED_SKILLS: \"/culture-based-skills\",\n    GENERATE_JOB: \"/generate-job\",\n    EDIT_SKILLS: \"/edit-skills\",\n    HIRING_TYPE: \"/hiring-type\",\n    JOB_EDITOR: \"/job-editor\",\n    ACTIVE_JOBS: \"/active-jobs\",\n    CANDIDATE_PROFILE: \"/candidate-profile\",\n    ARCHIVE: \"/archive\",\n  },\n  SCREEN_RESUME: {\n    MANUAL_CANDIDATE_UPLOAD: \"/manual-upload-resume\",\n    CANDIDATE_QUALIFICATION: \"/candidate-qualification\",\n    CANDIDATE_LIST: \"/candidates-list\",\n    CANDIDATES: \"/candidates\",\n  },\n  INTERVIEW: {\n    ADD_CANDIDATE_INFO: \"/additional-submission\",\n    SCHEDULE_INTERVIEW: \"/schedule-interview\",\n    PRE_INTERVIEW_QUESTIONS_OVERVIEW: \"/pre-interview-questions-overview\",\n    INTERVIEW_QUESTION: \"/interview-question\",\n    CALENDAR: \"/calendar\",\n    INTERVIEW_SUMMARY: \"/interview-summary\",\n  },\n\n  ROLE_EMPLOYEES: {\n    ROLES_PERMISSIONS: \"/roles-permissions\",\n    EMPLOYEE_MANAGEMENT: \"/employee-management\",\n    EMPLOYEE_MANAGEMENT_DETAIL: \"/employee-management-detail\",\n    ADD_EMPLOYEE: \"/add-employees\",\n    ADD_DEPARTMENT: \"/add-department\",\n  },\n\n  FINAL_ASSESSMENT: {\n    FINAL_ASSESSMENT: \"/final-assessment\",\n  },\n};\n\nexport const BEFORE_LOGIN_ROUTES = [ROUTES.LOGIN, ROUTES.FORGOT_PASSWORD, ROUTES.VERIFY, ROUTES.RESET_PASSWORD, ROUTES.CANDIDATE_ASSESSMENT];\n\n// Routes that don't require permission checks for authenticated users\nexport const UNRESTRICTED_ROUTES = [ROUTES.SUBSCRIPTIONS.SUCCESS, ROUTES.SUBSCRIPTIONS.CANCEL];\n\nexport default ROUTES;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,SAAS;IACb,OAAO;IACP,iBAAiB;IACjB,QAAQ;IACR,gBAAgB;IAChB,sBAAsB;IACtB,WAAW;IACX,MAAM;IACN,kBAAkB;IAClB,SAAS;QACP,YAAY;IACd;IACA,eAAe;QACb,SAAS;QACT,QAAQ;IACV;IACA,MAAM;QACJ,qBAAqB;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,cAAc;QACd,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,mBAAmB;QACnB,SAAS;IACX;IACA,eAAe;QACb,yBAAyB;QACzB,yBAAyB;QACzB,gBAAgB;QAChB,YAAY;IACd;IACA,WAAW;QACT,oBAAoB;QACpB,oBAAoB;QACpB,kCAAkC;QAClC,oBAAoB;QACpB,UAAU;QACV,mBAAmB;IACrB;IAEA,gBAAgB;QACd,mBAAmB;QACnB,qBAAqB;QACrB,4BAA4B;QAC5B,cAAc;QACd,gBAAgB;IAClB;IAEA,kBAAkB;QAChB,kBAAkB;IACpB;AACF;AAEO,MAAM,sBAAsB;IAAC,OAAO,KAAK;IAAE,OAAO,eAAe;IAAE,OAAO,MAAM;IAAE,OAAO,cAAc;IAAE,OAAO,oBAAoB;CAAC;AAGrI,MAAM,sBAAsB;IAAC,OAAO,aAAa,CAAC,OAAO;IAAE,OAAO,aAAa,CAAC,MAAM;CAAC;uCAE/E", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/pages/api/auth/%5B...nextauth%5D.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\n\nimport endpoints from \"@/constants/endpoint\";\nimport routes from \"@/constants/routes\";\n\nexport default NextAuth({\n  providers: [\n    CredentialsProvider({\n      name: \"Credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" },\n      },\n\n      async authorize(credentials) {\n        const { email, password } = credentials as { email: string; password: string };\n\n        const res = await fetch(endpoints.auth.SIGNIN, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify({\n            email,\n            password,\n          }),\n        });\n\n        const user = await res.json();\n\n        if (user) {\n          return user;\n        } else return null;\n      },\n    }),\n  ],\n  session: {\n    maxAge: 3 * 24 * 60 * 60,\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n  pages: {\n    signIn: routes.LOGIN,\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      return { ...token, ...user };\n    },\n    async session({ session, token }) {\n      session.user = token;\n      return session;\n    },\n  },\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;;;;;uCAEe,CAAA,GAAA,iHAAA,CAAA,UAAQ,AAAD,EAAE;IACtB,WAAW;QACT,CAAA,GAAA,yKAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YAEA,MAAM,WAAU,WAAW;gBACzB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;gBAE5B,MAAM,MAAM,MAAM,MAAM,qHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,MAAM,EAAE;oBAC7C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;oBACF;gBACF;gBAEA,MAAM,OAAO,MAAM,IAAI,IAAI;gBAE3B,IAAI,MAAM;oBACR,OAAO;gBACT,OAAO,OAAO;YAChB;QACF;KACD;IACD,SAAS;QACP,QAAQ,IAAI,KAAK,KAAK;IACxB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;IACnC,OAAO;QACL,QAAQ,mHAAA,CAAA,UAAM,CAAC,KAAK;IACtB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,OAAO;gBAAE,GAAG,KAAK;gBAAE,GAAG,IAAI;YAAC;QAC7B;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,QAAQ,IAAI,GAAG;YACf,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/next/dist/src/server/route-modules/pages-api/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/pages-api/module.js')\n} else {\n  if (process.env.NODE_ENV === 'development') {\n    module.exports = require('next/dist/compiled/next-server/pages-api.runtime.dev.js')\n  } else if (process.env.TURBOPACK) {\n    module.exports = require('next/dist/compiled/next-server/pages-api-turbo.runtime.prod.js')\n  } else {\n    module.exports = require('next/dist/compiled/next-server/pages-api.runtime.prod.js')\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,QAAQ,KAAK,WAAe;QAC1CH,OAAOC,OAAO,GAAGC,QAAQ;IAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACM,SAAS,EAAE;;IAIlC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/next/dist/src/server/route-kind.ts"], "sourcesContent": ["export const enum RouteKind {\n  /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */\n  PAGES = 'PAGES',\n  /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */\n  PAGES_API = 'PAGES_API',\n  /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */\n  APP_PAGE = 'APP_PAGE',\n  /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */\n  APP_ROUTE = 'APP_ROUTE',\n\n  /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */\n  IMAGE = 'IMAGE',\n}\n"], "names": ["RouteKind"], "mappings": ";;;AAAO,IAAWA,YAAAA,WAAAA,GAAAA,SAAAA,SAAAA;IAChB;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;IAED;;GAEC,GAAA,SAAA,CAAA,YAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,WAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,YAAA,GAAA;IAGD;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;WAtBeA;MAwBjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/next/dist/src/build/templates/helpers.ts"], "sourcesContent": ["/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */\nexport function hoist(module: any, name: string) {\n  // If the name is available in the module, return it.\n  if (name in module) {\n    return module[name]\n  }\n\n  // If a property called `then` exists, assume it's a promise and\n  // return a promise that resolves to the name.\n  if ('then' in module && typeof module.then === 'function') {\n    return module.then((mod: any) => hoist(mod, name))\n  }\n\n  // If we're trying to hoise the default export, and the module is a function,\n  // return the module itself.\n  if (typeof module === 'function' && name === 'default') {\n    return module\n  }\n\n  // Otherwise, return undefined.\n  return undefined\n}\n"], "names": ["hoist", "module", "name", "then", "mod", "undefined"], "mappings": "AAAA;;;;;;CAMC,GACD;;;AAAO,SAASA,MAAMC,MAAW,EAAEC,IAAY;IAC7C,qDAAqD;IACrD,IAAIA,QAAQD,QAAQ;QAClB,OAAOA,MAAM,CAACC,KAAK;IACrB;IAEA,gEAAgE;IAChE,8CAA8C;IAC9C,IAAI,UAAUD,UAAU,OAAOA,OAAOE,IAAI,KAAK,YAAY;QACzD,OAAOF,OAAOE,IAAI,CAAC,CAACC,MAAaJ,MAAMI,KAAKF;IAC9C;IAEA,6EAA6E;IAC7E,4BAA4B;IAC5B,IAAI,OAAOD,WAAW,cAAcC,SAAS,WAAW;QACtD,OAAOD;IACT;IAEA,+BAA+B;IAC/B,OAAOI;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/next/dist/src/build/templates/pages-api.ts"], "sourcesContent": ["import { PagesAPIRouteModule } from '../../server/route-modules/pages-api/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\n\nimport { hoist } from './helpers'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\n\n// Re-export the handler (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export config.\nexport const config = hoist(userland, 'config')\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesAPIRouteModule({\n  definition: {\n    kind: RouteKind.PAGES_API,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  userland,\n})\n"], "names": ["PagesAPIRouteModule", "RouteKind", "hoist", "userland", "config", "routeModule", "definition", "kind", "PAGES_API", "page", "pathname", "bundlePath", "filename"], "mappings": ";;;;;AAAA,SAASA,mBAAmB,QAAQ,uDAAsD;AAC1F,SAASC,SAAS,QAAQ,0BAAyB;AAEnD,SAASC,KAAK,QAAQ,YAAW;AAEjC,4BAA4B;AAC5B,YAAYC,cAAc,eAAc;;;;;yMAGzBD,QAAAA,EAAMC,4IAAU,WAAU;AAGlC,MAAMC,2KAASF,QAAAA,EAAMC,4IAAU,UAAS;AAGxC,MAAME,cAAc,qMAAIL,sBAAAA,CAAoB;IACjDM,YAAY;QACVC,8JAAMN,YAAAA,CAAUO,SAAS;QACzBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;cACAT;AACF,GAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}