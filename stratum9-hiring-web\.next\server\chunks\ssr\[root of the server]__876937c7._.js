module.exports = {

"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/redux/slices/jobSkillsSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearSkillsData": (()=>clearSkillsData),
    "default": (()=>__TURBOPACK__default__export__),
    "jobSkillsSlice": (()=>jobSkillsSlice),
    "selectCareerSkills": (()=>selectCareerSkills),
    "selectCultureSpecificSkills": (()=>selectCultureSpecificSkills),
    "selectJobSkillsState": (()=>selectJobSkillsState),
    "selectRoleSpecificSkills": (()=>selectRoleSpecificSkills),
    "setSkillsData": (()=>setSkillsData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
// Define the initial state using that type
const initialState = {
    careerSkills: [],
    roleSpecificSkills: [],
    cultureSpecificSkills: []
};
const jobSkillsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "jobSkills",
    initialState,
    reducers: {
        setSkillsData: (state, action)=>{
            if (action.payload.careerSkills) {
                state.careerSkills = action.payload.careerSkills;
            }
            if (action.payload.roleSpecificSkills) {
                state.roleSpecificSkills = action.payload.roleSpecificSkills;
            }
            if (action.payload.cultureSpecificSkills) {
                state.cultureSpecificSkills = action.payload.cultureSpecificSkills;
            }
        },
        clearSkillsData: (state)=>{
            state.careerSkills = [];
            state.roleSpecificSkills = [];
            state.cultureSpecificSkills = [];
        }
    }
});
const { setSkillsData, clearSkillsData } = jobSkillsSlice.actions;
const selectJobSkillsState = (state)=>state.jobSkills;
const selectCareerSkills = (state)=>state.jobSkills.careerSkills;
const selectRoleSpecificSkills = (state)=>state.jobSkills.roleSpecificSkills;
const selectCultureSpecificSkills = (state)=>state.jobSkills.cultureSpecificSkills;
const __TURBOPACK__default__export__ = jobSkillsSlice.reducer;
}}),
"[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACCESS_TOKEN_KEY": (()=>ACCESS_TOKEN_KEY),
    "ACTIVE": (()=>ACTIVE),
    "ASSESSMENT_INSTRUCTIONS": (()=>ASSESSMENT_INSTRUCTIONS),
    "DEFAULT_LIMIT": (()=>DEFAULT_LIMIT),
    "DEFAULT_MCQ_OPTIONS": (()=>DEFAULT_MCQ_OPTIONS),
    "DEFAULT_OFFSET": (()=>DEFAULT_OFFSET),
    "DEFAULT_TRUE_FALSE_OPTIONS": (()=>DEFAULT_TRUE_FALSE_OPTIONS),
    "EMAIL_REGEX": (()=>EMAIL_REGEX),
    "EMPTY_CONTENT_PATTERNS": (()=>EMPTY_CONTENT_PATTERNS),
    "FILE_EXTENSION": (()=>FILE_EXTENSION),
    "IMAGE_EXTENSIONS": (()=>IMAGE_EXTENSIONS),
    "INTERVIEW_SCHEDULE_ROUND_TYPE": (()=>INTERVIEW_SCHEDULE_ROUND_TYPE),
    "MAX_IMAGE_SIZE": (()=>MAX_IMAGE_SIZE),
    "MessageType": (()=>MessageType),
    "NAME_REGEX": (()=>NAME_REGEX),
    "ONE_TO_ONE_INTERVIEW_INSTRUCTIONS": (()=>ONE_TO_ONE_INTERVIEW_INSTRUCTIONS),
    "OPTION_ID": (()=>OPTION_ID),
    "PASSWORD_REGEX": (()=>PASSWORD_REGEX),
    "PDF_ADDITIONAL_SUBMISSION_LIMIT": (()=>PDF_ADDITIONAL_SUBMISSION_LIMIT),
    "PDF_FILE_NAME": (()=>PDF_FILE_NAME),
    "PDF_FILE_SIZE_LIMIT": (()=>PDF_FILE_SIZE_LIMIT),
    "PDF_FILE_TYPE": (()=>PDF_FILE_TYPE),
    "PERMISSION": (()=>PERMISSION),
    "PERMISSIONS_COOKIES_KEY": (()=>PERMISSIONS_COOKIES_KEY),
    "QUESTION_TYPE": (()=>QUESTION_TYPE),
    "QUESTION_TYPES": (()=>QUESTION_TYPES),
    "QuestionType": (()=>QuestionType),
    "S3_PATHS": (()=>S3_PATHS),
    "SKILL_CONSTANTS": (()=>SKILL_CONSTANTS),
    "STANDARD_LIMIT": (()=>STANDARD_LIMIT),
    "ScheduleInterviewFormSubmissionType": (()=>ScheduleInterviewFormSubmissionType),
    "TOKEN_EXPIRED": (()=>TOKEN_EXPIRED),
    "VIDEO_CALL_INTERVIEW_INSTRUCTIONS": (()=>VIDEO_CALL_INTERVIEW_INSTRUCTIONS),
    "commonConstants": (()=>commonConstants),
    "initialState": (()=>initialState)
});
const ACCESS_TOKEN_KEY = "__ATK__";
const EMAIL_REGEX = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;
const PASSWORD_REGEX = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$/;
const NAME_REGEX = /^[a-zA-Z0-9\s.'-]+$/;
const MAX_IMAGE_SIZE = 5242880;
const ScheduleInterviewFormSubmissionType = {
    SCHEDULE: "schedule",
    UPDATE: "update"
};
const S3_PATHS = {
    PROFILE_IMAGE: "profile-images/:path"
};
const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [
    "Arrive at the interview location on time with a government-issued ID.",
    "Ensure your phone is on silent mode and distractions are minimized.",
    "Bring a printed copy of your resume and any supporting documents.",
    "Dress professionally and maintain proper body language.",
    "Listen carefully, answer honestly, and ask for clarification if needed.",
    "Respect the interview flow and do not interrupt the interviewer.",
    "Take brief notes if necessary, but focus on active conversation.",
    "If you need assistance or face any issues, notify the interview coordinator."
];
const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [
    "Join the interview on time using the link provided.",
    "Ensure a stable internet connection and a quiet, well-lit space.",
    "Test your camera, microphone, and audio settings in advance.",
    "Keep your video on unless instructed otherwise by the interviewer.",
    "Minimize background noise and avoid multitasking during the session.",
    "Use headphones if possible for better audio clarity.",
    "Be attentive, respond clearly, and maintain professional posture.",
    "Contact support if you face technical difficulties before or during the interview."
];
const PERMISSION = {
    CREATE_OR_EDIT_JOB_POST: "create-or-edit-job-post",
    SCHEDULE_CONDUCT_INTERVIEWS: "schedule-conduct-interviews",
    VIEW_HIRED_CANDIDATES: "view-hired-candidates",
    ARCHIVE_RESTORE_CANDIDATES: "archive-restore-candidates",
    ARCHIVE_RESTORE_JOB_POSTS: "archive-restore-job-posts",
    MANUAL_RESUME_SCREENING: "manual-resume-screening",
    EDIT_SCHEDULED_INTERVIEWS: "edit-scheduled-interviews",
    ADD_ADDITIONAL_CANDIDATE_INFO: "add-additional-candidate-info",
    ADD_OR_EDIT_INTERVIEW_NOTES: "add-or-edit-interview-notes",
    MANAGE_TOP_CANDIDATES: "manage-top-candidates",
    MANAGE_PRE_INTERVIEW_QUESTIONS: "manage-pre-interview-questions",
    CREATE_FINAL_ASSESSMENT: "create-final-assessment",
    VIEW_FINAL_ASSESSMENT: "view-final-assessment",
    VIEW_CANDIDATE_PROFILE_SUMMARY: "view-candidate-profile-summary",
    HIRE_CANDIDATE: "hire-candidate",
    CREATE_NEW_ROLE: "create-new-role",
    MANAGE_USER_PERMISSIONS: "manage-user-permissions",
    CREATE_NEW_DEPARTMENT: "create-new-department",
    ADD_INTERVIEW_PARTICIPANTS: "add-interview-participants",
    VIEW_SUBSCRIPTION_PLAN: "view-subscription-plan",
    MANAGE_SUBSCRIPTIONS: "manage-subscriptions",
    VIEW_AUDIT_LOGS_UPCOMING: "view-audit-logs-upcoming",
    VIEW_ALL_SCHEDULED_INTERVIEWS: "view-all-scheduled-interviews"
};
const SKILL_CONSTANTS = {
    REQUIRED_ROLE_SKILLS: 10,
    REQUIRED_CULTURE_SKILLS: 5
};
const commonConstants = {
    finalAssessmentId: "finalAssessmentId",
    token: "token",
    isShared: "isShared",
    isSubmitted: "isSubmitted",
    jobId: "jobId",
    jobApplicationId: "jobApplicationId"
};
const QuestionType = {
    MCQ: "mcq",
    TRUE_FALSE: "true_false"
};
const OPTION_ID = {
    A: "A",
    B: "B",
    C: "C",
    D: "D",
    TRUE: "true",
    FALSE: "false"
};
const QUESTION_TYPE = {
    MCQ: "mcq",
    TRUE_FALSE: "true_false"
};
const DEFAULT_MCQ_OPTIONS = [
    {
        id: OPTION_ID.A,
        text: ""
    },
    {
        id: OPTION_ID.B,
        text: ""
    },
    {
        id: OPTION_ID.C,
        text: ""
    },
    {
        id: OPTION_ID.D,
        text: ""
    }
];
const DEFAULT_TRUE_FALSE_OPTIONS = [
    {
        id: OPTION_ID.TRUE,
        text: "True"
    },
    {
        id: OPTION_ID.FALSE,
        text: "False"
    }
];
const INTERVIEW_SCHEDULE_ROUND_TYPE = [
    {
        label: "One-On-One",
        value: "One-On-One"
    },
    {
        label: "Video Call",
        value: "Video Call"
    }
];
const QUESTION_TYPES = {
    ROLE_SPECIFIC: "role_specific",
    CULTURE_SPECIFIC: "culture_specific",
    CAREER_BASED: "career_based"
};
const EMPTY_CONTENT_PATTERNS = [
    "<p><br></p>",
    "<p></p>",
    "<div><br></div>",
    "<div></div>",
    "<p>&nbsp;</p>"
];
const initialState = {
    title: "",
    employment_type: "",
    department_id: "",
    salary_range: "",
    salary_cycle: "",
    location_type: "",
    state: "",
    city: "",
    role_overview: "",
    experience_level: "",
    responsibilities: "",
    educations_requirement: "",
    certifications: undefined,
    skills_and_software_expertise: "",
    experience_required: "",
    ideal_candidate_traits: "",
    about_company: "",
    perks_benefits: undefined,
    tone_style: "",
    additional_info: undefined,
    compliance_statement: [],
    show_compliance: false,
    hiring_type: ""
};
const FILE_EXTENSION = [
    "pdf",
    "plain",
    "csv",
    "vnd.ms-excel.sheet.macroEnabled.12",
    "vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "vnd.openxmlformats-officedocument.wordprocessingml.document",
    "vnd.openxmlformats-officedocument.presentationml.presentation"
];
const ACTIVE = "active";
const TOKEN_EXPIRED = "Session Expired! Please log in again.";
const DEFAULT_LIMIT = 15;
const STANDARD_LIMIT = 18;
const DEFAULT_OFFSET = 0;
var MessageType = /*#__PURE__*/ function(MessageType) {
    MessageType["success"] = "success";
    MessageType["error"] = "error";
    return MessageType;
}({});
const IMAGE_EXTENSIONS = [
    "png",
    "jpg",
    "jpeg",
    "gif",
    "webp"
];
const ASSESSMENT_INSTRUCTIONS = {
    instructions: [
        "Do not refresh or close the browser",
        "Check your internet connection",
        "Ensure a distraction-free environment",
        "Click 'Submit' only once when finished",
        "Read each question carefully",
        "Manage your time efficiently",
        "Avoid any form of plagiarism",
        "Reach out to support if needed"
    ]
};
const PERMISSIONS_COOKIES_KEY = "permissions_data";
const PDF_FILE_NAME = "pdf";
const PDF_FILE_TYPE = "application/pdf";
const PDF_FILE_SIZE_LIMIT = 5 * 1024 * 1024;
const PDF_ADDITIONAL_SUBMISSION_LIMIT = 10854484;
}}),
"[project]/src/redux/slices/jobDetailsSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearJobDetails": (()=>clearJobDetails),
    "default": (()=>__TURBOPACK__default__export__),
    "jobDetailsSlice": (()=>jobDetailsSlice),
    "selectJobDetails": (()=>selectJobDetails),
    "setJobDetails": (()=>setJobDetails),
    "updateJobDetail": (()=>updateJobDetail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
;
;
const jobDetailsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "jobDetails",
    initialState: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initialState"],
    reducers: {
        // Set all job details at once
        setJobDetails: (state, action)=>{
            return {
                ...state,
                ...action.payload
            };
        },
        // Clear job details
        clearJobDetails: ()=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initialState"];
        },
        // Update a specific field in job details
        updateJobDetail: (state, action)=>{
            const { field, value } = action.payload;
            state[field] = value;
        }
    }
});
const { setJobDetails, clearJobDetails, updateJobDetail } = jobDetailsSlice.actions;
const selectJobDetails = (state)=>state.jobDetails;
const __TURBOPACK__default__export__ = jobDetailsSlice.reducer;
}}),
"[project]/src/redux/slices/allSkillsSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "allSkillsSlice": (()=>allSkillsSlice),
    "default": (()=>__TURBOPACK__default__export__),
    "fetchSkillsFailure": (()=>fetchSkillsFailure),
    "fetchSkillsStart": (()=>fetchSkillsStart),
    "fetchSkillsSuccess": (()=>fetchSkillsSuccess),
    "selectAllSkills": (()=>selectAllSkills),
    "selectSkillsByCategory": (()=>selectSkillsByCategory),
    "selectSkillsError": (()=>selectSkillsError),
    "selectSkillsLoading": (()=>selectSkillsLoading),
    "updateSkillItem": (()=>updateSkillItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
// Define the initial state
const initialState = {
    categories: [],
    loading: false,
    error: null
};
const allSkillsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "allSkills",
    initialState,
    reducers: {
        fetchSkillsStart: (state)=>{
            state.loading = true;
            state.error = null;
        },
        fetchSkillsSuccess: (state, action)=>{
            state.categories = action.payload;
            state.loading = false;
            state.error = null;
        },
        fetchSkillsFailure: (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        },
        updateSkillItem: (state, action)=>{
            const { categoryType, skillId, updatedSkill } = action.payload;
            const categoryIndex = state.categories.findIndex((cat)=>cat.type === categoryType);
            if (categoryIndex !== -1) {
                const skillIndex = state.categories[categoryIndex].items.findIndex((item)=>item.id === skillId);
                if (skillIndex !== -1) {
                    state.categories[categoryIndex].items[skillIndex] = {
                        ...state.categories[categoryIndex].items[skillIndex],
                        ...updatedSkill
                    };
                }
            }
        }
    }
});
const { fetchSkillsStart, fetchSkillsSuccess, fetchSkillsFailure, updateSkillItem } = allSkillsSlice.actions;
const selectAllSkills = (state)=>state.allSkills.categories;
const selectSkillsLoading = (state)=>state.allSkills.loading;
const selectSkillsError = (state)=>state.allSkills.error;
const selectSkillsByCategory = (categoryType)=>(state)=>state.allSkills.categories.find((cat)=>cat.type === categoryType)?.items || [];
const __TURBOPACK__default__export__ = allSkillsSlice.reducer;
}}),
"[project]/src/redux/slices/authSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authSlice": (()=>authSlice),
    "default": (()=>__TURBOPACK__default__export__),
    "selectCurrentPlan": (()=>selectCurrentPlan),
    "selectDepartment": (()=>selectDepartment),
    "selectPermissions": (()=>selectPermissions),
    "selectProfileData": (()=>selectProfileData),
    "selectRole": (()=>selectRole),
    "setAuthData": (()=>setAuthData),
    "setCurrentPlan": (()=>setCurrentPlan),
    "setDepartment": (()=>setDepartment),
    "setPermissions": (()=>setPermissions),
    "setRole": (()=>setRole),
    "updateUserProfileData": (()=>updateUserProfileData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
const initialState = {
    authData: {
        id: -1,
        account_type: "",
        email: "",
        isVerified: false,
        sms_notification: false,
        allow_notification: false,
        is_deleted: false,
        image: "",
        orgId: -1,
        departmentId: -1,
        organizationName: "",
        organizationCode: "",
        createdTs: "",
        first_name: "",
        last_name: ""
    },
    department: null,
    role: null,
    permissions: [],
    currentPlan: null
};
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "auth",
    initialState,
    reducers: {
        setAuthData: (state, action)=>{
            state.authData = action.payload;
        },
        setRole: (state, action)=>{
            state.role = action.payload;
        },
        setDepartment: (state, action)=>{
            state.department = action.payload;
        },
        setPermissions: (state, action)=>{
            state.permissions = action.payload;
        },
        setCurrentPlan: (state, action)=>{
            state.currentPlan = action.payload;
        },
        updateUserProfileData: (state, action)=>{
            if (state.authData) {
                const { first_name, last_name, image } = action.payload;
                // Update firstName and lastName separately
                if (first_name !== undefined) {
                    state.authData.first_name = first_name;
                }
                if (last_name !== undefined) {
                    state.authData.last_name = last_name;
                }
                // Update image if provided
                if (image !== undefined) {
                    state.authData.image = image;
                }
            }
        }
    }
});
const selectRole = (state)=>state.auth.role;
const selectDepartment = (state)=>state.auth.department;
const selectPermissions = (state)=>state.auth.permissions;
const selectProfileData = (state)=>state.auth.authData;
const selectCurrentPlan = (state)=>state.auth.currentPlan;
const { setAuthData, setRole, setDepartment, setPermissions, updateUserProfileData, setCurrentPlan } = authSlice.actions;
const __TURBOPACK__default__export__ = authSlice.reducer;
}}),
"[project]/src/redux/slices/jobRequirementSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearJobRequirement": (()=>clearJobRequirement),
    "default": (()=>__TURBOPACK__default__export__),
    "jobRequirementSlice": (()=>jobRequirementSlice),
    "selectJobRequirement": (()=>selectJobRequirement),
    "setJobRequirement": (()=>setJobRequirement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
const initialState = {
    content: "",
    isGenerated: false,
    generatedAt: null
};
const jobRequirementSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "jobRequirement",
    initialState,
    reducers: {
        // Set job requirement content
        setJobRequirement: (state, action)=>{
            state.content = action.payload;
            state.isGenerated = true;
            state.generatedAt = new Date().toISOString();
        },
        // Clear job requirement data
        clearJobRequirement: (state)=>{
            state.content = "";
            state.isGenerated = false;
            state.generatedAt = null;
        }
    }
});
const { setJobRequirement, clearJobRequirement } = jobRequirementSlice.actions;
const selectJobRequirement = (state)=>state.jobRequirement;
const __TURBOPACK__default__export__ = jobRequirementSlice.reducer;
}}),
"[project]/src/redux/slices/interviewSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearInterview": (()=>clearInterview),
    "default": (()=>__TURBOPACK__default__export__),
    "interviewSlice": (()=>interviewSlice),
    "setInterviewQuestions": (()=>setInterviewQuestions),
    "setInterviewStaticInformation": (()=>setInterviewStaticInformation),
    "updateQuestionAnswer": (()=>updateQuestionAnswer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
;
;
const initialState = {
    roleSpecificQuestions: {},
    cultureSpecificQuestions: {},
    careerBasedQuestions: {
        questions: [],
        score: 0
    },
    interviewStaticInformation: {
        oneToOneInterviewInstructions: [],
        videoCallInterviewInstructions: [],
        startumDescription: []
    }
};
const interviewSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "interview",
    initialState,
    reducers: {
        setInterviewQuestions: (state, action)=>{
            // Handle role-specific questions
            if (action.payload.roleSpecificQuestions !== undefined) {
                state.roleSpecificQuestions = action.payload.roleSpecificQuestions;
            }
            // Handle culture-specific questions
            if (action.payload.cultureSpecificQuestions !== undefined) {
                state.cultureSpecificQuestions = action.payload.cultureSpecificQuestions;
            }
            // Handle career-based questions
            if (action.payload.careerBasedQuestions !== undefined) {
                state.careerBasedQuestions = action.payload.careerBasedQuestions;
            }
        },
        setInterviewStaticInformation: (state, action)=>{
            state.interviewStaticInformation = action.payload;
        },
        updateQuestionAnswer: (state, action)=>{
            const { questionType, category, questionAnswers, stratumScore, interviewerName } = action.payload;
            // Create a Map for O(1) lookups
            const answerMap = new Map(questionAnswers.map((qa)=>[
                    qa.questionId,
                    qa.answer
                ]));
            switch(questionType){
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED:
                    // Update answers
                    state.careerBasedQuestions.questions = state.careerBasedQuestions.questions.map((question)=>{
                        const answer = answerMap.get(question.id);
                        if (answer !== undefined) {
                            return {
                                ...question,
                                answer
                            };
                        }
                        return question;
                    });
                    // Update score
                    state.careerBasedQuestions.score = stratumScore;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC:
                    if (category) {
                        // Initialize category if it doesn't exist
                        if (!state.roleSpecificQuestions[category]) {
                            state.roleSpecificQuestions[category] = {
                                questions: [],
                                score: 0
                            };
                        }
                        // Update answers
                        state.roleSpecificQuestions[category].questions = state.roleSpecificQuestions[category].questions.map((question)=>{
                            const answer = answerMap.get(question.id);
                            if (answer !== undefined) {
                                return {
                                    ...question,
                                    answer
                                };
                            }
                            return question;
                        });
                        // Update score and interviewer name
                        state.roleSpecificQuestions[category].score = stratumScore;
                        state.roleSpecificQuestions[category].interviewerName = interviewerName;
                    }
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC:
                    if (category) {
                        // Initialize category if it doesn't exist
                        if (!state.cultureSpecificQuestions[category]) {
                            state.cultureSpecificQuestions[category] = {
                                questions: [],
                                score: 0
                            };
                        }
                        // Update answers
                        state.cultureSpecificQuestions[category].questions = state.cultureSpecificQuestions[category].questions.map((question)=>{
                            const answer = answerMap.get(question.id);
                            if (answer !== undefined) {
                                return {
                                    ...question,
                                    answer
                                };
                            }
                            return question;
                        });
                        // Update score and interviewer name
                        state.cultureSpecificQuestions[category].score = stratumScore;
                        state.cultureSpecificQuestions[category].interviewerName = interviewerName;
                    }
                    break;
            }
        },
        clearInterview: (state)=>{
            // Reset state to initial values
            state.roleSpecificQuestions = initialState.roleSpecificQuestions;
            state.cultureSpecificQuestions = initialState.cultureSpecificQuestions;
            state.careerBasedQuestions = initialState.careerBasedQuestions;
        }
    }
});
const { setInterviewQuestions, updateQuestionAnswer, clearInterview, setInterviewStaticInformation } = interviewSlice.actions;
const __TURBOPACK__default__export__ = interviewSlice.reducer;
}}),
"[project]/src/redux/slices/notificationSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "notificationSlice": (()=>notificationSlice),
    "setHasUnreadNotification": (()=>setHasUnreadNotification),
    "setNotificationsData": (()=>setNotificationsData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
const initialState = {
    notifications: [],
    hasUnreadNotifications: false
};
const notificationSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "notification",
    initialState,
    reducers: {
        setNotificationsData: (state, action)=>{
            state.notifications = action.payload;
        },
        setHasUnreadNotification: (state, action)=>{
            state.hasUnreadNotifications = action.payload;
        }
    }
});
const { setNotificationsData, setHasUnreadNotification } = notificationSlice.actions;
const __TURBOPACK__default__export__ = notificationSlice.reducer;
}}),
"[project]/src/redux/store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "persistor": (()=>persistor),
    "store": (()=>store)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistStore.js [app-ssr] (ecmascript) <export default as persistStore>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistReducer.js [app-ssr] (ecmascript) <export default as persistReducer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/constants.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/lib/storage/index.js [app-ssr] (ecmascript)"); // defaults to localStorage for web
// Import the reducers directly to avoid circular dependency
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobSkillsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/jobSkillsSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobDetailsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/jobDetailsSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$allSkillsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/allSkillsSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/authSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobRequirementSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/jobRequirementSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/interviewSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/notificationSlice.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
// Configure persist options for job skills slice
const jobSkillsPersistConfig = {
    key: "jobSkills",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Configure persist options for job details slice
const jobDetailsPersistConfig = {
    key: "jobDetails",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Configure persist options for all skills slice
const allSkillsPersistConfig = {
    key: "allSkills",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    blacklist: [
        "loading",
        "error"
    ]
};
// Configure persist options for auth slice
const authPersistConfig = {
    key: "auth",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Configure persist options for job requirement slice
const jobRequirementPersistConfig = {
    key: "jobRequirement",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Configure persist options for interview questions slice
const interviewPersistConfig = {
    key: "interview",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Configure persist options for notification slice
const notificationPersistConfig = {
    key: "notification",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Create persisted reducers
const persistedJobSkillsReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(jobSkillsPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobSkillsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedJobDetailsReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(jobDetailsPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobDetailsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedAllSkillsReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(allSkillsPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$allSkillsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedAuthReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(authPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedJobRequirementReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(jobRequirementPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobRequirementSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedInterviewReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(interviewPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedNotificationReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(notificationPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: {
        jobSkills: persistedJobSkillsReducer,
        jobDetails: persistedJobDetailsReducer,
        allSkills: persistedAllSkillsReducer,
        auth: persistedAuthReducer,
        jobRequirement: persistedJobRequirementReducer,
        interview: persistedInterviewReducer,
        notification: persistedNotificationReducer
    },
    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FLUSH"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REHYDRATE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PAUSE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERSIST"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PURGE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGISTER"]
                ]
            }
        })
});
const persistor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__["persistStore"])(store);
}}),
"[project]/src/redux/ReduxProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/integration/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/store.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const ReduxProvider = ({ children })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["store"],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PersistGate"], {
            persistor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persistor"],
            children: children
        }, void 0, false, {
            fileName: "[project]/src/redux/ReduxProvider.tsx",
            lineNumber: 14,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/redux/ReduxProvider.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ReduxProvider;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/utils/syncReduxToCookies.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "syncReduxStateToCookies": (()=>syncReduxStateToCookies)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-ssr] (ecmascript)");
;
;
;
const syncReduxStateToCookies = (permissions, forceSync = false)=>{
    try {
        const permissionData = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERMISSIONS_COOKIES_KEY"]);
        if (!forceSync && permissionData) {
            return;
        }
        const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["store"].getState();
        // Sync auth state to cookies (permissions are in auth state)
        if (state.auth) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERMISSIONS_COOKIES_KEY"], JSON.stringify(permissions?.length ? permissions : state.auth.permissions), {
                expires: 4,
                path: "/",
                sameSite: "strict"
            });
        }
    } catch (error) {
        console.error("Error syncing Redux state to cookies:", error);
    }
};
}}),
"[project]/public/assets/images/logo.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/logo.cf3f1dcc.svg");}}),
"[project]/public/assets/images/logo.svg.mjs { IMAGE => \"[project]/public/assets/images/logo.svg (static in ecmascript)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/images/logo.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 257,
    height: 70,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
}}),
"[project]/public/assets/images/down-arrow.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/down-arrow.88f69ff5.svg");}}),
"[project]/public/assets/images/down-arrow.svg.mjs { IMAGE => \"[project]/public/assets/images/down-arrow.svg (static in ecmascript)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/images/down-arrow.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 15,
    height: 8,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
}}),
"[project]/public/assets/images/user.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/user.8774be1d.png");}}),
"[project]/public/assets/images/user.png.mjs { IMAGE => \"[project]/public/assets/images/user.png (static in ecmascript)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/images/user.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 51,
    height: 50,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+AAICAgEzMzM5iIiHuaqckfWjl472i4qKvDU1NT4CAgICAC8vLzSXl5fQq6ai/tCpjv/HpIr/q6el/piYmNYyMjI5AH9/f6qsrKz+rqqn/82hiP/UqIz/sKuo/6ysrP6FhYWxAKWlpeaoqav/k5ah/6SIfP+wkH//kZSf/6eoq/+pqansAKSkpeWCip3/UGGI/25sfP93cn//U2KJ/4CInf+np6fsAHt8fqhlcpH+SlqC/0xbgv9HWID/SluF/2hzkf5/gIKyACUnKzNLWXrQc3KC/qWWj/+Cf4z/UmCG/lZhfdYsLS86AAEBAgEWGyc6P0ZduZ2BcPWQfHH2PUdhvRkdJz4BAQICbTiKQnEKIQsAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 8
};
}}),
"[project]/src/styles/header.module.scss.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "header-module-scss-module__4lyeFq__active",
  "admin_info": "header-module-scss-module__4lyeFq__admin_info",
  "align_header_search": "header-module-scss-module__4lyeFq__align_header_search",
  "circle_img": "header-module-scss-module__4lyeFq__circle_img",
  "dropdown_menu": "header-module-scss-module__4lyeFq__dropdown_menu",
  "header": "header-module-scss-module__4lyeFq__header",
  "header_buttons": "header-module-scss-module__4lyeFq__header_buttons",
  "header_right": "header-module-scss-module__4lyeFq__header_right",
  "hidden": "header-module-scss-module__4lyeFq__hidden",
  "logo": "header-module-scss-module__4lyeFq__logo",
  "navbar_content": "header-module-scss-module__4lyeFq__navbar_content",
  "navbar_links": "header-module-scss-module__4lyeFq__navbar_links",
  "open": "header-module-scss-module__4lyeFq__open",
  "searchBar": "header-module-scss-module__4lyeFq__searchBar",
  "searchButton": "header-module-scss-module__4lyeFq__searchButton",
  "searchContainer": "header-module-scss-module__4lyeFq__searchContainer",
  "searchIcon": "header-module-scss-module__4lyeFq__searchIcon",
  "searchInput": "header-module-scss-module__4lyeFq__searchInput",
  "search_wrapper": "header-module-scss-module__4lyeFq__search_wrapper",
  "show": "header-module-scss-module__4lyeFq__show",
  "sidebar_sub_menu": "header-module-scss-module__4lyeFq__sidebar_sub_menu",
  "sub_menu_list": "header-module-scss-module__4lyeFq__sub_menu_list",
  "sub_menubar": "header-module-scss-module__4lyeFq__sub_menubar",
  "user_drop": "header-module-scss-module__4lyeFq__user_drop",
  "user_drop_btn": "header-module-scss-module__4lyeFq__user_drop_btn",
});
}}),
"[project]/src/components/svgComponents/Notification.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function NotificationIcon(props) {
    const { hasNotification, ...restProps } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        ...restProps,
        className: "cursor-pointer",
        xmlns: "http://www.w3.org/2000/svg",
        width: "25",
        height: "24",
        viewBox: "0 0 33 32",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M27.458 22.9624C26.251 20.8511 25.6133 18.4492 25.6133 16.0166C25.6133 16.0166 25.6133 14.0014 25.6133 14C25.6133 10.1198 22.9443 6.54149 19.2454 5.40924C19.4725 4.98712 19.6133 4.51202 19.6133 4C19.6133 2.3457 18.2676 1 16.6133 1C14.959 1 13.6133 2.3457 13.6133 4C13.6133 4.51233 13.7544 4.98767 13.9817 5.40997C10.2878 6.57581 7.61332 10.1457 7.61332 14.3071V16.0166C7.61332 18.4492 6.97562 20.8511 5.76811 22.9629C5.1221 24.0927 4.75006 25.2737 5.46489 26.5054C6.00736 27.4414 6.97758 28 8.05961 28H12.6133C12.6133 30.2056 14.4077 32 16.6133 32C18.8189 32 20.6133 30.2056 20.6133 28H25.167C26.249 28 27.2193 27.4414 27.7617 26.5054C28.4522 25.3141 28.0953 24.0784 27.458 22.9624ZM16.6133 3C17.1646 3 17.6133 3.44873 17.6133 4C17.6133 4.55127 17.1646 5 16.6133 5C16.062 5 15.6133 4.55127 15.6133 4C15.6133 3.44873 16.062 3 16.6133 3ZM16.6133 30C15.5103 30 14.6133 29.103 14.6133 28H18.6133C18.6133 29.103 17.7163 30 16.6133 30ZM26.0323 25.5019C25.9453 25.6514 25.687 26 25.167 26H8.05961C7.53967 26 7.28136 25.6515 7.19441 25.502C6.87823 24.9586 7.23496 24.428 7.50492 23.9546C8.88432 21.542 9.61332 18.7969 9.61332 16.0166C9.61332 16.0166 9.61332 14.3081 9.61332 14.3071C9.61332 10.5303 12.7077 7.00054 16.602 7.00049C20.3752 7.00044 23.6133 10.2392 23.6133 14V16.0166C23.6133 18.7968 24.3423 21.5419 25.7212 23.954C26.0017 24.4448 26.3567 24.9391 26.0323 25.5019Z",
                fill: "#333333"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/Notification.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this),
            hasNotification && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "24.6136",
                cy: "10.6654",
                r: "4.83333",
                fill: "#D4000D",
                stroke: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/Notification.tsx",
                lineNumber: 16,
                columnNumber: 27
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/Notification.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = NotificationIcon;
}}),
"[project]/src/utils/storage.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$secure$2d$ls$2f$dist$2f$secure$2d$ls$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/secure-ls/dist/secure-ls.js [app-ssr] (ecmascript)");
;
const ls = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$secure$2d$ls$2f$dist$2f$secure$2d$ls$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
const storage = {
    set: (key, data)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    get: (key)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return null;
    },
    remove: (key)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    removeAll: ()=>{
        ls.removeAll();
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    getAllKeys: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return [];
    }
};
const __TURBOPACK__default__export__ = storage;
}}),
"[project]/src/constants/endpoint.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// import config from "@/config/config";
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const URL = ("TURBOPACK compile-time value", "http://localhost:3001/api/v1");
const endpoint = {
    auth: {
        SIGNIN: `${URL}/auth/sign-in`,
        VERIFY_OTP: `${URL}/auth/verify-otp`,
        RESEND_OTP: `${URL}/auth/resend-otp`,
        FORGOT_PASSWORD: `${URL}/auth/forgot-password`,
        RESET_PASSWORD: `${URL}/auth/reset-password`,
        DELETE_SESSION: `${URL}/auth/delete-session`,
        UPDATE_TIMEZONE: `${URL}/auth/update-timezone`
    },
    interview: {
        UPDATE_OR_SCHEDULE_INTERVIEW: `${URL}/interview/update-or-schedule-interview`,
        GET_INTERVIEWS: `${URL}/interview/get-interviews`,
        GET_INTERVIEWERS: `${URL}/interview/get-interviewers`,
        GET_MY_INTERVIEWS: `${URL}/interview/get-my-interviews`,
        UPDATE_INTERVIEW_ANSWERS: `${URL}/interview/update-interview-answers`,
        GET_UPCOMING_OR_PAST_INTERVIEW: `${URL}/interview/get-upcoming-or-past-interviews`,
        GET_INTERVIEW_SKILL_QUESTIONS: `${URL}/interview/get-interview-skill-questions`,
        UPDATE_INTERVIEW_SKILL_QUESTION: `${URL}/interview/update-interview-skill-question`,
        ADD_INTERVIEW_SKILL_QUESTION: `${URL}/interview/add-interview-skill-question`,
        GET_COMPLETED_SKILLS: `${URL}/interview/get-completed-skills`,
        GET_JOB_LIST: `${URL}/interview/get-job-list`,
        GET_CANDIDATE_LIST: `${URL}/interview/get-candidate-list`,
        END_INTERVIEW: `${URL}/interview/end-interview`,
        CONDUCT_INTERVIEW_STATIC_INFORMATION: `${URL}/interview/conduct-interview-static-information`
    },
    common: {
        REMOVE_ATTACHMENTS_FROM_S3: `${URL}/remove-attachments-from-s3`,
        GENERATE_PRESIGNED_URL: `${URL}/generate-presignedurl`
    },
    jobRequirements: {
        GENERATE_SKILL: `${URL}/jobs/generate-skills`,
        UPLOAD_URL: `${URL}/jobs/upload-url`,
        PARSE_PDF: `${URL}/jobs/parse-pdf`,
        GET_ALL_SKILLS: `${URL}/jobs/get-all-skills`,
        GENERATE_JOB_REQUIREMENT: `${URL}/jobs/generate-job-requirement`,
        SAVE_JOB_DETAILS: `${URL}/jobs/save-job-details`,
        GET_JOBS_META: `${URL}/jobs/get-jobs-meta`,
        UPDATE_JOB_STATUS: "/jobs/updateJob",
        GET_JOB_HTML_DESCRIPTION: `${URL}/jobs/get-job-html-description`,
        UPDATE_JOB_DESCRIPTION: `${URL}/jobs/update-job-description`,
        GENERATE_PDF: `${URL}/jobs/generate-pdf`
    },
    Dashboard: {
        GET_DASHBOARD_COUNTS: `${URL}/jobs/dashboard-counts`
    },
    resumeScreen: {
        MANUAL_CANDIDATE_UPLOAD: `${URL}/resume-screen/manual-candidate-upload`,
        GET_PRESIGNED_URL: `${URL}/resume-screen/get-presigned-url`,
        GET_ALL_PENDING_JOB_APPLICATIONS: `${URL}/resume-screen/get-all-pending-job-applications`,
        CHANGE_APPLICATION_STATUS: `${URL}/resume-screen/change-application-status`
    },
    employee: {
        ADD_EMPLOYEES: `${URL}/employee-management/add-hiring-employee`,
        GET_EMPLOYEES: `${URL}/employee-management`,
        GET_EMPLOYEES_BY_DEPARTMENT: `${URL}/employee-management/employees`,
        UPDATE_EMPLOYEE_ROLE: `${URL}/employee-management/employee/:employeeId/role`,
        UPDATE_EMPLOYEE_STATUS: `${URL}/employee-management/employee/change-status/:employeeId`,
        // this task is for future use
        // DELETE_EMPLOYEE: `${URL}/employee-management/employee/:employeeId`, // original
        DELETE_EMPLOYEE: `${URL}/employee-management/dummy`,
        UPDATE_EMPLOYEE_INTERVIEW_ORDER: `${URL}/employee-management/employee/:employeeId/interview-order`
    },
    userprofile: {
        GET_MY_PROFILE: `${URL}/user-profile/get-my-profile`,
        UPDATE_MY_PROFILE: `${URL}/user-profile/update-my-profile`
    },
    roles: {
        GET_ROLES_WITH_PAGINATION: `${URL}/access-management/user-roles-pagination`,
        GET_ROLES: `${URL}/access-management/user-roles`,
        ADD_USER_ROLE: `${URL}/access-management/add-user-role`,
        UPDATE_USER_ROLE: `${URL}/access-management/user-role`,
        DELETE_USER_ROLE: `${URL}/access-management/user-role`,
        GET_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions`,
        GET_ROLE_PERMISSIONS_BY_ID: `${URL}/access-management/role-permissions/:roleId`,
        UPDATE_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions/:roleId`,
        USER_PERMISSIONS: `${URL}/access-management/user-permissions`
    },
    notification: {
        UPDATE_NOTIFICATION: `${URL}/notifications/mark-as-watched`,
        GET_NOTIFICATIONS: `${URL}/notifications/get-notifications`,
        DELETE_ALL_NOTIFICATIONS: `${URL}/notifications/delete-users-all-notifications`,
        GET_UNREAD_NOTIFICATIONS_COUNT: `${URL}/notifications/get-unread-notifications-count`
    },
    departments: {
        GET_DEPARTMENTS: `${URL}/employee-management/departments`,
        ADD_DEPARTMENT: `${URL}/employee-management/add-department`,
        UPDATE_DEPARTMENT: `${URL}/employee-management/update-department/:departmentId`,
        DELETE_DEPARTMENT: `${URL}/employee-management/delete-department/:departmentId`
    },
    assessment: {
        CREATE_FINAL_ASSESSMENT: `${URL}/final-assessment/create-final-assessment`,
        GET_FINAL_ASSESSMENT_QUESTIONS: `${URL}/final-assessment/assessment/questions`,
        CREATE_ASSESSMENT_QUESTION: `${URL}/final-assessment/assessment/create-question`,
        SUBMIT_CANDIDATE_ANSWERS: `${URL}/final-assessment/candidate/:candidateId/submit`,
        SHARE_ASSESSMENT: `${URL}/final-assessment/assessment/share`,
        GET_FINAL_ASSESSMENT_BY_CANDIDATE: `${URL}/final-assessment/candidate/assessment`,
        SUBMIT_ASSESSMENT: `${URL}/final-assessment/candidate/assessment/submit`,
        GET_ASSESSMENT_STATUS: `${URL}/final-assessment/assessment-status`,
        VERIFY_CANDIDATE_EMAIL: `${URL}/final-assessment/candidate/verify-email`,
        GENERATE_ASSESSMENT_TOKEN: `${URL}/final-assessment/assessment/generate-token`
    },
    candidatesApplication: {
        ADDITIONAL_INFO: `${URL}/candidates/add-applicant-additional-info`,
        PROMOTE_DEMOTE_CANDIDATE: `${URL}/candidates/update-candidate-rank-status`,
        GET_TOP_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/top-candidates`,
        GET_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/get-candidates`,
        ARCHIVE_ACTIVE_APPLICATION: `${URL}/candidates/archive-active-application/:applicationId`,
        GET_CANDIDATE_DETAILS: `${URL}/candidates/get-candidate-details`,
        UPDATE_JOB_APPLICATION_STATUS: `${URL}/candidates/update-job-application-status/:jobApplicationId`,
        GET_CANDIDATE_INTERVIEW_HISTORY: `${URL}/candidates/get-candidate-interview-history/:applicationId`,
        GET_APPLICATION_FINAL_SUMMARY: `${URL}/candidates/application-final-summary/:jobApplicationId`,
        GET_APPLICATION_SKILL_SCORE_DATA: `${URL}/candidates/application-skill-score-data/:jobApplicationId`,
        GENERATE_FINAL_SUMMARY: `${URL}/candidates/generate-final-summary`
    },
    subscription: {
        GET_ALL_PLANS: `${URL}/subscription/all`,
        GET_CURRENT_SUBSCRIPTION: `${URL}/subscription/current`,
        CANCEL_SUBSCRIPTION: `${URL}/subscription/cancel`,
        GET_TRANSACTIONS: `${URL}/subscription/transactions`,
        BUY_SUBSCRIPTION: `${URL}/subscription/buy-subscription`
    }
};
const __TURBOPACK__default__export__ = endpoint;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/config/config.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const config = {
    env: ("TURBOPACK compile-time value", "development"),
    apiBaseUrl: ("TURBOPACK compile-time value", "http://localhost:3001/api/v1")
};
const __TURBOPACK__default__export__ = config;
}}),
"[project]/src/utils/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isObject": (()=>isObject),
    "withData": (()=>withData),
    "withError": (()=>withError)
});
const { toString } = Object.prototype;
const isObject = (arg)=>toString.call(arg) === "[object Object]";
const withError = (arg)=>{
    if (isObject(arg)) {
        return {
            data: null,
            error: {
                ...arg
            }
        };
    }
    return {
        data: null,
        error: {
            message: arg
        }
    };
};
const withData = (data)=>({
        error: null,
        data
    });
}}),
"[project]/src/utils/helper.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearStorage": (()=>clearStorage),
    "dismissAllToasts": (()=>dismissAllToasts),
    "formatDate": (()=>formatDate),
    "formatTimeForInput": (()=>formatTimeForInput),
    "getAccessToken": (()=>getAccessToken),
    "logout": (()=>logout),
    "normalizeSpaces": (()=>normalizeSpaces),
    "pushFileToS3": (()=>pushFileToS3),
    "setAccessToken": (()=>setAccessToken),
    "toTitleCase": (()=>toTitleCase),
    "toastMessageError": (()=>toastMessageError),
    "toastMessageSuccess": (()=>toastMessageSuccess),
    "toastMessageWithIcon": (()=>toastMessageWithIcon),
    "uploadFileOnS3": (()=>uploadFileOnS3)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/storage.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/authServices.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$commonService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/commonService.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const getAccessToken = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ACCESS_TOKEN_KEY"]);
};
const clearStorage = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].removeAll();
};
const setAccessToken = (accessToken)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ACCESS_TOKEN_KEY"], accessToken);
};
/**
 * Toast style object
 */ const style = {
    fontSize: "16px"
};
const toastMessageSuccess = (message)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success(message, {
        style
    });
};
const toastMessageWithIcon = (message, icon)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success(message, {
        style,
        icon
    });
};
const toastMessageError = (message)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error(message, {
        style
    });
};
const dismissAllToasts = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dismiss();
};
const logout = async (userId)=>{
    try {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteSession"])(userId);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["signOut"])({
            redirect: false
        });
        clearStorage();
        // Delete permissions_data cookies when user logs out
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERMISSIONS_COOKIES_KEY"], {
            path: "/"
        });
    } catch (error) {
        console.error("Error in logout:", error);
    }
};
const uploadFileOnS3 = async (file, filePath)=>{
    let body = {
        filePath: "",
        fileFormat: ""
    };
    body = {
        filePath,
        fileFormat: file.type
    };
    let signedUrl;
    const presignedUrl = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$commonService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSignedUrl"])(body);
    if (presignedUrl && presignedUrl.data) {
        const response = await pushFileToS3(presignedUrl.data.data, file);
        if (response?.url) {
            signedUrl = response?.url.split("?")?.[0];
        }
    }
    return signedUrl?.replace(`${("TURBOPACK compile-time value", "https://s9-interview-assets.s3.us-east-1.amazonaws.com/")}`, `${("TURBOPACK compile-time value", "https://dxxd0n8h8rh9s.cloudfront.net/")}`);
};
const pushFileToS3 = async (signedUrl, file)=>{
    return fetch(signedUrl, {
        method: "PUT",
        body: file,
        headers: {
            "Content-Type": file.type
        }
    });
};
const formatDate = (dateString)=>{
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
    });
};
const formatTimeForInput = (date)=>{
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
};
const toTitleCase = (name)=>{
    if (!name) return "";
    return name.toLowerCase().split(" ").filter((word)=>word) // remove extra spaces
    .map((word)=>word[0].toUpperCase() + word.slice(1)).join(" ");
};
const normalizeSpaces = (text)=>{
    return text.trim().replace(/\s+/g, " ");
};
}}),
"[project]/src/utils/http.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// src/utils/http.ts
__turbopack_context__.s({
    "get": (()=>get),
    "http": (()=>http),
    "patch": (()=>patch),
    "post": (()=>post),
    "postFile": (()=>postFile),
    "put": (()=>put),
    "remove": (()=>remove)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
const http = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].apiBaseUrl,
    headers: {
        "Content-Type": "application/json"
    }
});
http.interceptors.request.use(async (req)=>{
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSession"])();
    const accessToken = session?.user?.data?.token;
    if (accessToken) {
        req.headers.authorization = `Bearer ${accessToken}`;
    }
    req.headers["ngrok-skip-browser-warning"] = "fjdlkghjsk";
    return req;
});
// Flag to prevent multiple logout calls
let isLoggingOut = false;
http.interceptors.response.use((res)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withData"])(res.data), async (err)=>{
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSession"])();
    const userId = session?.user?.data?.authData?.userData?.id;
    const accessToken = session?.user?.data?.token;
    if (err?.response?.status === 401 && !isLoggingOut && accessToken) {
        isLoggingOut = true;
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logout"])(userId);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TOKEN_EXPIRED"]);
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
        } catch (error) {
            console.error("Session cleanup error:", error);
        } finally{
            isLoggingOut = false;
        }
    } else if (err?.response?.status === 403) {
        // Show toast message for forbidden access (403)
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(err?.response?.data?.message);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withError"])(err?.response?.data?.error);
});
function get(url, params) {
    return http({
        method: "get",
        url,
        params
    });
}
function post(url, data, params) {
    return http({
        method: "post",
        url,
        data,
        params
    });
}
function postFile(url, data, params) {
    return http({
        method: "post",
        url,
        data,
        params,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}
function put(url, data, params) {
    return http({
        method: "put",
        url,
        data,
        params
    });
}
function patch(url, data, params) {
    return http({
        method: "patch",
        url,
        data,
        params
    });
}
function remove(url, params) {
    return http({
        method: "delete",
        url,
        params
    });
}
}}),
"[project]/src/services/authServices.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteSession": (()=>deleteSession),
    "forgotPassword": (()=>forgotPassword),
    "getUserPermissions": (()=>getUserPermissions),
    "logIn": (()=>logIn),
    "resendOTP": (()=>resendOTP),
    "resetPassword": (()=>resetPassword),
    "updateTimezone": (()=>updateTimezone),
    "verifyOTP": (()=>verifyOTP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-ssr] (ecmascript)");
;
;
const logIn = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.SIGNIN, data);
};
const verifyOTP = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.VERIFY_OTP, data);
};
const resendOTP = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.RESEND_OTP, data);
};
const forgotPassword = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.FORGOT_PASSWORD, data);
};
const resetPassword = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.RESET_PASSWORD, data);
};
const deleteSession = (userId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["remove"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.DELETE_SESSION}/${userId}`);
};
const updateTimezone = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.UPDATE_TIMEZONE, data);
};
const getUserPermissions = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.USER_PERMISSIONS);
};
}}),
"[project]/src/services/commonService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSignedUrl": (()=>getSignedUrl),
    "removeAttachmentsFromS3": (()=>removeAttachmentsFromS3)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-ssr] (ecmascript)");
;
;
const removeAttachmentsFromS3 = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].common.REMOVE_ATTACHMENTS_FROM_S3, data);
};
const getSignedUrl = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].common.GENERATE_PRESIGNED_URL, data);
};
}}),
"[project]/src/components/svgComponents/dataSecurityIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function dataSecurityIcon() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "30",
        height: "30",
        viewBox: "0 0 43 42",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "21.5",
                cy: "21",
                r: "21",
                fill: "url(#paint0_linear_9593_1613)"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M21.5091 9.28711H21.4886C18.2484 9.93022 15.0058 10.5721 11.7656 11.2139C11.7927 14.3154 11.8183 17.4181 11.8451 20.5211C11.8835 25.2709 14.6681 29.5713 18.9852 31.5523C19.8209 31.9347 20.6541 32.3187 21.4886 32.7014V32.7102C21.4924 32.709 21.4961 32.7064 21.499 32.7052C21.5015 32.7064 21.5053 32.709 21.5094 32.7102V32.7014C22.3439 32.3187 23.1771 31.935 24.0128 31.5523C28.3298 29.5716 31.1144 25.2709 31.1529 20.5211C31.1797 17.4184 31.2055 14.3154 31.2323 11.2139C27.9919 10.5721 24.7492 9.93022 21.5091 9.28711ZM29.4181 20.6065C29.3856 24.503 27.1019 28.0303 23.5604 29.6558C22.8757 29.9694 22.1919 30.2841 21.5072 30.5974V30.605C21.5043 30.604 21.5015 30.6021 21.4987 30.6012C21.4968 30.6021 21.4939 30.604 21.4911 30.605V30.5974C20.8064 30.2837 20.1226 29.9691 19.4379 29.6558C15.8964 28.0303 13.6127 24.503 13.5802 20.6065C13.5581 18.0621 13.537 15.5171 13.515 12.9727C16.1735 12.4462 18.8326 11.9198 21.4911 11.3924H21.5075C24.166 11.9198 26.8251 12.4462 29.4837 12.9727C29.4616 15.5171 29.4405 18.0621 29.4184 20.6065H29.4181Z",
                fill: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                lineNumber: 7,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M25.0804 18.5382H24.734V17.2775C24.734 15.5752 23.3538 14.1953 21.6518 14.1953H21.3743C19.672 14.1953 18.2921 15.5752 18.2921 17.2775V18.5382H17.9457C17.6619 18.5382 17.4321 18.768 17.4321 19.0517V24.4369C17.4321 24.7206 17.6619 24.9517 17.9457 24.9517H25.0807C25.3645 24.9517 25.5955 24.7206 25.5955 24.4369V19.0517C25.5955 18.768 25.3645 18.5382 25.0807 18.5382H25.0804ZM21.7965 21.7077V23.3868C21.7965 23.4804 21.7195 23.5576 21.6243 23.5576H21.3996C21.3059 23.5576 21.2287 23.4807 21.2287 23.3868V21.7077C20.9525 21.5961 20.7561 21.3253 20.7561 21.0069C20.7561 20.5949 21.0875 20.2598 21.4982 20.2535C21.5033 20.2522 21.5074 20.2522 21.5124 20.2522C21.9282 20.2522 22.2671 20.5899 22.2671 21.0069C22.2671 21.3253 22.072 21.5961 21.7961 21.7077H21.7965ZM23.7554 18.5382H19.2136V17.1672C19.2136 15.967 20.1855 14.9938 21.3869 14.9938H21.5808C22.7822 14.9938 23.7554 15.967 23.7554 17.1672V18.5382Z",
                fill: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                    id: "paint0_linear_9593_1613",
                    x1: "-2.3",
                    y1: "17.5",
                    x2: "29.5828",
                    y2: "-6.01022",
                    gradientUnits: "userSpaceOnUse",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            stopColor: "#74A8FF"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                            lineNumber: 17,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            offset: "0.474301",
                            stopColor: "#AACAFF"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                            lineNumber: 18,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            offset: "1",
                            stopColor: "#5D86CC"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                            lineNumber: 19,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                    lineNumber: 16,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                lineNumber: 15,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = dataSecurityIcon;
}}),
"[project]/src/constants/routes.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BEFORE_LOGIN_ROUTES": (()=>BEFORE_LOGIN_ROUTES),
    "UNRESTRICTED_ROUTES": (()=>UNRESTRICTED_ROUTES),
    "default": (()=>__TURBOPACK__default__export__)
});
const ROUTES = {
    LOGIN: "/login",
    FORGOT_PASSWORD: "/forgot-password",
    VERIFY: "/verify",
    RESET_PASSWORD: "/reset-password",
    CANDIDATE_ASSESSMENT: "/candidate-assessment",
    DASHBOARD: "/dashboard",
    HOME: "/",
    BUY_SUBSCRIPTION: "/buy-subscription",
    PROFILE: {
        MY_PROFILE: "/my-profile"
    },
    SUBSCRIPTIONS: {
        SUCCESS: "/subscriptions/success",
        CANCEL: "/subscriptions/cancel"
    },
    JOBS: {
        CAREER_BASED_SKILLS: "/career-based-skills",
        ROLE_BASED_SKILLS: "/role-based-skills",
        CULTURE_BASED_SKILLS: "/culture-based-skills",
        GENERATE_JOB: "/generate-job",
        EDIT_SKILLS: "/edit-skills",
        HIRING_TYPE: "/hiring-type",
        JOB_EDITOR: "/job-editor",
        ACTIVE_JOBS: "/active-jobs",
        CANDIDATE_PROFILE: "/candidate-profile",
        ARCHIVE: "/archive"
    },
    SCREEN_RESUME: {
        MANUAL_CANDIDATE_UPLOAD: "/manual-upload-resume",
        CANDIDATE_QUALIFICATION: "/candidate-qualification",
        CANDIDATE_LIST: "/candidates-list",
        CANDIDATES: "/candidates"
    },
    INTERVIEW: {
        ADD_CANDIDATE_INFO: "/additional-submission",
        SCHEDULE_INTERVIEW: "/schedule-interview",
        PRE_INTERVIEW_QUESTIONS_OVERVIEW: "/pre-interview-questions-overview",
        INTERVIEW_QUESTION: "/interview-question",
        CALENDAR: "/calendar",
        INTERVIEW_SUMMARY: "/interview-summary"
    },
    ROLE_EMPLOYEES: {
        ROLES_PERMISSIONS: "/roles-permissions",
        EMPLOYEE_MANAGEMENT: "/employee-management",
        EMPLOYEE_MANAGEMENT_DETAIL: "/employee-management-detail",
        ADD_EMPLOYEE: "/add-employees",
        ADD_DEPARTMENT: "/add-department"
    },
    FINAL_ASSESSMENT: {
        FINAL_ASSESSMENT: "/final-assessment"
    }
};
const BEFORE_LOGIN_ROUTES = [
    ROUTES.LOGIN,
    ROUTES.FORGOT_PASSWORD,
    ROUTES.VERIFY,
    ROUTES.RESET_PASSWORD,
    ROUTES.CANDIDATE_ASSESSMENT
];
const UNRESTRICTED_ROUTES = [
    ROUTES.SUBSCRIPTIONS.SUCCESS,
    ROUTES.SUBSCRIPTIONS.CANCEL
];
const __TURBOPACK__default__export__ = ROUTES;
}}),
"[project]/src/components/svgComponents/ProfileIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
"use client";
;
const ProfileIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        xmlnsXlink: "http://www.w3.org/1999/xlink",
        version: "1.1",
        id: "Layer_1",
        x: "0px",
        y: "0px",
        viewBox: "0 0 512 512",
        xmlSpace: "preserve",
        style: {
            minWidth: "20px"
        },
        width: "18",
        height: "18",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M256,0c-74.439,0-135,60.561-135,135s60.561,135,135,135s135-60.561,135-135S330.439,0,256,0z M256,240 c-57.897,0-105-47.103-105-105c0-57.897,47.103-105,105-105c57.897,0,105,47.103,105,105C361,192.897,313.897,240,256,240z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/ProfileIcon.tsx",
                        lineNumber: 21,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/ProfileIcon.tsx",
                    lineNumber: 20,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ProfileIcon.tsx",
                lineNumber: 19,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M423.966,358.195C387.006,320.667,338.009,300,286,300h-60c-52.008,0-101.006,20.667-137.966,58.195 C51.255,395.539,31,444.833,31,497c0,8.284,6.716,15,15,15h420c8.284,0,15-6.716,15-15    C481,444.833,460.745,395.539,423.966,358.195z M61.66,482c7.515-85.086,78.351-152,164.34-152h60    c85.989,0,156.825,66.914,164.34,152H61.66z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/ProfileIcon.tsx",
                        lineNumber: 26,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/ProfileIcon.tsx",
                    lineNumber: 25,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ProfileIcon.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/ProfileIcon.tsx",
        lineNumber: 6,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ProfileIcon;
}}),
"[project]/src/components/svgComponents/LogoutIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function LogoutIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        style: {
            minWidth: "20px",
            fill: "#fff"
        },
        width: "20",
        height: "20",
        viewBox: "0 0 32 33",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                clipPath: "url(#clip0_10037_3401)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M18.6667 11.2415V8.57487C18.6667 7.86763 18.3857 7.18935 17.8856 6.68925C17.3855 6.18915 16.7072 5.9082 16 5.9082H6.66667C5.95942 5.9082 5.28115 6.18915 4.78105 6.68925C4.28095 7.18935 4 7.86763 4 8.57487V24.5749C4 25.2821 4.28095 25.9604 4.78105 26.4605C5.28115 26.9606 5.95942 27.2415 6.66667 27.2415H16C16.7072 27.2415 17.3855 26.9606 17.8856 26.4605C18.3857 25.9604 18.6667 25.2821 18.6667 24.5749V21.9082",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/LogoutIcon.tsx",
                        lineNumber: 15,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M12 16.5762H28L24 12.5762",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/LogoutIcon.tsx",
                        lineNumber: 21,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M24 20.5762L28 16.5762",
                        strokeWidth: "1.5",
                        strokeLinecap: "round",
                        strokeLinejoin: "round"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/LogoutIcon.tsx",
                        lineNumber: 22,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/svgComponents/LogoutIcon.tsx",
                lineNumber: 14,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                    id: "clip0_10037_3401",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        width: "32",
                        height: "32",
                        fill: "white",
                        transform: "translate(0 0.271484)"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/LogoutIcon.tsx",
                        lineNumber: 26,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/LogoutIcon.tsx",
                    lineNumber: 25,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/LogoutIcon.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/LogoutIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = LogoutIcon;
}}),
"[project]/src/components/loader/Loader.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
"use client";
;
;
const Loader = ({ className })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `spinner-border ${className}`,
        role: "status",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "visually-hidden",
            children: t("loading")
        }, void 0, false, {
            fileName: "[project]/src/components/loader/Loader.tsx",
            lineNumber: 9,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/loader/Loader.tsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Loader;
}}),
"[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/loader/Loader.tsx [app-ssr] (ecmascript)");
;
;
const Button = ({ className, type, disabled = false, onClick, children, loading })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        type: type,
        className: `theme-btn ${className}`,
        onClick: (e)=>{
            if (onClick) {
                onClick(e);
            }
        },
        disabled: disabled,
        "aria-label": "",
        children: [
            children,
            loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/formElements/Button.tsx",
                lineNumber: 24,
                columnNumber: 16
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/Button.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this);
const __TURBOPACK__default__export__ = Button;
}}),
"[project]/src/services/notificationServices/notificationService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteAllNotifications": (()=>deleteAllNotifications),
    "getNotifications": (()=>getNotifications),
    "getUnreadNotificationsCount": (()=>getUnreadNotificationsCount),
    "updateNotificationStatus": (()=>updateNotificationStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-ssr] (ecmascript)");
;
;
const getNotifications = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].notification.GET_NOTIFICATIONS, data);
};
const deleteAllNotifications = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["remove"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].notification.DELETE_ALL_NOTIFICATIONS);
};
const updateNotificationStatus = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].notification.UPDATE_NOTIFICATION, {});
};
const getUnreadNotificationsCount = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].notification.GET_UNREAD_NOTIFICATIONS_COUNT);
};
}}),
"[project]/src/components/views/notification/Notifications.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/* eslint-disable react-hooks/exhaustive-deps */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/notificationSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationServices$2f$notificationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/notificationServices/notificationService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$infinite$2d$scroll$2d$component$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-infinite-scroll-component/dist/index.es.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const Notifications = ({ setIsNotificationOpen })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("common");
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [offset, setOffset] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [hasMore, setHasMore] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const notificationRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDispatch"])();
    const { notifications } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])((state)=>state.notification);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            if (notificationRef.current && !notificationRef.current.contains(event.target) && event.target.id !== "notification-icon-id") {
                setIsNotificationOpen(false);
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setHasUnreadNotification"])(false));
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setNotificationsData"])(notifications.map((item)=>({
                        ...item,
                        isWatched: 1
                    }))));
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationServices$2f$notificationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateNotificationStatus"])();
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return ()=>{
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setNotificationsData"])([]));
        fetchNotifications(offset);
    }, []);
    const fetchNotifications = async (offset)=>{
        try {
            setLoading(true);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationServices$2f$notificationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNotifications"])({
                offset,
                limit: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"]
            });
            if (response.data?.success) {
                console.log("Notifications fetched successfully:", response.data.data);
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setNotificationsData"])([
                    ...notifications,
                    ...response.data.data
                ]));
                setOffset((prevOffset)=>prevOffset + __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"]);
                setHasMore(response.data.data.length === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"]);
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message));
            }
        } catch (error) {
            console.log(error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
        } finally{
            setLoading(false);
        }
    };
    const handleClearAll = async ()=>{
        try {
            setLoading(true);
            const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationServices$2f$notificationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteAllNotifications"])();
            if (res.data?.success) {
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setNotificationsData"])([])); // Clear UI
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(t("failed_to_delete_notifications"));
            }
        } catch  {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
        } finally{
            setLoading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "notifications",
        ref: notificationRef,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "header-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        children: tCommon("notifications")
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/notification/Notifications.tsx",
                        lineNumber: 97,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        onClick: notifications.length > 0 ? handleClearAll : undefined,
                        className: "clear-btn p-0",
                        children: tCommon("clear_all")
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/notification/Notifications.tsx",
                        lineNumber: 98,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/views/notification/Notifications.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "notification-wrapper",
                id: "notification-scroll-container",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$infinite$2d$scroll$2d$component$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    dataLength: notifications.length,
                    next: ()=>fetchNotifications(offset),
                    hasMore: hasMore,
                    loader: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            ...Array(4)
                        ].map((_, rowIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "notification-item",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        style: {
                                            margin: 0
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            height: 17,
                                            width: "100%",
                                            borderRadius: 4
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                            lineNumber: 113,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                        lineNumber: 112,
                                        columnNumber: 19
                                    }, void 0),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            height: 14,
                                            width: "80%",
                                            borderRadius: 4
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                            lineNumber: 116,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                        lineNumber: 115,
                                        columnNumber: 19
                                    }, void 0),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "time",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            height: 10,
                                            width: "20%",
                                            borderRadius: 4
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                            lineNumber: 119,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                        lineNumber: 118,
                                        columnNumber: 19
                                    }, void 0)
                                ]
                            }, rowIndex, true, {
                                fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                lineNumber: 111,
                                columnNumber: 17
                            }, void 0))
                    }, void 0, false),
                    scrollableTarget: "notification-scroll-container",
                    endMessage: notifications.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "p-2 text-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                            children: tCommon("no_more_notifications")
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/notification/Notifications.tsx",
                            lineNumber: 129,
                            columnNumber: 17
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/notification/Notifications.tsx",
                        lineNumber: 128,
                        columnNumber: 15
                    }, void 0),
                    children: notifications.length === 0 && !loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "p-2 text-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                            children: [
                                " ",
                                tCommon("no_notifications_found")
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/notification/Notifications.tsx",
                            lineNumber: 136,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/notification/Notifications.tsx",
                        lineNumber: 135,
                        columnNumber: 13
                    }, this) : notifications.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `notification-item ${item.isWatched === 0 ? "unread" : ""}`,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    style: {
                                        margin: 0
                                    },
                                    children: item.title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                    lineNumber: 141,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: item.description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                    lineNumber: 142,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "time",
                                    children: new Date(item.createdTs).toLocaleString("en-IN", {
                                        dateStyle: "medium",
                                        timeStyle: "short"
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/notification/Notifications.tsx",
                                    lineNumber: 143,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, item.id, true, {
                            fileName: "[project]/src/components/views/notification/Notifications.tsx",
                            lineNumber: 140,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/views/notification/Notifications.tsx",
                    lineNumber: 104,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/views/notification/Notifications.tsx",
                lineNumber: 103,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/views/notification/Notifications.tsx",
        lineNumber: 95,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].memo(Notifications);
}}),
"[project]/src/components/svgComponents/NavCalendarIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const NavCalendarIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 25 27",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                style: {
                    width: "24px",
                    height: "22px"
                },
                d: "M23.2117 11.406H1.1439C0.631332 11.406 0.215332 10.99 0.215332 10.4774C0.215332 9.96483 0.631332 9.54883 1.1439 9.54883H23.2117C23.7243 9.54883 24.1403 9.96483 24.1403 10.4774C24.1403 10.99 23.7243 11.406 23.2117 11.406Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 4,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M17.6835 16.24C17.171 16.24 16.75 15.824 16.75 15.3114C16.75 14.7988 17.1598 14.3828 17.6724 14.3828H17.6835C18.1961 14.3828 18.6121 14.7988 18.6121 15.3114C18.6121 15.824 18.1961 16.24 17.6835 16.24Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M12.1899 16.24C11.6773 16.24 11.2563 15.824 11.2563 15.3114C11.2563 14.7988 11.6662 14.3828 12.1787 14.3828H12.1899C12.7024 14.3828 13.1184 14.7988 13.1184 15.3114C13.1184 15.824 12.7024 16.24 12.1899 16.24Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 17,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M6.68427 16.24C6.1717 16.24 5.74951 15.824 5.74951 15.3114C5.74951 14.7988 6.16056 14.3828 6.67313 14.3828H6.68427C7.19685 14.3828 7.61285 14.7988 7.61285 15.3114C7.61285 15.824 7.19685 16.24 6.68427 16.24Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 23,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M17.6835 21.0525C17.171 21.0525 16.75 20.6365 16.75 20.1239C16.75 19.6113 17.1598 19.1953 17.6724 19.1953H17.6835C18.1961 19.1953 18.6121 19.6113 18.6121 20.1239C18.6121 20.6365 18.1961 21.0525 17.6835 21.0525Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 29,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M12.1899 21.0525C11.6773 21.0525 11.2563 20.6365 11.2563 20.1239C11.2563 19.6113 11.6662 19.1953 12.1787 19.1953H12.1899C12.7024 19.1953 13.1184 19.6113 13.1184 20.1239C13.1184 20.6365 12.7024 21.0525 12.1899 21.0525Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M6.68427 21.0525C6.1717 21.0525 5.74951 20.6365 5.74951 20.1239C5.74951 19.6113 6.16056 19.1953 6.67313 19.1953H6.68427C7.19685 19.1953 7.61285 19.6113 7.61285 20.1239C7.61285 20.6365 7.19685 21.0525 6.68427 21.0525Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 41,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M17.1786 6.31257C16.666 6.31257 16.25 5.89657 16.25 5.384V1.30943C16.25 0.796859 16.666 0.380859 17.1786 0.380859C17.6911 0.380859 18.1071 0.796859 18.1071 1.30943V5.384C18.1071 5.89657 17.6911 6.31257 17.1786 6.31257Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 47,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M7.17711 6.31257C6.66454 6.31257 6.24854 5.89657 6.24854 5.384V1.30943C6.24854 0.796859 6.66454 0.380859 7.17711 0.380859C7.68968 0.380859 8.10568 0.796859 8.10568 1.30943V5.384C8.10568 5.89657 7.68968 6.31257 7.17711 6.31257Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 53,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("mask", {
                id: "mask0_15282_6458",
                style: {
                    maskType: "luminance"
                },
                maskUnits: "userSpaceOnUse",
                x: "0",
                y: "2",
                width: "25",
                height: "25",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    fillRule: "evenodd",
                    clipRule: "evenodd",
                    d: "M0.101074 2.33594H24.2439V26.9999H0.101074V2.33594Z",
                    fill: "white"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                    lineNumber: 60,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 59,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    fillRule: "evenodd",
                    clipRule: "evenodd",
                    d: "M6.93671 4.19289C3.72633 4.19289 1.95833 5.90518 1.95833 9.01404V20.2176C1.95833 23.3945 3.72633 25.1427 6.93671 25.1427H17.4085C20.6189 25.1427 22.3869 23.4267 22.3869 20.3117V9.01404C22.3919 7.48499 21.9808 6.29642 21.1649 5.47928C20.3255 4.63737 19.0317 4.19289 17.4197 4.19289H6.93671ZM17.4084 27H6.9366C2.72088 27 0.101074 24.4013 0.101074 20.2177V9.01422C0.101074 4.89384 2.72088 2.33594 6.9366 2.33594H17.4196C19.5355 2.33594 21.2849 2.96984 22.4796 4.16708C23.6397 5.33213 24.2501 7.00727 24.2439 9.0167V20.3118C24.2439 24.4372 21.6241 27 17.4084 27Z"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                    lineNumber: 63,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/NavCalendarIcon.tsx",
        lineNumber: 3,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = NavCalendarIcon;
}}),
"[project]/src/components/svgComponents/NavCandidatesIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const NavCandidatesIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 28 21",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                style: {
                    width: "24px",
                    height: "25px"
                },
                d: "M13.5942 11.4094H13.6276C16.7476 11.4094 19.2845 8.87132 19.2845 5.75256C19.2845 2.6338 16.7476 0.0957031 13.6276 0.0957031C10.5089 0.0957031 7.97201 2.6338 7.97201 5.74885C7.96582 7.25561 8.54648 8.67447 9.60877 9.74294C10.6686 10.8127 12.0837 11.4045 13.5942 11.4094ZM9.82915 5.75256C9.82915 3.6577 11.5328 1.95285 13.6276 1.95285C15.7225 1.95285 17.4273 3.6577 17.4273 5.75256C17.4273 7.84866 15.7225 9.55227 13.6276 9.55227H13.5967C12.5864 9.54856 11.638 9.15113 10.9261 8.43427C10.2142 7.71742 9.82544 6.7678 9.82915 5.75256Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCandidatesIcon.tsx",
                lineNumber: 4,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M20.1376 9.38204C20.2032 9.84633 20.6007 10.1819 21.0563 10.1819C21.0984 10.1819 21.1417 10.1794 21.185 10.1732C23.3827 9.8649 25.0429 7.95823 25.0479 5.73585C25.0479 3.52833 23.4668 1.66623 21.2903 1.30842C20.7814 1.22795 20.3072 1.56719 20.223 2.07357C20.1401 2.57995 20.483 3.05785 20.9882 3.14081C22.2647 3.35004 23.1908 4.44204 23.1908 5.73338C23.1883 7.03585 22.2164 8.15262 20.9288 8.33338C20.4199 8.40395 20.067 8.87319 20.1376 9.38204Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCandidatesIcon.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M23.8712 17.4887C24.0123 17.8588 24.3652 18.0867 24.7391 18.0867C24.8493 18.0867 24.9607 18.0668 25.0697 18.026C26.8402 17.3512 27.1051 16.1218 27.1051 15.4483C27.1051 14.3451 26.4749 12.9362 23.475 12.4868C22.9637 12.4211 22.4932 12.7591 22.4177 13.2668C22.3422 13.7756 22.6925 14.2473 23.1989 14.3241C24.5583 14.5271 25.248 14.906 25.248 15.4483C25.248 15.6179 25.248 15.9708 24.4085 16.2902C23.9294 16.4722 23.688 17.0095 23.8712 17.4887Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCandidatesIcon.tsx",
                lineNumber: 15,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M13.6276 20.8845C11.5736 20.8845 5.32121 20.8845 5.32121 16.9288C5.32121 12.9892 11.5736 12.9892 13.6276 12.9892C15.6816 12.9892 21.9327 12.9892 21.9327 16.9498C21.9327 20.8845 15.8995 20.8845 13.6276 20.8845ZM13.6276 14.8463C10.6859 14.8463 7.17835 15.2078 7.17835 16.9288C7.17835 18.6633 10.6859 19.0273 13.6276 19.0273C16.5693 19.0273 20.0756 18.6671 20.0756 16.9498C20.0756 15.2115 16.5693 14.8463 13.6276 14.8463Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCandidatesIcon.tsx",
                lineNumber: 19,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M6.19776 10.1818C6.15567 10.1818 6.11233 10.1793 6.069 10.1731C3.87138 9.86483 2.21233 7.95817 2.20738 5.73826C2.20738 3.52826 3.78843 1.66617 5.965 1.30836C6.48624 1.22664 6.94805 1.56959 7.03224 2.0735C7.11519 2.57988 6.77224 3.05778 6.2671 3.14074C4.99062 3.34997 4.06452 4.44197 4.06452 5.73578C4.067 7.03578 5.0389 8.15378 6.32529 8.33331C6.83414 8.40388 7.187 8.87312 7.11643 9.38198C7.05081 9.84626 6.65338 10.1818 6.19776 10.1818Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCandidatesIcon.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M2.18559 18.026C2.29454 18.0669 2.40597 18.0867 2.51616 18.0867C2.89007 18.0867 3.24293 17.8589 3.38407 17.4887C3.56731 17.0096 3.32588 16.4722 2.84674 16.2902C2.00607 15.9696 2.00607 15.6179 2.00607 15.4483C2.00607 14.906 2.69569 14.5272 4.05512 14.3241C4.5615 14.2474 4.91188 13.7757 4.83635 13.2668C4.75959 12.7592 4.29159 12.4224 3.78026 12.4868C0.779116 12.9362 0.148926 14.3464 0.148926 15.4483C0.148926 16.1206 0.413878 17.35 2.18559 18.026Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavCandidatesIcon.tsx",
                lineNumber: 29,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/NavCandidatesIcon.tsx",
        lineNumber: 3,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = NavCandidatesIcon;
}}),
"[project]/src/components/svgComponents/NavHomeIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const NavHomeIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        style: {
            width: "24px",
            height: "24px"
        },
        viewBox: "0 0 30 30",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M24.0664 13.9443C24.0664 13.212 23.7285 12.5205 23.1504 12.0713H23.1494L16.5469 6.93555C16.1304 6.61158 15.6175 6.43555 15.0898 6.43555C14.5624 6.43565 14.0501 6.61168 13.6338 6.93555L7.0293 12.0713C6.74431 12.293 6.51407 12.577 6.35547 12.9014C6.19686 13.2258 6.11414 13.5822 6.11426 13.9434V22.8584C6.11426 23.2689 6.27715 23.6629 6.56738 23.9531C6.8576 24.2433 7.25173 24.4062 7.66211 24.4062H22.5186C22.9289 24.4062 23.3231 24.2433 23.6133 23.9531C23.9035 23.6629 24.0664 23.2689 24.0664 22.8584V13.9443ZM25.9238 22.8584C25.9238 23.7612 25.5651 24.6271 24.9268 25.2656C24.2882 25.9041 23.4216 26.2637 22.5186 26.2637H7.66211C6.75911 26.2637 5.89242 25.9041 5.25391 25.2656C4.6156 24.6271 4.25684 23.7612 4.25684 22.8584V13.9443C4.25657 13.3002 4.40368 12.6646 4.68652 12.0859C4.96946 11.5071 5.38108 11 5.88965 10.6045L12.4932 5.46973C13.2355 4.89223 14.1493 4.57823 15.0898 4.57812C16.0305 4.57813 16.945 4.89213 17.6875 5.46973L24.2891 10.6045C25.3204 11.4058 25.9238 12.6389 25.9238 13.9443V22.8584Z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/NavHomeIcon.tsx",
                        lineNumber: 5,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M9.30687 18.6483C9.57186 18.2093 10.1423 18.068 10.5813 18.3328C11.7705 19.0508 13.3869 19.4373 15.053 19.4373C16.7189 19.4373 18.3359 19.0509 19.5266 18.3328C19.9657 18.068 20.5371 18.2091 20.802 18.6483C21.0666 19.0872 20.9253 19.6577 20.4866 19.9227C18.9411 20.8549 16.9718 21.2938 15.053 21.2938C13.1344 21.2937 11.1666 20.8549 9.6223 19.9227C9.18337 19.6577 9.04207 19.0872 9.30687 18.6483Z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/NavHomeIcon.tsx",
                        lineNumber: 6,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/svgComponents/NavHomeIcon.tsx",
                lineNumber: 4,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                    id: "clip0_15321_8934",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        width: "29.7143",
                        height: "29.7143",
                        fill: "white",
                        transform: "translate(0.196289 0.142578)"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/NavHomeIcon.tsx",
                        lineNumber: 10,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/NavHomeIcon.tsx",
                    lineNumber: 9,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/NavHomeIcon.tsx",
                lineNumber: 8,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/NavHomeIcon.tsx",
        lineNumber: 3,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = NavHomeIcon;
}}),
"[project]/src/components/svgComponents/NavJobsIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const NavJobsIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        style: {
            width: "25px",
            height: "24px"
        },
        viewBox: "0 0 31 30",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            fillRule: "evenodd",
            clipRule: "evenodd",
            d: "M9.9135 8.37664H5.06568C3.70607 8.37664 2.5918 9.50363 2.5918 10.9084V15.6188C2.5918 15.9587 2.82564 16.254 3.15658 16.3318L3.58889 16.4335V23.6685C3.58889 24.2863 3.83434 24.8788 4.27115 25.3156C4.70803 25.7525 5.30047 25.9979 5.91828 25.9979H24.7605C25.3783 25.9979 25.9708 25.7525 26.4076 25.3156C26.8445 24.8788 27.0899 24.2863 27.0899 23.6685V16.4335L27.5222 16.3318C27.8531 16.254 28.087 15.9587 28.087 15.6188V10.9084C28.087 9.50363 26.9727 8.37664 25.6131 8.37664H20.7653V6.90869C20.7653 5.30445 19.4648 4.00391 17.8605 4.00391H12.8183C11.214 4.00391 9.9135 5.30445 9.9135 6.90869V8.37664ZM18.6651 18.4142L25.6251 16.7779V23.6685C25.6251 23.8978 25.5339 24.1177 25.3718 24.2798C25.2097 24.4419 24.9898 24.533 24.7605 24.533H5.91828C5.689 24.533 5.4691 24.4419 5.30697 24.2798C5.14484 24.1177 5.05373 23.8978 5.05373 23.6685V16.7779L12.0137 18.4142V19.3379C12.0137 20.2357 12.7426 20.9646 13.6404 20.9646H17.0384C17.9362 20.9646 18.6651 20.2357 18.6651 19.3379V18.4142ZM17.2002 19.3379V17.5495C17.2002 17.4602 17.1277 17.3877 17.0384 17.3877H13.6404C13.5511 17.3877 13.4786 17.4602 13.4786 17.5495V19.3379C13.4786 19.4272 13.5511 19.4997 13.6404 19.4997H17.0384C17.1277 19.4997 17.2002 19.4272 17.2002 19.3379ZM18.5454 16.9375C18.3038 16.3426 17.7197 15.9229 17.0384 15.9229H13.6404C12.9591 15.9229 12.375 16.3426 12.1334 16.9375L4.05664 15.0386V10.9084C4.05664 10.3256 4.5016 9.84148 5.06568 9.84148H25.6131C26.1772 9.84148 26.6221 10.3256 26.6221 10.9084V15.0386L18.5454 16.9375ZM19.3004 8.37664H11.3783V6.90869C11.3783 6.11346 12.023 5.46875 12.8183 5.46875H17.8605C18.6558 5.46875 19.3004 6.11346 19.3004 6.90869V8.37664Z"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/NavJobsIcon.tsx",
            lineNumber: 4,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/NavJobsIcon.tsx",
        lineNumber: 3,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = NavJobsIcon;
}}),
"[project]/src/components/svgComponents/NavSettingsIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const NavSettingsIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 31 30",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M26.7675 16.9907L25.7251 16.1326C25.011 15.5448 25.0123 14.4523 25.7251 13.8657L26.7675 13.0076C27.5092 12.3969 27.699 11.3497 27.2186 10.5174L25.1603 6.95242C24.6798 6.12025 23.6778 5.76104 22.7782 6.09803L21.5139 6.57172C20.6478 6.89609 19.7023 6.349 19.5506 5.43824L19.3286 4.1064C19.1707 3.15864 18.3586 2.4707 17.3977 2.4707H13.2812C12.3203 2.4707 11.5082 3.15864 11.3503 4.10645L11.1283 5.43824C10.9762 6.35061 10.0294 6.89555 9.16502 6.57177L7.90068 6.09803C7.00102 5.76104 5.99914 6.1203 5.5186 6.95242L3.46036 10.5174C2.97993 11.3495 3.16962 12.3968 3.91149 13.0075L4.95384 13.8656C5.66797 14.4535 5.66655 15.5459 4.95384 16.1326L3.91144 16.9907C3.16962 17.6015 2.97988 18.6487 3.46032 19.4808L5.5186 23.0459C5.99909 23.878 7.00078 24.2372 7.90068 23.9002L9.16497 23.4266C10.0312 23.102 10.9765 23.6495 11.1282 24.56L11.3502 25.8918C11.5082 26.8397 12.3203 27.5276 13.2812 27.5276H17.3977C18.3586 27.5276 19.1707 26.8397 19.3286 25.8919L19.5505 24.5601C19.7025 23.648 20.6493 23.1027 21.5138 23.4267L22.7782 23.9003C23.6781 24.2373 24.6798 23.8781 25.1603 23.0459L27.2186 19.4808C27.699 18.6487 27.5092 17.6015 26.7675 16.9907ZM23.465 22.0671L22.2006 21.5934C20.1795 20.8363 17.9738 22.1137 17.6197 24.2383L17.3977 25.5701H13.2812L13.0592 24.2383C12.7045 22.1096 10.4955 20.8378 8.47826 21.5934L7.21391 22.0671L5.15567 18.5021L6.19803 17.644C7.86436 16.2722 7.86089 13.7233 6.19803 12.3543L5.15567 11.4962L7.21396 7.9312L8.47826 8.40489C10.4995 9.16198 12.7051 7.88466 13.0592 5.76006L13.2812 4.42827H17.3977L17.6196 5.76006C17.9744 7.88902 20.1835 9.16041 22.2006 8.40489L23.4649 7.9312L25.5237 11.4957C25.5237 11.4957 25.5235 11.4959 25.5231 11.4962L24.4808 12.3543C22.8145 13.726 22.8179 16.275 24.4808 17.6439L25.5232 18.502L23.465 22.0671ZM15.3394 10.1705C12.6769 10.1705 10.5108 12.3366 10.5108 14.9992C10.5108 17.6617 12.6769 19.8279 15.3394 19.8279C18.002 19.8279 20.1681 17.6617 20.1681 14.9992C20.1681 12.3366 18.002 10.1705 15.3394 10.1705ZM15.3394 17.8703C13.7563 17.8703 12.4683 16.5823 12.4683 14.9992C12.4683 13.416 13.7563 12.128 15.3394 12.128C16.9226 12.128 18.2106 13.416 18.2106 14.9992C18.2106 16.5823 16.9226 17.8703 15.3394 17.8703Z"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/NavSettingsIcon.tsx",
            lineNumber: 4,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/NavSettingsIcon.tsx",
        lineNumber: 3,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = NavSettingsIcon;
}}),
"[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const ModalCloseIcon = (props)=>{
    const { className } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "40",
        height: "41",
        viewBox: "0 0 40 41",
        fill: "none",
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "20.0003",
                cy: "20.5",
                r: "18.209",
                fill: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
                lineNumber: 5,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z",
                fill: "#333333"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
        lineNumber: 4,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ModalCloseIcon;
}}),
"[project]/src/components/header/MobileSidebar.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavSettingsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavSettingsIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavJobsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavJobsIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavCalendarIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavCalendarIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavCandidatesIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavCandidatesIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavHomeIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavHomeIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$Notification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/Notification.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$LogoutIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/LogoutIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/authSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/user.png.mjs { IMAGE => "[project]/public/assets/images/user.png (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/down-arrow.svg.mjs { IMAGE => "[project]/public/assets/images/down-arrow.svg (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ProfileIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ProfileIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const MobileSidebar = ({ isOpen, onClose })=>{
    const navigate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [subLink, setSubLink] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const userProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["selectProfileData"]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("header");
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `sidebar ${isOpen ? "open" : ""}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "sidebar-header",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                src: userProfile?.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                alt: "Profile",
                                className: "sidebar-profile",
                                width: 100,
                                height: 100
                            }, void 0, false, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 32,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                id: "dropdown-user-name",
                                children: `${userProfile?.first_name}`
                            }, void 0, false, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 33,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                onClick: onClose,
                                className: "clear-btn p-0",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                    lineNumber: 36,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 35,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                        lineNumber: 31,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "sidebar-menu",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                onClick: ()=>{
                                    navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DASHBOARD);
                                    onClose();
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavHomeIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                        lineNumber: 46,
                                        columnNumber: 13
                                    }, this),
                                    " Home"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 40,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                onClick: ()=>{
                                    navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.CANDIDATES);
                                    onClose();
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavCandidatesIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                        lineNumber: 54,
                                        columnNumber: 13
                                    }, this),
                                    " Candidates"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 48,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                onClick: ()=>{
                                    navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].INTERVIEW.CALENDAR);
                                    onClose();
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavCalendarIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                        lineNumber: 62,
                                        columnNumber: 13
                                    }, this),
                                    " Calendar"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 56,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                onClick: ()=>{
                                    navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS);
                                    onClose();
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavJobsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                        lineNumber: 70,
                                        columnNumber: 13
                                    }, this),
                                    " Jobs"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 64,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: "sub-menu-bar",
                                onClick: ()=>setSubLink(!subLink),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "sub-menu-list",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavSettingsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                                lineNumber: 74,
                                                columnNumber: 15
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: [
                                                    "Settings",
                                                    " ",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                        alt: "downArrow",
                                                        style: {
                                                            rotate: `${subLink ? "180deg" : "0deg"}`,
                                                            width: "13px",
                                                            marginLeft: "5px"
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                                        lineNumber: 77,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                                lineNumber: 75,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                        lineNumber: 73,
                                        columnNumber: 13
                                    }, this),
                                    subLink && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "sidebar-menu sidebar-sub-menu",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                onClick: ()=>{
                                                    navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ROLE_EMPLOYEES.ROLES_PERMISSIONS);
                                                },
                                                children: "Roles and Permissions"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                                lineNumber: 82,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                onClick: ()=>{
                                                    navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);
                                                },
                                                children: "Employee Management"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                                lineNumber: 89,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                        lineNumber: 81,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 72,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$Notification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                        lineNumber: 100,
                                        columnNumber: 13
                                    }, this),
                                    "Notifications"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 99,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                onClick: ()=>{
                                    navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DASHBOARD);
                                    onClose();
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ProfileIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                        lineNumber: 109,
                                        columnNumber: 13
                                    }, this),
                                    " ",
                                    t("my_profile")
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 103,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$LogoutIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        className: "strokeSvg"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                        lineNumber: 112,
                                        columnNumber: 13
                                    }, this),
                                    "Logout"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                                lineNumber: 111,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/header/MobileSidebar.tsx",
                        lineNumber: 39,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                lineNumber: 30,
                columnNumber: 7
            }, this),
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "overlay",
                onClick: onClose
            }, void 0, false, {
                fileName: "[project]/src/components/header/MobileSidebar.tsx",
                lineNumber: 118,
                columnNumber: 18
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = MobileSidebar;
}}),
"[project]/src/components/svgComponents/HamburgerIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function HamburgerIcon() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 640 640",
        fill: "#333",
        width: "25",
        height: "25",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M96 160C96 142.3 110.3 128 128 128L512 128C529.7 128 544 142.3 544 160C544 177.7 529.7 192 512 192L128 192C110.3 192 96 177.7 96 160zM96 320C96 302.3 110.3 288 128 288L512 288C529.7 288 544 302.3 544 320C544 337.7 529.7 352 512 352L128 352C110.3 352 96 337.7 96 320zM544 480C544 497.7 529.7 512 512 512L128 512C110.3 512 96 497.7 96 480C96 462.3 110.3 448 128 448L512 448C529.7 448 544 462.3 544 480z"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/HamburgerIcon.tsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/HamburgerIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = HamburgerIcon;
}}),
"[project]/src/components/header/Header.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$syncReduxToCookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/syncReduxToCookies.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/logo.svg.mjs { IMAGE => "[project]/public/assets/images/logo.svg (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/down-arrow.svg.mjs { IMAGE => "[project]/public/assets/images/down-arrow.svg (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/user.png.mjs { IMAGE => "[project]/public/assets/images/user.png (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/header.module.scss.module.css [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$Notification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/Notification.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/authSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/authServices.ts [app-ssr] (ecmascript)");
// Interface definitions moved to authServices.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$dataSecurityIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/dataSecurityIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ProfileIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ProfileIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$LogoutIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/LogoutIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$notification$2f$Notifications$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/notification/Notifications.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationServices$2f$notificationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/notificationServices/notificationService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/notificationSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavCalendarIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavCalendarIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavCandidatesIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavCandidatesIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavHomeIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavHomeIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavJobsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavJobsIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavSettingsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/NavSettingsIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$MobileSidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/MobileSidebar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$HamburgerIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/HamburgerIcon.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const Header = ()=>{
    const [dropdown, SetDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const userProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["selectProfileData"]);
    const [isSidebarOpen, setSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [subLink, setSubLink] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const path = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDispatch"])();
    const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])((state)=>state.auth.authData);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("header");
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("common");
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const [isNotificationOpen, setIsNotificationOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const hasUnreadNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])((state)=>state.notification.hasUnreadNotifications);
    const navigate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // Handle clicks outside of dropdown to close it
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            if (dropdownRef.current && !dropdownRef.current.contains(event.target) && !(event.target instanceof HTMLElement && event.target.id.includes("dropdown"))) {
                SetDropdown(false);
            }
        };
        // Add event listener when dropdown is open
        if (dropdown) {
            document.addEventListener("mousedown", handleClickOutside);
        }
        // Clean up event listener
        return ()=>{
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [
        dropdown
    ]);
    // Toggle dropdown visibility
    const MenuDropdown = ()=>{
        SetDropdown(!dropdown);
    };
    // Function to fetch permissions using the authServices
    const fetchPermissions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUserPermissions"])();
            // Only update Redux store when success is true
            if (response.data?.success) {
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setPermissions"])(response.data.data.rolePermissions));
                // Sync Redux state to cookies after updating permissions
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$syncReduxToCookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["syncReduxStateToCookies"])(response.data.data.rolePermissions, true);
            } else {
                console.log("Permission fetch unsuccessful:", response.data?.message);
            }
        } catch (error) {
            console.error("Error fetching permissions:", error);
        }
    }, [
        path,
        dispatch
    ]);
    const getUserNotificationsUnreadStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$notificationServices$2f$notificationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUnreadNotificationsCount"])();
            if (response.data?.success) {
                const hasUnreadNotifications = response.data.data.count > 0;
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setHasUnreadNotification"])(hasUnreadNotifications));
            } else {
                console.error("Failed to fetch unread notifications status:", response.data?.message);
            }
        } catch (error) {
            console.error("Error fetching unread notifications status:", error);
        }
    }, []);
    // Sync Redux state to cookies after mounting component
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$syncReduxToCookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["syncReduxStateToCookies"])();
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Check if this is first mount or a genuine route change
        fetchPermissions();
        getUserNotificationsUnreadStatus();
    }, [
        path,
        dispatch,
        fetchPermissions
    ]);
    /**
   * Logs out the user if the access token is invalid.
   * If the access token is invalid, it logs out the user and shows a toast message.
   */ // const logoutUser = async () => {
    //   const token = getAccessToken();
    //   if (!token) {
    //     onHandleLogout();
    //     toast.dismiss();
    //     toastMessageError(t("session_expired"));
    //   }
    // };
    const onHandleLogout = async ()=>{
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logout"])(authData?.id);
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    };
    const SidebarDropdown = ()=>{
        setSidebarOpen(!isSidebarOpen);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                    className: "navbar navbar-expand-sm",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "container",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "d-flex align-items-center justify-content-between w-100",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "navbar-brand",
                                    href: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].HOME,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                        alt: "logo",
                                        width: 640,
                                        height: 320,
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logo
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 166,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 165,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    onClick: SidebarDropdown,
                                    className: "clear-btn p-0 primary hamburger",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$HamburgerIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 169,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 168,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: "header_links",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            className: pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DASHBOARD ? "active" : "",
                                            onClick: ()=>navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DASHBOARD),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavHomeIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 174,
                                                    columnNumber: 19
                                                }, this),
                                                " Home"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 173,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            className: pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.CANDIDATES ? "active" : "",
                                            onClick: ()=>navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.CANDIDATES),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavCandidatesIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 180,
                                                    columnNumber: 19
                                                }, this),
                                                " Candidates"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 176,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            className: pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].INTERVIEW.CALENDAR ? "active" : "",
                                            onClick: ()=>navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].INTERVIEW.CALENDAR),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavCalendarIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 183,
                                                    columnNumber: 19
                                                }, this),
                                                " Calendar"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 182,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            className: pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS ? "active" : "",
                                            onClick: ()=>navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavJobsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 186,
                                                    columnNumber: 19
                                                }, this),
                                                " Jobs"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 185,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {}, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 191,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 172,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header_right,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].user_drop,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$Notification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                hasNotification: hasUnreadNotification,
                                                id: "notification-icon-id",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].user_drop,
                                                onClick: (e)=>{
                                                    e.stopPropagation();
                                                    e.preventDefault();
                                                    setIsNotificationOpen((prev)=>!prev);
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 195,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 194,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `dropdown ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].user_drop}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    className: `dropdown-toggle ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].user_drop_btn}`,
                                                    "data-bs-toggle": "dropdown",
                                                    onClick: MenuDropdown,
                                                    id: "dropdown-MenuButton",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].circle_img}`,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                src: userProfile?.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                alt: "Profile",
                                                                width: 100,
                                                                height: 100,
                                                                id: "dropdown-user-image"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 215,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 214,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].admin_info,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                                id: "dropdown-user-name",
                                                                children: `${userProfile?.first_name}`
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 218,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 217,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                            alt: "downArrow",
                                                            style: {
                                                                rotate: `${dropdown ? "180deg" : "0deg"}`
                                                            },
                                                            id: "dropdown-downArrow"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 220,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 207,
                                                    columnNumber: 19
                                                }, this),
                                                dropdown && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown_menu,
                                                    ref: dropdownRef,
                                                    id: "dropdown-menu",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ProfileIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 226,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    onClick: ()=>{
                                                                        navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].PROFILE.MY_PROFILE);
                                                                        SetDropdown(false);
                                                                    },
                                                                    children: t("my_profile")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 227,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 225,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].sub_menubar,
                                                            onClick: ()=>setSubLink(!subLink),
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].sub_menu_list,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$NavSettingsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 238,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            children: [
                                                                                "Settings",
                                                                                " ",
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                                    alt: "downArrow",
                                                                                    style: {
                                                                                        rotate: `${subLink ? "180deg" : "0deg"}`,
                                                                                        width: "13px",
                                                                                        marginLeft: "5px"
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                                    lineNumber: 241,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 239,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 237,
                                                                    columnNumber: 25
                                                                }, this),
                                                                subLink && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].sidebar_sub_menu}`,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                            onClick: ()=>{
                                                                                navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ROLE_EMPLOYEES.ROLES_PERMISSIONS);
                                                                                SetDropdown(false);
                                                                            },
                                                                            children: "🔒 Roles and Permissions"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 250,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                            onClick: ()=>{
                                                                                navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);
                                                                                SetDropdown(false);
                                                                            },
                                                                            children: "🗂️ Employee Management"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 258,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 249,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 236,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$LogoutIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    className: "strokeSvg"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 270,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    onClick: ()=>onHandleLogout(),
                                                                    children: "Logout"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 271,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 269,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 224,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 206,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 193,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/header/Header.tsx",
                            lineNumber: 164,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 163,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/header/Header.tsx",
                    lineNumber: 162,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 158,
                columnNumber: 7
            }, this),
            isNotificationOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$notification$2f$Notifications$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                setIsNotificationOpen: setIsNotificationOpen
            }, void 0, false, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 282,
                columnNumber: 29
            }, this) : null,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$MobileSidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isOpen: isSidebarOpen,
                onClose: ()=>setSidebarOpen(false)
            }, void 0, false, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 284,
                columnNumber: 7
            }, this),
            pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.GENERATE_JOB && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "information-box",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$dataSecurityIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 289,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: tCommon("data_security_msg")
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 290,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 288,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = Header;
}}),
"[project]/src/components/header/HeaderWrapper.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>HeaderWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/Header.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
function HeaderWrapper() {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    console.log("pathname", pathname);
    if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BEFORE_LOGIN_ROUTES"].includes(pathname)) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/src/components/header/HeaderWrapper.tsx",
        lineNumber: 14,
        columnNumber: 10
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__876937c7._.js.map