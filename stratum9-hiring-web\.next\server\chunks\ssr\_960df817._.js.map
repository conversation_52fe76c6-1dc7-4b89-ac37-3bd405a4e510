{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ThreeDotsIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction ThreeDotsIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M12.0001 19.2C13.3256 19.2 14.4001 20.2745 14.4001 21.6C14.4001 22.9255 13.3256 24 12.0001 24C10.6746 24 9.6001 22.9255 9.6001 21.6C9.6001 20.2745 10.6746 19.2 12.0001 19.2ZM12.0001 9.60005C13.3256 9.60005 14.4001 10.6746 14.4001 12C14.4001 13.3255 13.3256 14.4 12.0001 14.4C10.6746 14.4 9.6001 13.3255 9.6001 12C9.6001 10.6746 10.6746 9.60005 12.0001 9.60005ZM12.0001 0C13.3256 0 14.4001 1.07452 14.4001 2.39999C14.4001 3.72546 13.3256 4.79998 12.0001 4.79998C10.6746 4.79998 9.6001 3.72546 9.6001 2.39999C9.6001 1.07452 10.6746 0 12.0001 0Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default ThreeDotsIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,8OAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGpD;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,8OAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,8OAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,8OAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface TextareaProps<T extends FieldValues> extends TextareaHTMLAttributes<HTMLTextAreaElement> {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textarea<T extends FieldValues>({ control, name, ...props }: TextareaProps<T>) {\n  return (\n    <Controller\n      control={control}\n      render={({ field }) => <textarea {...props} value={field.value} onChange={field.onChange} aria-label=\"\" />}\n      name={name}\n      defaultValue={\"\" as T[typeof name]}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAgC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAyB;IACnG,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,8OAAC;gBAAU,GAAG,KAAK;gBAAE,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;;;;;QACrG,MAAM;QACN,cAAc;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/screenResumeServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { http } from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { IUploadManualCandidate } from \"@/interfaces/screenResumeInterfaces\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\n\n// Define interface for pagination parameters\ninterface PaginationParams {\n  limit?: number;\n  offset?: number;\n  job_id?: number;\n  status?: string;\n}\n\ninterface JobApplication {\n  application_id: number;\n  job_id: number;\n  hiring_manager_id: number;\n  candidate_id: number;\n  candidate_name: string;\n  ai_decision: string;\n  ai_reason: string;\n  status?: string;\n  created_ts: string;\n}\n\ninterface JobApplicationResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication[];\n  pagination: {\n    limit: number;\n    offset: number;\n    totalCount: number;\n    hasMore: boolean;\n  };\n}\n\ninterface ChangeApplicationStatusParams {\n  job_id: number;\n  candidate_id: number;\n  hiring_manager_id: number;\n  status: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];\n  hiring_manager_reason: string;\n}\n\ninterface ChangeApplicationStatusResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication;\n}\n/**\n * Upload resume and assessment files and get presigned URLs\n * @param file - The file to upload (resume or assessment)\n * @returns Promise with presigned URL response\n */\nexport const getPresignedUrl = async (file: File): Promise<ApiResponse> => {\n  const formData = new FormData();\n  formData.append(\"file\", file);\n  formData.append(\"fileType\", file.type);\n  formData.append(\"fileName\", file.name);\n\n  return http.post(endpoint.resumeScreen.GET_PRESIGNED_URL, formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\",\n    },\n  });\n};\n\n/**\n * Upload file to S3 using presigned URL\n * @param presignedUrl - The presigned URL for S3 upload\n * @param file - The file to upload\n * @returns Promise with upload response\n */\nexport const uploadToS3 = async (presignedUrl: string, file: File): Promise<Response> => {\n  return fetch(presignedUrl, {\n    method: \"PUT\",\n    body: file,\n    headers: {\n      \"Content-Type\": file.type,\n    },\n  });\n};\n\n/**\n * Process the file upload to get presigned URL and upload to S3\n * @param file - The file to upload\n * @returns Object with file URL and parsed text\n */\nexport const processFileUpload = async (file: File): Promise<{ fileUrl: string; fileText: string; presignedUrl: string }> => {\n  try {\n    // Get presigned URL\n    const presignedUrlResponse = await getPresignedUrl(file);\n\n    if (!presignedUrlResponse.data) {\n      throw new Error(\"Failed to get presigned URL\");\n    }\n    const responseData = presignedUrlResponse.data;\n\n    // The response might have data nested inside another data property\n    const urlData = responseData.data;\n\n    if (!urlData.presignedUrl || !urlData.fileUrl) {\n      console.error(\"Missing URL information in response:\", urlData);\n      throw new Error(\"Missing URL information in response\");\n    }\n\n    const { presignedUrl, fileUrl, fileText } = urlData;\n\n    // Upload file to S3\n    const uploadResponse = await uploadToS3(presignedUrl, file);\n    if (!uploadResponse.ok) {\n      throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`);\n    }\n    // Return the file URL and flag for backend extraction\n    return {\n      fileUrl,\n      fileText: fileText, // Special flag to indicate backend should extract text\n      presignedUrl,\n    };\n  } catch (error) {\n    console.error(\"Error processing file upload:\", error);\n    // Include error details in the console for debugging\n    if (error instanceof Error) {\n      console.error(\"Error message:\", error.message);\n      console.error(\"Error stack:\", error.stack);\n    }\n    throw error;\n  }\n};\n\n/**\n * Upload manual candidate data with resume and assessment\n * @param data - The form values with candidate information\n * @returns Promise with API response\n */\n/**\n * Get all job applications with pagination (not just pending)\n * @param params - Pagination parameters (limit, offset, filters)\n * @returns Promise with job applications response\n */\nexport const getAllPendingJobApplications = async (params: PaginationParams): Promise<ApiResponse<JobApplicationResponse>> => {\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    if (params.limit) queryParams.append(\"limit\", params.limit.toString());\n    // Always include offset parameter, even when it's 0\n    queryParams.append(\"offset\", params.offset !== undefined ? params.offset.toString() : \"0\");\n    if (params.job_id) queryParams.append(\"job_id\", params.job_id.toString());\n    if (params.status) queryParams.append(\"status\", params.status);\n\n    // Make API request\n    const url = `${endpoint.resumeScreen.GET_ALL_PENDING_JOB_APPLICATIONS}?${queryParams.toString()}`;\n    return http.get(url);\n  } catch (error) {\n    console.error(\"Error fetching job applications:\", error);\n    throw error;\n  }\n};\n\n/**\n * Upload manual candidates to the backend for processing\n *\n * This function sends candidate data to the backend API for manual candidate upload.\n * The candidates should already have their files uploaded to S3 and contain URLs\n * instead of File objects.\n *\n * @param {Object} uploadManualCandidateData - The data object containing candidates and job information\n * @param {IUploadManualCandidate[]} uploadManualCandidateData.candidates - Array of candidate objects with uploaded file URLs\n * @param {number} uploadManualCandidateData.jobId - The ID of the job to associate candidates with\n *\n * @returns {Promise<ApiResponse>} Promise that resolves to the API response containing upload results\n *\n * @example\n * ```typescript\n * const candidateData = {\n *   candidates: [\n *     {\n *       name: \"John Doe\",\n *       email: \"<EMAIL>\",\n *       gender: \"male\",\n *       resume: \"https://cdn.example.com/resume.pdf\",\n *       assessment: \"https://cdn.example.com/assessment.pdf\",\n *       additionalInfo: \"Experienced developer\"\n *     }\n *   ],\n *   jobId: 123\n * };\n *\n * const response = await uploadManualCandidate(candidateData);\n * ```\n */\nexport const uploadManualCandidate = async (uploadManualCandidateData: {\n  candidates: IUploadManualCandidate[];\n  job_id: number;\n}): Promise<ApiResponse> => {\n  return http.post(endpoint.resumeScreen.MANUAL_CANDIDATE_UPLOAD, uploadManualCandidateData);\n};\n\n/**\n * Change the status of a job application (Approve, Reject, or Hold)\n * @param params - Parameters containing job_id, candidate_id, hiring_manager_id, and status\n * @param data - Data containing hiring_manager_reason\n * @returns Promise with API response\n */\nexport const changeApplicationStatus = async (data: ChangeApplicationStatusParams): Promise<ApiResponse<ChangeApplicationStatusResponse>> => {\n  return http.post(endpoint.resumeScreen.CHANGE_APPLICATION_STATUS, data);\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAuDO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IACxB,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IACrC,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IAErC,OAAO,oHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,iBAAiB,EAAE,UAAU;QAClE,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAQO,MAAM,aAAa,OAAO,cAAsB;IACrD,OAAO,MAAM,cAAc;QACzB,QAAQ;QACR,MAAM;QACN,SAAS;YACP,gBAAgB,KAAK,IAAI;QAC3B;IACF;AACF;AAOO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,oBAAoB;QACpB,MAAM,uBAAuB,MAAM,gBAAgB;QAEnD,IAAI,CAAC,qBAAqB,IAAI,EAAE;YAC9B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,eAAe,qBAAqB,IAAI;QAE9C,mEAAmE;QACnE,MAAM,UAAU,aAAa,IAAI;QAEjC,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,OAAO,EAAE;YAC7C,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;QAE5C,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,WAAW,cAAc;QACtD,IAAI,CAAC,eAAe,EAAE,EAAE;YACtB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,eAAe,MAAM,EAAE;QACzE;QACA,sDAAsD;QACtD,OAAO;YACL;YACA,UAAU;YACV;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,qDAAqD;QACrD,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;YAC7C,QAAQ,KAAK,CAAC,gBAAgB,MAAM,KAAK;QAC3C;QACA,MAAM;IACR;AACF;AAYO,MAAM,+BAA+B,OAAO;IACjD,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,IAAI,OAAO,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACnE,oDAAoD;QACpD,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,KAAK,YAAY,OAAO,MAAM,CAAC,QAAQ,KAAK;QACtF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACtE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAE7D,mBAAmB;QACnB,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,gCAAgC,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI;QACjG,OAAO,oHAAA,CAAA,OAAI,CAAC,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAkCO,MAAM,wBAAwB,OAAO;IAI1C,OAAO,oHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,uBAAuB,EAAE;AAClE;AAQO,MAAM,0BAA0B,OAAO;IAC5C,OAAO,oHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,yBAAyB,EAAE;AACpE", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/jobRequirementConstant.ts"], "sourcesContent": ["import { JobSelectOption } from \"@/interfaces/jobRequirementesInterfaces\";\n\n/**\n * Job category options\n */\nexport const CATEGORY_OPTION: JobSelectOption[] = [\n  { label: \"Full time\", value: \"full_time\" },\n  { label: \"Part time\", value: \"part_time\" },\n  { label: \"Contract\", value: \"contract\" },\n  { label: \"Internship\", value: \"internship\" },\n  { label: \"Freelance\", value: \"freelance\" },\n];\n\n/**\n * Salary cycle options\n */\nexport const SALARY_CYCLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Per Hour\", value: \"per hour\" },\n  { label: \"Per Month\", value: \"per month\" },\n  { label: \"Per Annum\", value: \"per annum\" },\n];\n\n/**\n * Location type options\n */\nexport const LOCATION_TYPE_OPTIONS: JobSelectOption[] = [\n  { label: \"Remote\", value: \"remote\" },\n  { label: \"Hybrid\", value: \"hybrid\" },\n  { label: \"On-site\", value: \"onsite\" },\n];\n\n/**\n * Tone style options\n */\nexport const TONE_STYLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Professional & Formal\", value: \"Professional_Formal\" },\n  { label: \"Conversational & Approachable\", value: \"Conversational_Approachable\" },\n  { label: \"Bold & Energetic\", value: \"Bold_Energetic\" },\n  { label: \"Inspirational & Mission-Driven\", value: \"Inspirational_Mission-Driven\" },\n  { label: \"Technical & Precise\", value: \"Technical_Precise\" },\n  { label: \"Creative & Fun\", value: \"Creative_Fun\" },\n  { label: \"Inclusive & Human-Centered\", value: \"Inclusive_Human-Centered\" },\n  { label: \"Minimalist & Straightforward\", value: \"Minimalist_Straightforward\" },\n];\n\n/**\n * Compliance options\n */\nexport const COMPLIANCE_OPTIONS: JobSelectOption[] = [\n  { label: \"Equal Employment Opportunity (EEO) Statement\", value: \"Equal Employment Opportunity (EEO) Statement\" },\n  {\n    label: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n    value: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n  },\n  { label: \"Disability Accommodation Statement\", value: \"Disability Accommodation Statement\" },\n  {\n    label: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n    value: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n  },\n  { label: \"Diversity & Inclusion Commitment\", value: \"Diversity & Inclusion Commitment\" },\n  {\n    label: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n    value: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n  },\n  {\n    label: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n    value: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n  },\n  { label: \"Work Authorization & Immigration Statement\", value: \"Work Authorization & Immigration Statement\" },\n];\n\nexport const EXPERIENCE_LEVEL_OPTIONS: JobSelectOption[] = [\n  { label: \"General\", value: \"General\" },\n  { label: \"No experience necessary\", value: \"No experience necessary\" },\n  { label: \"Entry-Level Position\", value: \"Entry-Level Position\" },\n  { label: \"Mid-Level Professional\", value: \"Mid-Level Professional\" },\n  { label: \"Senior/Experienced Professional\", value: \"Senior/Experienced Professional\" },\n  { label: \"Managerial/Executive Level\", value: \"Managerial/Executive Level\" },\n  { label: \"Specialized Expert\", value: \"Specialized Expert\" },\n];\n\nexport const DEPARTMENT_OPTION: JobSelectOption[] = [\n  { label: \"IT\", value: \"IT\" },\n  { label: \"HR\", value: \"HR\" },\n  { label: \"Marketing\", value: \"Marketing\" },\n  { label: \"Finance\", value: \"Finance\" },\n  { label: \"Sales\", value: \"Sales\" },\n];\n/**\n * Constants for file upload validation\n */\nexport const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\nexport const FILE_TYPE = \"application/pdf\";\nexport const FILE_NAME = \".pdf\";\n\n/**\n * Remove all $ and space symbols to clean the input\n */\nexport const SALARY_REMOVE_SYMBOL_REGEX = /[\\$\\s]/g;\n\n/**\n * Currency symbol\n */\nexport const CURRENCY_SYMBOL = \"$\";\n\n/**\n * Button list for SunEditor\n */\nexport const SUN_EDITOR_BUTTON_LIST = [\n  [\"font\", \"fontSize\", \"formatBlock\"],\n  [\"bold\", \"underline\", \"italic\"],\n  [\"fontColor\", \"hiliteColor\"],\n  [\"align\", \"list\", \"lineHeight\"],\n];\n\n/**\n * HiringType Select [Internal,External]\n */\nexport const HIRING_TYPE = {\n  INTERNAL: \"internal\",\n  EXTERNAL: \"external\",\n};\n\n/**\n * Skill categories\n */\nexport const SKILL_CATEGORY = {\n  Personal_Health: \"Personal Health\",\n  Social_Interaction: \"Social Interaction\",\n  Mastery_Of_Emotions: \"Mastery of Emotions\",\n  Mentality: \"Mentality\",\n  Cognitive_Abilities: \"Cognitive Abilities\",\n};\n\n/**\n * Application status values\n */\nexport const APPLICATION_STATUS = {\n  PENDING: \"Pending\",\n  APPROVED: \"Approved\",\n  REJECTED: \"Rejected\",\n  HIRED: \"Hired\",\n  ON_HOLD: \"On-Hold\",\n  FINAL_REJECT: \"Final-Reject\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport const SKILL_TYPE = {\n  ROLE: \"role\",\n  CULTURE: \"culture\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport type SkillType = (typeof SKILL_TYPE)[keyof typeof SKILL_TYPE];\n\n/**\n * HiringType key for searchParams\n */\nexport const HIRING_TYPE_KEY = \"hiringType\";\n\nexport const CURSOR_POINT = { cursor: \"pointer\" };\n\nexport const COMPLIANCE_LINK = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\n\n// Dynamic uploading messages for job generation\nexport const JOB_GENERATION_UPLOAD_MESSAGES = [\n  \"Analyzing your job description...\",\n  \"Extracting key requirements...\",\n  \"Processing document content...\",\n  \"Identifying skills and qualifications...\",\n  \"Parsing job details...\",\n  \"Almost ready...\",\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,kBAAqC;IAChD;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,uBAA0C;IACrD;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,wBAA2C;IACtD;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAS;CACrC;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAyB,OAAO;IAAsB;IAC/D;QAAE,OAAO;QAAiC,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAoB,OAAO;IAAiB;IACrD;QAAE,OAAO;QAAkC,OAAO;IAA+B;IACjF;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAkB,OAAO;IAAe;IACjD;QAAE,OAAO;QAA8B,OAAO;IAA2B;IACzE;QAAE,OAAO;QAAgC,OAAO;IAA6B;CAC9E;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAgD,OAAO;IAA+C;IAC/G;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAsC,OAAO;IAAqC;IAC3F;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAoC,OAAO;IAAmC;IACvF;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAA8C,OAAO;IAA6C;CAC5G;AAEM,MAAM,2BAA8C;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAA2B,OAAO;IAA0B;IACrE;QAAE,OAAO;QAAwB,OAAO;IAAuB;IAC/D;QAAE,OAAO;QAA0B,OAAO;IAAyB;IACnE;QAAE,OAAO;QAAmC,OAAO;IAAkC;IACrF;QAAE,OAAO;QAA8B,OAAO;IAA6B;IAC3E;QAAE,OAAO;QAAsB,OAAO;IAAqB;CAC5D;AAEM,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAIM,MAAM,kBAAkB,IAAI,OAAO,MAAM,MAAM;AAC/C,MAAM,gBAAgB,IAAI,OAAO,MAAM,MAAM;AAC7C,MAAM,YAAY;AAClB,MAAM,YAAY;AAKlB,MAAM,6BAA6B;AAKnC,MAAM,kBAAkB;AAKxB,MAAM,yBAAyB;IACpC;QAAC;QAAQ;QAAY;KAAc;IACnC;QAAC;QAAQ;QAAa;KAAS;IAC/B;QAAC;QAAa;KAAc;IAC5B;QAAC;QAAS;QAAQ;KAAa;CAChC;AAKM,MAAM,cAAc;IACzB,UAAU;IACV,UAAU;AACZ;AAKO,MAAM,iBAAiB;IAC5B,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;IACrB,WAAW;IACX,qBAAqB;AACvB;AAKO,MAAM,qBAAqB;IAChC,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,cAAc;AAChB;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;AACX;AAUO,MAAM,kBAAkB;AAExB,MAAM,eAAe;IAAE,QAAQ;AAAU;AAEzC,MAAM,kBAAkB;AAGxB,MAAM,iCAAiC;IAC5C;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/CandidateApproveRejectModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { FC, useState } from \"react\";\r\nimport Button from \"../formElements/Button\";\r\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\r\nimport InputWrapper from \"../formElements/InputWrapper\";\r\nimport Textarea from \"../formElements/Textarea\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { changeApplicationStatus } from \"@/services/screenResumeServices\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { AuthState } from \"@/redux/slices/authSlice\";\r\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\r\nimport { toastMessageSuccess, toTitleCase } from \"@/utils/helper\";\r\nimport { CandidateApplication, topCandidateApplication } from \"@/interfaces/candidatesInterface\";\r\n\r\ninterface IProps {\r\n  onClickCancel: () => void;\r\n  disabled?: boolean;\r\n  candidate?: CandidateApplication | topCandidateApplication;\r\n  onSuccess?: (candidate: CandidateApplication | topCandidateApplication, status: string) => void;\r\n}\r\n\r\nconst CandidateApproveRejectModal: FC<IProps> = ({ onClickCancel, candidate, onSuccess }) => {\r\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [error, setError] = useState(\"\");\r\n\r\n  const {\r\n    control,\r\n    handleSubmit,\r\n    formState: { errors },\r\n  } = useForm<{ reason: string }>({\r\n    defaultValues: {\r\n      reason: \"\",\r\n    },\r\n    mode: \"onSubmit\",\r\n    criteriaMode: \"firstError\",\r\n    shouldFocusError: true,\r\n    reValidateMode: \"onChange\",\r\n    resolver: (values) => {\r\n      const errors: Record<string, { type: string; message: string }> = {};\r\n\r\n      // Required validation for reason field\r\n      if (!values.reason || values.reason.trim() === \"\") {\r\n        errors.reason = {\r\n          type: \"required\",\r\n          message: \"Please provide a reason\",\r\n        };\r\n      } else if (values.reason.trim().length < 5) {\r\n        errors.reason = {\r\n          type: \"minLength\",\r\n          message: \"Reason should be at least 5 characters long\",\r\n        };\r\n      } else if (values.reason.trim().length > 50) {\r\n        errors.reason = {\r\n          type: \"maxLength\",\r\n          message: \"Reason should not exceed 50 characters\",\r\n        };\r\n      }\r\n\r\n      return {\r\n        values,\r\n        errors,\r\n      };\r\n    },\r\n  });\r\n\r\n  const submitWithStatus = async (formData: { reason: string }, status: string) => {\r\n    if (!candidate || !authData) return;\r\n\r\n    try {\r\n      setIsSubmitting(true);\r\n      setError(\"\");\r\n      const data = {\r\n        job_id: candidate.job_id,\r\n        candidate_id: candidate.candidateId,\r\n        hiring_manager_id: authData.id,\r\n        status: status,\r\n        hiring_manager_reason: formData.reason,\r\n      };\r\n\r\n      const response = await changeApplicationStatus(data);\r\n\r\n      if (response.data && response.data.success) {\r\n        toastMessageSuccess(\"Candidate status has been updated successfully!\");\r\n        // Call the onSuccess callback if provided\r\n        if (onSuccess) {\r\n          setTimeout(() => {\r\n            onClickCancel();\r\n            onSuccess(candidate, status);\r\n          }, 1500);\r\n        }\r\n      } else {\r\n        setError(response.data?.message || \"Failed to update candidate status\");\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error updating candidate status:\", err);\r\n      setError(\"An unexpected error occurred. Please try again.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleApprove = async () => {\r\n    handleSubmit((data) => {\r\n      submitWithStatus(data, APPLICATION_STATUS.APPROVED);\r\n    })();\r\n  };\r\n\r\n  const handleReject = async () => {\r\n    handleSubmit((data) => {\r\n      submitWithStatus(data, APPLICATION_STATUS.REJECTED);\r\n    })();\r\n  };\r\n\r\n  return (\r\n    <div className=\"modal theme-modal show-modal\">\r\n      <div className=\"modal-dialog modal-dialog-centered\">\r\n        <div className=\"modal-content\">\r\n          <div className=\"modal-header justify-content-center\">\r\n            <h2>Review Candidate</h2>\r\n            <p>Please review the candidate's profile and make a decision.</p>\r\n            {!isSubmitting && (\r\n              <Button className=\"modal-close-btn\" onClick={onClickCancel}>\r\n                <ModalCloseIcon />\r\n              </Button>\r\n            )}\r\n          </div>\r\n          <div className=\"modal-body\">\r\n            {/* qualification-card */}\r\n            <div className=\"qualification-card\">\r\n              <div className=\"qualification-card-top\">\r\n                <div className=\"name\">\r\n                  <h3>{toTitleCase(candidate?.candidateName || \"Candidate\")}</h3>\r\n                  <p>{candidate?.aiDecision || \"Pending\"} by S9 Interviews</p>\r\n                </div>\r\n                <div className=\"top-right\">\r\n                  <div className=\"on-hold-status\">\r\n                    <p>{candidate?.applicationStatus}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"qualification-card-mid\">\r\n                <p>\r\n                  <b>Reasons why they are a good match:</b>\r\n                </p>\r\n                <p>{candidate?.aiReason || \"No reason provided by AI evaluation.\"}</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Decision selection moved to the submit buttons */}\r\n\r\n            <form>\r\n              <InputWrapper>\r\n                <InputWrapper.Label htmlFor=\"reason\" required>\r\n                  Reason\r\n                </InputWrapper.Label>\r\n                <Textarea rows={6} name=\"reason\" control={control} placeholder=\"Enter your reason here\" className=\"form-control\" />\r\n                {errors.reason && <p className=\"text-danger mt-1\">{errors.reason.message as string}</p>}\r\n              </InputWrapper>\r\n\r\n              {error && <div className=\"error-message alert alert-danger my-3\">{error}</div>}\r\n\r\n              <div className=\"action-btn gap-3 mt-4\">\r\n                {isSubmitting ? (\r\n                  <div className=\"text-center w-100\">\r\n                    <div className=\"spinner-border text-primary\" role=\"status\">\r\n                      <span className=\"visually-hidden\">Submitting...</span>\r\n                    </div>\r\n                    <p className=\"mt-2\">Submitting...</p>\r\n                  </div>\r\n                ) : (\r\n                  <>\r\n                    <Button type=\"button\" className=\"primary-btn rounded-md w-100\" onClick={handleApprove}>\r\n                      Approve Candidate\r\n                    </Button>\r\n                    <Button type=\"button\" className=\"danger-btn rounded-md w-100\" onClick={handleReject}>\r\n                      Reject Candidate\r\n                    </Button>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateApproveRejectModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAXA;;;;;;;;;;;;AAqBA,MAAM,8BAA0C,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE;IACtF,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,eAAe;YACb,QAAQ;QACV;QACA,MAAM;QACN,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,UAAU,CAAC;YACT,MAAM,SAA4D,CAAC;YAEnE,uCAAuC;YACvC,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,OAAO,IAAI;gBACjD,OAAO,MAAM,GAAG;oBACd,MAAM;oBACN,SAAS;gBACX;YACF,OAAO,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;gBAC1C,OAAO,MAAM,GAAG;oBACd,MAAM;oBACN,SAAS;gBACX;YACF,OAAO,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;gBAC3C,OAAO,MAAM,GAAG;oBACd,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,OAAO;gBACL;gBACA;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO,UAA8B;QAC5D,IAAI,CAAC,aAAa,CAAC,UAAU;QAE7B,IAAI;YACF,gBAAgB;YAChB,SAAS;YACT,MAAM,OAAO;gBACX,QAAQ,UAAU,MAAM;gBACxB,cAAc,UAAU,WAAW;gBACnC,mBAAmB,SAAS,EAAE;gBAC9B,QAAQ;gBACR,uBAAuB,SAAS,MAAM;YACxC;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,0BAAuB,AAAD,EAAE;YAE/C,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBAC1C,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;gBACpB,0CAA0C;gBAC1C,IAAI,WAAW;oBACb,WAAW;wBACT;wBACA,UAAU,WAAW;oBACvB,GAAG;gBACL;YACF,OAAO;gBACL,SAAS,SAAS,IAAI,EAAE,WAAW;YACrC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa,CAAC;YACZ,iBAAiB,MAAM,0IAAA,CAAA,qBAAkB,CAAC,QAAQ;QACpD;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,CAAC;YACZ,iBAAiB,MAAM,0IAAA,CAAA,qBAAkB,CAAC,QAAQ;QACpD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;4BACF,CAAC,8BACA,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;0CAC3C,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAI,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,WAAW,iBAAiB;;;;;;kEAC7C,8OAAC;;4DAAG,WAAW,cAAc;4DAAU;;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAG,WAAW;;;;;;;;;;;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DACC,cAAA,8OAAC;8DAAE;;;;;;;;;;;0DAEL,8OAAC;0DAAG,WAAW,YAAY;;;;;;;;;;;;;;;;;;0CAM/B,8OAAC;;kDACC,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAS,QAAQ;0DAAC;;;;;;0DAG9C,8OAAC,8IAAA,CAAA,UAAQ;gDAAC,MAAM;gDAAG,MAAK;gDAAS,SAAS;gDAAS,aAAY;gDAAyB,WAAU;;;;;;4CACjG,OAAO,MAAM,kBAAI,8OAAC;gDAAE,WAAU;0DAAoB,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;oCAGzE,uBAAS,8OAAC;wCAAI,WAAU;kDAAyC;;;;;;kDAElE,8OAAC;wCAAI,WAAU;kDACZ,6BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA8B,MAAK;8DAChD,cAAA,8OAAC;wDAAK,WAAU;kEAAkB;;;;;;;;;;;8DAEpC,8OAAC;oDAAE,WAAU;8DAAO;;;;;;;;;;;iEAGtB;;8DACE,8OAAC,4IAAA,CAAA,UAAM;oDAAC,MAAK;oDAAS,WAAU;oDAA+B,SAAS;8DAAe;;;;;;8DAGvF,8OAAC,4IAAA,CAAA,UAAM;oDAAC,MAAK;oDAAS,WAAU;oDAA8B,SAAS;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzG;uCAEe", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/ArchiveCandidateModal.tsx"], "sourcesContent": ["\"use client\";\nimport { FC, useState } from \"react\";\nimport Button from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport InputWrapper from \"../formElements/InputWrapper\";\nimport Textarea from \"../formElements/Textarea\";\nimport { useForm } from \"react-hook-form\";\nimport { useSelector } from \"react-redux\";\nimport { AuthState } from \"@/redux/slices/authSlice\";\n\ninterface ArchiveCandidateModalProps {\n  onClickCancel: () => void;\n  applicationId: number;\n  jobId: number;\n  onSuccess?: (reason: string) => void;\n}\n\nconst ArchiveCandidateModal: FC<ArchiveCandidateModalProps> = ({ onClickCancel, onSuccess }) => {\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState(\"\");\n  const {\n    control,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<{ reason: string }>({\n    defaultValues: { reason: \"\" },\n    mode: \"onSubmit\",\n    criteriaMode: \"firstError\",\n    shouldFocusError: true,\n    reValidateMode: \"onChange\",\n    resolver: (values) => {\n      const errors: Record<string, { type: string; message: string }> = {};\n      if (!values.reason || values.reason.trim() === \"\") {\n        errors.reason = { type: \"required\", message: \"Please provide a reason\" };\n      }\n      if (values.reason.trim().length < 5) {\n        errors.reason = { type: \"minLength\", message: \"Reason should be at least 5 characters long\" };\n      } else if (values.reason.trim().length > 50) {\n        errors.reason = { type: \"maxLength\", message: \"Reason should not exceed 50 characters\" };\n      }\n      return { values, errors };\n    },\n  });\n\n  const onSubmit = async (data: { reason: string }) => {\n    if (!authData) return;\n    try {\n      setIsSubmitting(true);\n      setError(\"\");\n      if (onSuccess) await onSuccess(data.reason);\n    } catch (err) {\n      console.error(\"Archive candidate error:\", err);\n      setError(\"An unexpected error occurred. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"modal theme-modal show-modal\">\n      <div className=\"modal-dialog modal-dialog-centered\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-between pb-0 \">\n            <h2 className=\"m-0\">Archive Candidate</h2>\n            <Button className=\"modal-close-btn\" onClick={onClickCancel} disabled={isSubmitting}>\n              <ModalCloseIcon />\n            </Button>\n          </div>\n          <div className=\"modal-body\">\n            <form onSubmit={handleSubmit(onSubmit)}>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"reason\" required>\n                  Please provide a reason\n                </InputWrapper.Label>\n                <Textarea rows={5} name=\"reason\" control={control} placeholder=\"Enter reason for archiving candidate\" className=\"form-control\" />\n                <InputWrapper.Error message={errors?.reason?.message || error} />\n              </InputWrapper>\n\n              {/* {error && <div className=\"alert alert-danger my-3\">{error}</div>} */}\n\n              <div className=\"d-flex justify-content-end gap-2 mt-4\">\n                <Button type=\"button\" className=\"secondary-btn rounded-md w-100\" onClick={onClickCancel} disabled={isSubmitting}>\n                  Cancel\n                </Button>\n                <Button type=\"submit\" className=\"primary-btn rounded-md w-100\" disabled={isSubmitting}>\n                  {isSubmitting ? \"Archiving...\" : \"Archive\"}\n                </Button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ArchiveCandidateModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AAiBA,MAAM,wBAAwD,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE;IACzF,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,eAAe;YAAE,QAAQ;QAAG;QAC5B,MAAM;QACN,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,UAAU,CAAC;YACT,MAAM,SAA4D,CAAC;YACnE,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,OAAO,IAAI;gBACjD,OAAO,MAAM,GAAG;oBAAE,MAAM;oBAAY,SAAS;gBAA0B;YACzE;YACA,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;gBACnC,OAAO,MAAM,GAAG;oBAAE,MAAM;oBAAa,SAAS;gBAA8C;YAC9F,OAAO,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;gBAC3C,OAAO,MAAM,GAAG;oBAAE,MAAM;oBAAa,SAAS;gBAAyC;YACzF;YACA,OAAO;gBAAE;gBAAQ;YAAO;QAC1B;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,UAAU;QACf,IAAI;YACF,gBAAgB;YAChB,SAAS;YACT,IAAI,WAAW,MAAM,UAAU,KAAK,MAAM;QAC5C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAM;;;;;;0CACpB,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;gCAAe,UAAU;0CACpE,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,UAAU,aAAa;;8CAC3B,8OAAC,kJAAA,CAAA,UAAY;;sDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAS,QAAQ;sDAAC;;;;;;sDAG9C,8OAAC,8IAAA,CAAA,UAAQ;4CAAC,MAAM;4CAAG,MAAK;4CAAS,SAAS;4CAAS,aAAY;4CAAuC,WAAU;;;;;;sDAChH,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAS,QAAQ,QAAQ,WAAW;;;;;;;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;4CAAiC,SAAS;4CAAe,UAAU;sDAAc;;;;;;sDAGjH,8OAAC,4IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;4CAA+B,UAAU;sDACtE,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;uCAEe", "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1160, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/CandidatesServices/candidatesApplicationServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\n\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport {\n  AdditionalInfoPayload,\n  CandidateApplication,\n  CandidateProfileResponse,\n  FetchCandidatesParams,\n  ICandidateInterviewHistory,\n  IFinalAssessment,\n  ISkillSpecificAssessment,\n  PromoteDemotePayload,\n  topCandidateApplication,\n} from \"@/interfaces/candidatesInterface\";\n\n// ============================================================================\n// CANDIDATE APPLICATION MANAGEMENT\n// ============================================================================\n\n/**\n * Fetches candidates with their job applications using advanced filtering and pagination\n *\n * This function retrieves a paginated list of candidates along with their application details.\n * It supports comprehensive filtering options including job-specific filtering, name search,\n * and active/archived status filtering.\n *\n * @async\n * @function fetchCandidatesApplications\n * @param {FetchCandidatesParams} data - Query parameters for filtering and pagination\n * @param {number} [data.page] - Page offset for pagination (0-based)\n * @param {number} [data.limit] - Maximum number of candidates to return per page\n * @param {string} [data.searchStr] - Search string to filter candidates by name\n * @param {boolean} [data.isActive] - Filter by status: true for active, false for archived\n * @param {number} [data.jobId] - Optional job ID to filter candidates for specific position\n * @returns {Promise<ApiResponse<CandidateApplication[]>>} Promise resolving to candidate applications list\n *\n */\nexport const fetchCandidatesApplications = (data: FetchCandidatesParams): Promise<ApiResponse<CandidateApplication[]>> => {\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATES_WITH_APPLICATIONS, { ...data });\n};\n\n/**\n * Fetches top-ranked candidates with their applications for a specific job\n *\n * This function retrieves candidates who have been identified as top performers\n * based on AI scoring, ATS evaluation, and other ranking criteria. The results\n * are pre-filtered and sorted by the backend ranking algorithm.\n *\n * @async\n * @function fetchTopCandidatesApplications\n * @param {number} [jobId] - Optional job ID to filter top candidates for specific position\n * @returns {Promise<ApiResponse<topCandidateApplication[]>>} Promise resolving to top candidates list\n */\nexport const fetchTopCandidatesApplications = (jobId?: number): Promise<ApiResponse<topCandidateApplication[]>> => {\n  return http.get(endpoint.candidatesApplication.GET_TOP_CANDIDATES_WITH_APPLICATIONS, {\n    jobId,\n  });\n};\n\n/**\n * Promotes or demotes a candidate in the application ranking\n *\n * This function allows hiring managers to manually adjust candidate rankings\n * by promoting high-potential candidates or demoting those who don't meet\n * expectations. The action affects the candidate's visibility and priority.\n *\n * @async\n * @function promoteDemoteCandidate\n * @param {PromoteDemotePayload} payload - The promotion/demotion request data\n * @param {number} payload.candidateId - ID of the candidate to promote/demote\n * @param {number} payload.applicationId - ID of the specific application\n * @param {\"Promoted\" | \"Demoted\"} payload.action - Action to perform\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\n */\nexport const promoteDemoteCandidate = async (payload: PromoteDemotePayload): Promise<ApiResponse<null>> => {\n  return await http.put(endpoint.candidatesApplication.PROMOTE_DEMOTE_CANDIDATE, payload);\n};\n\n/**\n * Adds additional information to a candidate's application\n *\n * This function allows candidates or hiring managers to submit supplementary\n * information, documents, or clarifications to an existing application.\n * Commonly used for portfolio submissions, additional certifications, or\n * responses to specific questions.\n *\n * @async\n * @function addApplicantAdditionalInfo\n * @param {AdditionalInfoPayload} payload - The additional information data\n * @param {string} payload.applicationId - ID of the application to update\n * @param {string} payload.description - Description or text content\n * @param {string} payload.images - Image URLs or file references\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\n */\nexport const addApplicantAdditionalInfo = async (payload: AdditionalInfoPayload): Promise<ApiResponse<null>> => {\n  return await http.post(endpoint.candidatesApplication.ADDITIONAL_INFO, payload);\n};\n\n// ============================================================================\n// CANDIDATE PROFILE MANAGEMENT\n// ============================================================================\n\n/**\n * Fetches comprehensive candidate profile details by candidate ID\n *\n * This function retrieves detailed information about a specific candidate including\n * personal details, job application status, assigned interviewer information,\n * resume links, and current round information. Essential for candidate profile views.\n *\n * @async\n * @function fetchCandidateProfile\n * @param {number | string} candidateId - The unique identifier of the candidate\n * @returns {Promise<ApiResponse<CandidateProfileResponse>>} Promise resolving to candidate profile data\n */\nexport const fetchCandidateProfile = (jobApplicationId: number | string): Promise<ApiResponse<CandidateProfileResponse>> => {\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATE_DETAILS, { jobApplicationId });\n};\n\n/**\n * Updates the job application status for hire/reject decisions\n *\n * This function allows hiring managers to make final decisions on candidate applications\n * by updating the status to either \"Hired\" or \"Final-Reject\". This action typically\n * triggers workflow notifications and updates the candidate's status across the system.\n *\n * @async\n * @function updateJobApplicationStatus\n * @param {number} jobApplicationId - The unique identifier of the job application\n * @param {string} status - The new status (\"Hired\" or \"Final-Reject\")\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\n * ```\n */\nexport const updateJobApplicationStatus = async (jobApplicationId: number, status: string): Promise<ApiResponse<null>> => {\n  return await http.put(endpoint.candidatesApplication.UPDATE_JOB_APPLICATION_STATUS.replace(\":jobApplicationId\", jobApplicationId.toString()), {\n    status,\n  });\n};\n\n/**\n * Retrieves comprehensive interview history for a specific candidate\n *\n * This function fetches detailed interview records across all rounds including\n * interviewer information, skill scores, hard skill marks, interview summaries,\n * and AI-powered performance analysis. Essential for tracking candidate progress.\n *\n * @async\n * @function getCandidateInterviewHistory\n * @param {number} candidateId - The unique identifier of the candidate\n * @returns {Promise<ApiResponse<ICandidateInterviewHistory[]>>} Promise resolving to interview history array\n */\nexport const getCandidateInterviewHistory = async (applicationId: string): Promise<ApiResponse<ICandidateInterviewHistory[]>> => {\n  return await http.get(endpoint.candidatesApplication.GET_CANDIDATE_INTERVIEW_HISTORY.replace(\":applicationId\", applicationId));\n};\n\n/**\n * Retrieves comprehensive final assessment summary for a specific candidate application\n *\n * This function fetches the complete final assessment analysis generated after all\n * interview rounds are completed. It includes AI-powered insights, success probability\n * calculations, skill summaries, and personalized development recommendations.\n *\n * @async\n * @function getApplicationFinalSummary\n * @param {string} candidateId - The unique identifier of the candidate\n * @returns {Promise<ApiResponse<IFinalAssessment>>} Promise resolving to final assessment data\n *\n * Assessment Data Includes:\n * - Overall success probability percentage (0-100)\n * - Comprehensive skill summary with AI analysis\n * - Personalized development recommendations by category\n * - Job application reference details\n * ```\n */\nexport const getApplicationFinalSummary = async (jobApplicationId: string): Promise<ApiResponse<IFinalAssessment>> => {\n  return await http.get(endpoint.candidatesApplication.GET_APPLICATION_FINAL_SUMMARY.replace(\":jobApplicationId\", jobApplicationId));\n};\n\n/**\n * Retrieves detailed skill-specific assessment data for a candidate\n *\n * This function fetches comprehensive skill evaluation data aggregated from all\n * completed interview rounds. The data is flattened and optimized for frontend\n * consumption, providing detailed insights into each skill area evaluated.\n *\n * @async\n * @function getApplicationSkillScoreData\n * @param {string} candidateId - The unique identifier of the candidate\n * @returns {Promise<ApiResponse<ISkillSpecificAssessment>>} Promise resolving to skill assessment data\n *\n * Skill Data Includes:\n * - Individual skill scores and marks (0-10 scale)\n * - Skill-specific strengths and achievements\n * - Identified potential gaps and improvement areas\n * - Success probability for each skill area\n * - Overall career-based skills score\n * - Interviewer-specific evaluations and feedback\n */\nexport const getApplicationSkillScoreData = async (jobApplicationId: string): Promise<ApiResponse<ISkillSpecificAssessment>> => {\n  return await http.get(endpoint.candidatesApplication.GET_APPLICATION_SKILL_SCORE_DATA.replace(\":jobApplicationId\", jobApplicationId));\n};\n\n/**\n * Generates final summary for a candidate application\n *\n * This function triggers the generation of a comprehensive final summary for a candidate\n * based on their interview performance, skill assessments, and overall evaluation data.\n * The summary includes AI-powered insights and recommendations for hiring decisions.\n *\n * @async\n * @function generateFinalSummary\n * @param {number | string} candidateId - The unique identifier of the candidate\n * @param {number | string} jobApplicationId - The unique identifier of the job application\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\n *\n * Summary Generation Includes:\n * - Comprehensive analysis of interview performance across all rounds\n * - Skill-specific evaluation and scoring aggregation\n * - AI-powered insights and recommendations\n * - Overall success probability calculation\n * - Personalized development recommendations\n */\nexport const generateFinalSummary = async (jobApplicationId: number | string): Promise<ApiResponse<null>> => {\n  return await http.get(endpoint.candidatesApplication.GENERATE_FINAL_SUMMARY, {\n    jobApplicationId: jobApplicationId.toString(),\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAqCO,MAAM,8BAA8B,CAAC;IAC1C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,gCAAgC,EAAE;QAAE,GAAG,IAAI;IAAC;AAC7F;AAcO,MAAM,iCAAiC,CAAC;IAC7C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,oCAAoC,EAAE;QACnF;IACF;AACF;AAiBO,MAAM,yBAAyB,OAAO;IAC3C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,wBAAwB,EAAE;AACjF;AAkBO,MAAM,6BAA6B,OAAO;IAC/C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,eAAe,EAAE;AACzE;AAkBO,MAAM,wBAAwB,CAAC;IACpC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,qBAAqB,EAAE;QAAE;IAAiB;AAC3F;AAgBO,MAAM,6BAA6B,OAAO,kBAA0B;IACzE,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,qBAAqB,iBAAiB,QAAQ,KAAK;QAC5I;IACF;AACF;AAcO,MAAM,+BAA+B,OAAO;IACjD,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,+BAA+B,CAAC,OAAO,CAAC,kBAAkB;AACjH;AAqBO,MAAM,6BAA6B,OAAO;IAC/C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,qBAAqB;AAClH;AAsBO,MAAM,+BAA+B,OAAO;IACjD,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,gCAAgC,CAAC,OAAO,CAAC,qBAAqB;AACrH;AAsBO,MAAM,uBAAuB,OAAO;IACzC,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,sBAAsB,EAAE;QAC3E,kBAAkB,iBAAiB,QAAQ;IAC7C;AACF", "debugId": null}}, {"offset": {"line": 1216, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/CandidatesServices/candidatesApplicationStatusUpdateService.ts"], "sourcesContent": ["// candidatesApplicationServices.ts\n\nimport endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { UpdateCandidateApplicationStatusResponse } from \"@/interfaces/jobApplicationInterface\";\n\n/**\n * Archive or unarchive a candidate application\n */\nexport const archiveActiveApplication = (\n  applicationId: number,\n  isActive: boolean,\n  reason?: string\n): Promise<ApiResponse<UpdateCandidateApplicationStatusResponse>> => {\n  return http.put(endpoint.candidatesApplication.ARCHIVE_ACTIVE_APPLICATION.replace(\":applicationId\", applicationId.toString()), {\n    isActive,\n    reason,\n  });\n};\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;AAEnC;AACA;;;AAOO,MAAM,2BAA2B,CACtC,eACA,UACA;IAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,OAAO,CAAC,kBAAkB,cAAc,QAAQ,KAAK;QAC7H;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard__stats_header\": \"commonPage-module-scss-module__em0r7a__dashboard__stats_header\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/screenResumeConstant.ts"], "sourcesContent": ["export const GENDER_OPTIONS = [\n  { value: \"Male\", label: \"Male\" },\n  { value: \"Female\", label: \"Female\" },\n];\n\nexport enum InterviewTabType {\n  UPCOMING = \"UpcomingInterviews\",\n  PAST = \"PastInterviews\",\n}\n\nexport const APPLICATION_UPDATE_STATUS = {\n  PROMOTED: \"Promoted\",\n  DEMOTED: \"Demoted\",\n};\n\nexport type APPLICATION_UPDATE_STATUS = (typeof APPLICATION_UPDATE_STATUS)[keyof typeof APPLICATION_UPDATE_STATUS];\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAS;CACpC;AAEM,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,MAAM,4BAA4B;IACvC,UAAU;IACV,SAAS;AACX", "debugId": null}}, {"offset": {"line": 1312, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1318, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/skeletons/TableSkeleton.tsx"], "sourcesContent": ["import React from \"react\";\nimport Skeleton from \"react-loading-skeleton\";\nimport \"react-loading-skeleton/dist/skeleton.css\";\n\nconst TableSkeleton = ({ rows = 3, cols = 3, colWidths = \"120,80,100\" }) => {\n  const columnWidths = colWidths.split(\",\").map((w) => w.trim());\n\n  return (\n    <tbody>\n      {[...Array(rows)].map((_, rowIndex) => (\n        <tr key={`loader-row-${rowIndex}`}>\n          {[...Array(cols)].map((_, colIndex) => (\n            <td key={`loader-col-${colIndex}`} className=\"text-center\">\n              <Skeleton width={columnWidths[colIndex] || 80} height={20} circle={false} />\n            </td>\n          ))}\n        </tr>\n      ))}\n    </tbody>\n  );\n};\n\nexport default TableSkeleton;\n"], "names": [], "mappings": ";;;;AACA;;;;AAGA,MAAM,gBAAgB,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,YAAY,YAAY,EAAE;IACrE,MAAM,eAAe,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;IAE3D,qBACE,8OAAC;kBACE;eAAI,MAAM;SAAM,CAAC,GAAG,CAAC,CAAC,GAAG,yBACxB,8OAAC;0BACE;uBAAI,MAAM;iBAAM,CAAC,GAAG,CAAC,CAAC,GAAG,yBACxB,8OAAC;wBAAkC,WAAU;kCAC3C,cAAA,8OAAC,6JAAA,CAAA,UAAQ;4BAAC,OAAO,YAAY,CAAC,SAAS,IAAI;4BAAI,QAAQ;4BAAI,QAAQ;;;;;;uBAD5D,CAAC,WAAW,EAAE,UAAU;;;;;eAF5B,CAAC,WAAW,EAAE,UAAU;;;;;;;;;;AAUzC;uCAEe", "debugId": null}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1373, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/fullPageLoader.gif.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 1200, height: 857, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1386, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonComponent/FullPageLoader.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport fullPageLoaderGif from \"../../../public/assets/fullPageLoader.gif\";\n\nconst FullPageLoader = () => {\n  return (\n    <div\n      className=\"full-page-loader\"\n      style={{\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        width: \"100%\",\n        height: \"100%\",\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        zIndex: 9999,\n      }}\n    >\n      <Image src={fullPageLoaderGif} alt=\"Loading...\" />\n    </div>\n  );\n};\n\nexport default FullPageLoader;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,iBAAiB;IACrB,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,UAAU;YACV,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,iBAAiB;YACjB,SAAS;YACT,gBAAgB;YAChB,YAAY;YACZ,QAAQ;QACV;kBAEA,cAAA,8OAAC,6HAAA,CAAA,UAAK;YAAC,KAAK,oSAAA,CAAA,UAAiB;YAAE,KAAI;;;;;;;;;;;AAGzC;uCAEe", "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1437, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/resume/CandidatesList.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n\"use client\";\r\n\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport dayjs from \"dayjs\";\r\n// import { useForm } from \"react-hook-form\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useSelector } from \"react-redux\";\r\n// import InfiniteScroll from \"react-infinite-scroll-component\";\r\n\r\nimport Button from \"@/components/formElements/Button\";\r\n// import InputWrapper from \"@/components/formElements/InputWrapper\";\r\n// import Textbox from \"@/components/formElements/Textbox\";\r\n// import SearchIcon from \"@/components/svgComponents/SearchIcon\";\r\nimport ThreeDotsIcon from \"@/components/svgComponents/ThreeDotsIcon\";\r\nimport CandidateApproveRejectModal from \"@/components/commonModals/CandidateApproveRejectModal\";\r\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\r\nimport ArchiveCandidateModal from \"@/components/commonModals/ArchiveCandidateModal\"; //\r\n\r\n// Services\r\nimport {\r\n  fetchCandidatesApplications,\r\n  fetchTopCandidatesApplications,\r\n  promoteDemoteCandidate,\r\n} from \"@/services/CandidatesServices/candidatesApplicationServices\";\r\nimport { archiveActiveApplication } from \"@/services/CandidatesServices/candidatesApplicationStatusUpdateService\";\r\n\r\nimport { AuthState } from \"@/redux/slices/authSlice\";\r\nimport { DEFAULT_LIMIT, PERMISSION } from \"@/constants/commonConstants\";\r\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\r\n\r\nimport style from \"@/styles/commonPage.module.scss\";\r\nimport { useTranslations } from \"use-intl\";\r\nimport { CandidateApplication, PromoteDemotePayload, topCandidateApplication } from \"@/interfaces/candidatesInterface\";\r\nimport \"react-loading-skeleton/dist/skeleton.css\";\r\nimport InfiniteScroll from \"react-infinite-scroll-component\";\r\nimport { APPLICATION_UPDATE_STATUS } from \"@/constants/screenResumeConstant\";\r\nimport { toastMessageError, toTitleCase } from \"@/utils/helper\";\r\nimport TableSkeleton from \"../skeletons/TableSkeleton\";\r\nimport ROUTES from \"@/constants/routes\";\r\nimport { debounce } from \"lodash\";\r\nimport FullPageLoader from \"@/components/commonComponent/FullPageLoader\";\r\n\r\nconst CandidatesList = ({\r\n  params,\r\n  searchParams,\r\n}: {\r\n  params: Promise<{ jobId: string }>;\r\n  searchParams: Promise<{ title: string; jobUniqueId: string }>;\r\n}) => {\r\n  // const { control } = useForm();\r\n  const userData = useSelector((state: { auth: AuthState }) => state.auth.authData);\r\n  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];\r\n  const hasArchiveRestoreCandidatesPermission = userPermissions.includes(PERMISSION.ARCHIVE_RESTORE_CANDIDATES);\r\n  const hasManualResumeScreeningPermission = userPermissions.includes(PERMISSION.MANUAL_RESUME_SCREENING);\r\n  const hasEditScheduledInterviewsPermission = userPermissions.includes(PERMISSION.EDIT_SCHEDULED_INTERVIEWS);\r\n  const hasAddAdditionalCandidateInfoPermission = userPermissions.includes(PERMISSION.ADD_ADDITIONAL_CANDIDATE_INFO);\r\n  const hasManageTopCandidatesPermission = userPermissions.includes(PERMISSION.MANAGE_TOP_CANDIDATES);\r\n\r\n  const router = useRouter();\r\n\r\n  const paramsPromise = React.use(params);\r\n  const searchParamsPromise = React.use(searchParams);\r\n\r\n  // State for candidates data\r\n  const [candidates, setCandidates] = useState<CandidateApplication[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [loader, setLoader] = useState(false);\r\n  const [hasMore, setHasMore] = useState(true);\r\n  const [offset, setOffset] = useState(0);\r\n  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);\r\n  const [selectedCandidate, setSelectedCandidate] = useState<CandidateApplication | topCandidateApplication | null>(null);\r\n  const [showReviewModal, setShowReviewModal] = useState(false);\r\n  const [showArchiveModal, setShowArchiveModal] = useState(false); //\r\n  const [searchStr] = useState(\"\");\r\n  const [topCandidates, setTopCandidates] = useState<topCandidateApplication[]>([]);\r\n  const [disable, setDisable] = useState(false);\r\n  const t = useTranslations();\r\n  const dropdownRefs = React.useRef<{ [key: string]: HTMLUListElement | null }>({});\r\n  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);\r\n\r\n  const [loadingFullScreen, setFullScreenLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (!Number(paramsPromise.jobId) || !searchParamsPromise.title) {\r\n      router.push(ROUTES.DASHBOARD);\r\n    }\r\n  }, [paramsPromise.jobId]);\r\n\r\n  // const observer = useRef<IntersectionObserver | null>(null);\r\n\r\n  const fetchTopCandidates = async () => {\r\n    if (!userData?.orgId || !paramsPromise?.jobId) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await fetchTopCandidatesApplications(Number(paramsPromise.jobId));\r\n      if (response?.data?.success) {\r\n        setTopCandidates(response.data.data);\r\n      } else {\r\n        setTopCandidates([]);\r\n      }\r\n    } catch {\r\n      setTopCandidates([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePromoteDemoteCandidate = async (candidate: CandidateApplication | topCandidateApplication, action: APPLICATION_UPDATE_STATUS) => {\r\n    setDisable(true);\r\n    setFullScreenLoading(true);\r\n    try {\r\n      const payload = {\r\n        candidateId: candidate.candidateId,\r\n        applicationId: candidate.applicationId,\r\n        action,\r\n      };\r\n\r\n      const response = await promoteDemoteCandidate(payload as PromoteDemotePayload);\r\n\r\n      if (response?.data?.success) {\r\n        if (action === APPLICATION_UPDATE_STATUS.PROMOTED) {\r\n          // Candidate is currently in the 'other candidates' list\r\n          const fromOther = candidate as CandidateApplication;\r\n\r\n          // Remove from other candidates\r\n          setCandidates((prev) => prev.filter((c) => c.applicationId !== fromOther.applicationId));\r\n\r\n          // Add to top candidates\r\n          const promoted: topCandidateApplication = {\r\n            candidateName: fromOther.candidateName,\r\n            applicationCreatedTs: fromOther.applicationCreatedTs,\r\n            atsScore: fromOther.atsScore,\r\n            applicationId: fromOther.applicationId,\r\n            applicationRankStatus: APPLICATION_UPDATE_STATUS.PROMOTED,\r\n            candidateId: fromOther.candidateId,\r\n            aiReason: fromOther.aiReason,\r\n            aiDecision: fromOther.aiDecision,\r\n            applicationStatus: fromOther.applicationStatus,\r\n            hiringManagerReason: fromOther.hiringManagerReason,\r\n            applicationUpdatedTs: new Date().toISOString(),\r\n            applicationSource: fromOther.applicationSource || \"\", // Ensure this is set\r\n            job_id: fromOther.job_id || 0, // replace if you have this info\r\n            isTopApplication: fromOther.isTopApplication || false,\r\n          };\r\n\r\n          setTopCandidates((prev) => [...prev, promoted]);\r\n        } else if (action === APPLICATION_UPDATE_STATUS.DEMOTED) {\r\n          // Candidate is currently in the 'top candidates' list\r\n          const fromTop = candidate as topCandidateApplication;\r\n\r\n          // Remove from top candidates\r\n          setTopCandidates((prev) => prev.filter((c) => c.applicationId !== fromTop.applicationId));\r\n\r\n          // Add to other candidates\r\n          const demoted: CandidateApplication = {\r\n            candidateId: fromTop.candidateId,\r\n            candidateName: fromTop.candidateName,\r\n            applicationId: fromTop.applicationId,\r\n            applicationStatus: fromTop.applicationStatus, // Preserve the original status\r\n            applicationSource: fromTop.applicationSource || \"\",\r\n            applicationCreatedTs: fromTop.applicationCreatedTs,\r\n            applicationUpdatedTs: new Date().toISOString(),\r\n            isActive: true,\r\n            job_id: fromTop.job_id || 0,\r\n            hiring_manager_id: 0,\r\n            hiringManagerReason: fromTop.hiringManagerReason || \"\",\r\n            applicationRankStatus: APPLICATION_UPDATE_STATUS.DEMOTED,\r\n            atsScore: fromTop.atsScore,\r\n            aiReason: fromTop.aiReason,\r\n            aiDecision: fromTop.aiDecision,\r\n          };\r\n\r\n          setCandidates((prev) => [...prev, demoted]);\r\n        }\r\n      } else {\r\n        toastMessageError(t(response?.data?.message));\r\n      }\r\n    } catch {\r\n    } finally {\r\n      setActiveDropdown(null);\r\n      setDisable(false);\r\n      setFullScreenLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchMoreCandidatesApplications = useCallback(async (currentOffset = 0, reset = false, searchStr: string = \"\") => {\r\n    if (!userData?.orgId) return;\r\n    setLoader(true);\r\n    try {\r\n      const response = await fetchCandidatesApplications({\r\n        page: currentOffset,\r\n        limit: DEFAULT_LIMIT,\r\n        searchStr: searchStr,\r\n        isActive: true,\r\n        jobId: Number(paramsPromise.jobId),\r\n      });\r\n\r\n      console.log(\"response\", response);\r\n      if (response?.data?.success) {\r\n        const newCandidates: CandidateApplication[] = response.data?.data?.data;\r\n\r\n        console.log(\"newCandidates\", newCandidates);\r\n\r\n        setCandidates((prev) => (reset ? newCandidates : [...prev, ...newCandidates]));\r\n        if (newCandidates.length < DEFAULT_LIMIT) {\r\n          setHasMore(false);\r\n        } else {\r\n          setHasMore(true);\r\n        }\r\n        setOffset(currentOffset + newCandidates.length);\r\n      } else {\r\n        setHasMore(false);\r\n      }\r\n    } catch {\r\n      setHasMore(false);\r\n    } finally {\r\n      setLoader(false);\r\n    }\r\n  }, []);\r\n\r\n  const loadMoreCandidates = () => {\r\n    console.log(\"loadMoreCandidates called=================>\");\r\n    if (!loader && hasMore) fetchMoreCandidatesApplications(offset, false, searchStr);\r\n  };\r\n\r\n  // const lastElementRef = useCallback(\r\n  //   (node: HTMLElement | null) => {\r\n  //     if (loading) return;\r\n  //     if (observer.current) observer.current.disconnect();\r\n  //     observer.current = new IntersectionObserver((entries) => {\r\n  //       if (entries[0].isIntersecting && hasMore) loadMoreCandidates();\r\n  //     });\r\n  //     if (node) observer.current.observe(node);\r\n  //   },\r\n  //   [loading, hasMore, offset]\r\n  // );\r\n\r\n  useEffect(() => {\r\n    if (userData?.id && userData?.orgId) fetchTopCandidates();\r\n  }, [userData?.id, userData?.orgId]);\r\n\r\n  const debouncedHandleSearchInputChange = useCallback(\r\n    debounce(() => {\r\n      if (userData?.id && userData?.orgId) fetchMoreCandidatesApplications(0, true, searchStr);\r\n    }, 1500),\r\n    [userData?.id, userData?.orgId, searchStr, fetchMoreCandidatesApplications]\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (userData?.id && userData?.orgId) debouncedHandleSearchInputChange();\r\n    return () => {\r\n      debouncedHandleSearchInputChange.cancel();\r\n    };\r\n  }, [userData?.id, userData?.orgId, debouncedHandleSearchInputChange]);\r\n\r\n  const handleArchiveCandidate = (candidate: CandidateApplication | topCandidateApplication) => {\r\n    setSelectedCandidate(candidate);\r\n    setShowArchiveModal(true);\r\n    setActiveDropdown(null);\r\n  };\r\n\r\n  const handleReviewCandidate = (candidate: CandidateApplication | topCandidateApplication) => {\r\n    setSelectedCandidate(candidate);\r\n    setShowReviewModal(true);\r\n    setActiveDropdown(null);\r\n  };\r\n\r\n  const onCancelReviewModal = () => {\r\n    setShowReviewModal(false);\r\n    setSelectedCandidate(null);\r\n  };\r\n\r\n  const onSubmitArchiveReason = async (reason: string) => {\r\n    if (!selectedCandidate) return;\r\n    try {\r\n      await archiveActiveApplication(selectedCandidate.applicationId, false, reason);\r\n      if (selectedCandidate.isTopApplication) {\r\n        setTopCandidates((prev) => prev.filter((c) => c.applicationId !== selectedCandidate.applicationId));\r\n      } else {\r\n        setCandidates((prev) => prev.filter((c) => c.applicationId !== selectedCandidate.applicationId));\r\n      }\r\n      setShowArchiveModal(false);\r\n      setSelectedCandidate(null);\r\n    } catch {}\r\n  };\r\n\r\n  const handleStatusChangeSuccess = (candidate: CandidateApplication | topCandidateApplication, status: string) => {\r\n    if (candidate?.isTopApplication) {\r\n      if (status === APPLICATION_STATUS.REJECTED) {\r\n        setTopCandidates((prev) => prev.filter((c) => c.applicationId !== candidate.applicationId));\r\n      } else {\r\n        setTopCandidates((prev) => prev.map((c) => (c.applicationId === candidate.applicationId ? { ...c, applicationStatus: status } : c)));\r\n      }\r\n    } else {\r\n      setCandidates((prev) => prev.map((c) => (c.applicationId === candidate.applicationId ? { ...c, applicationStatus: status } : c)));\r\n    }\r\n    setShowReviewModal(false);\r\n    setSelectedCandidate(null);\r\n  };\r\n\r\n  const handleCandidateClick = (jobApplicationId: number) => {\r\n    router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${jobApplicationId}`);\r\n  };\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (\r\n        openDropdownId &&\r\n        dropdownRefs.current[openDropdownId] &&\r\n        !dropdownRefs.current[openDropdownId]?.contains(event.target as Node) &&\r\n        !(event.target as Element).closest(\".applications-sources-modal\")\r\n      ) {\r\n        setOpenDropdownId(null);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [openDropdownId]);\r\n\r\n  return (\r\n    <>\r\n      {loadingFullScreen && <FullPageLoader />}\r\n\r\n      <section className={`${style.resume_page} ${style.candidates_list_page}`}>\r\n        <div className=\"container\">\r\n          <div className=\"common-page-header\">\r\n            <div className=\"common-page-head-section\">\r\n              <div className=\"main-heading\">\r\n                <h2>\r\n                  <BackArrowIcon onClick={() => router.back()} />\r\n                  {t(\"candidates_for\")} <span>{searchParamsPromise.title}</span>\r\n                </h2>\r\n                <div className=\"right-action\">\r\n                  {hasManualResumeScreeningPermission && (\r\n                    <Button\r\n                      className=\"primary-btn rounded-md button-sm\"\r\n                      onClick={() =>\r\n                        router.push(\r\n                          `${ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${paramsPromise.jobId}?title=${searchParamsPromise.title}&jobUniqueId=${searchParamsPromise.jobUniqueId}`\r\n                        )\r\n                      }\r\n                    >\r\n                      {t(\"add_candidate\")}\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={style.candidates_list_section}>\r\n            <div className={style.section_name}>\r\n              <h3> {t(\"top_ten_candidates\")} </h3>\r\n              <p> {t(\"based_on_interview_date\")} </p>\r\n            </div>\r\n            <div className={`table-responsive ${topCandidates.length < 3 ? \"min-data\" : \"\"}`}>\r\n              <table className=\"table overflow-auto mb-0\">\r\n                <thead>\r\n                  <tr>\r\n                    <th style={{ width: \"20%\" }}> {t(\"candidate_name\")} </th>\r\n                    <th style={{ width: \"20%\" }}> {t(\"date_submitted\")} </th>\r\n                    <th style={{ width: \"20%\" }} className=\"text-center\">\r\n                      {t(\"ats_score\")}\r\n                    </th>\r\n                    {/* <th> {t(\"lined_up_for\")} </th> */}\r\n                    <th style={{ width: \"20%\" }} className=\"text-center\">\r\n                      {t(\"candidates_analysis\")}\r\n                    </th>\r\n\r\n                    <th style={{ width: \"20%\" }} className=\"text-center\">\r\n                      {\" \"}\r\n                      {t(\"actions\")}{\" \"}\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {topCandidates.length > 0 ? (\r\n                    topCandidates.map((candidate, index) => {\r\n                      const isPromoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.PROMOTED;\r\n                      const isDemoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.DEMOTED;\r\n                      const dotClass = isPromoted ? \"green-dot\" : isDemoted ? \"red-dot\" : \"\";\r\n                      const statusClass =\r\n                        candidate.applicationStatus === APPLICATION_STATUS.APPROVED\r\n                          ? \"color-success\"\r\n                          : candidate.applicationStatus === APPLICATION_STATUS.REJECTED\r\n                            ? \"color-danger\"\r\n                            : \"color-dark\";\r\n                      return (\r\n                        <tr key={candidate.candidateId}>\r\n                          <td style={{ width: \"20%\" }}>\r\n                            <div\r\n                              onClick={() => handleCandidateClick(candidate.applicationId)}\r\n                              className={`color-primary cursor-pointer  ${dotClass} d-inline`}\r\n                            >\r\n                              {index + 1}. <span className=\"text-decoration-underline\">{toTitleCase(candidate.candidateName)}</span>\r\n                            </div>\r\n                          </td>\r\n                          <td style={{ width: \"20%\" }}>\r\n                            {candidate.applicationCreatedTs ? dayjs(candidate.applicationCreatedTs).format(\"MMM D, YYYY\") : \"Not Available\"}\r\n                          </td>\r\n                          <td style={{ width: \"20%\" }} className=\"text-center\">\r\n                            {candidate.atsScore}\r\n                          </td>\r\n                          <td style={{ width: \"20%\", textAlign: \"center\" }} className={statusClass}>\r\n                            {candidate.applicationStatus}\r\n                          </td>\r\n\r\n                          {/* <td>{candidate.applicationRankStatus}</td> */}\r\n                          <td style={{ width: \"20%\" }} align=\"center\" className=\"position-relative\">\r\n                            <div onClick={() => (activeDropdown ? setActiveDropdown(null) : setActiveDropdown(candidate.candidateId))}>\r\n                              <Button className=\"clear-btn p-0\">\r\n                                <ThreeDotsIcon />\r\n                              </Button>\r\n                              {activeDropdown === candidate.candidateId && (\r\n                                <ul className=\"custom-dropdown\">\r\n                                  {candidate.applicationStatus === APPLICATION_STATUS.APPROVED && hasEditScheduledInterviewsPermission && (\r\n                                    <li\r\n                                      onClick={() =>\r\n                                        router.push(\r\n                                          `${ROUTES.INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`\r\n                                        )\r\n                                      }\r\n                                    >\r\n                                      {t(\"schedule_interview\")}{\" \"}\r\n                                    </li>\r\n                                  )}\r\n                                  {hasAddAdditionalCandidateInfoPermission && (\r\n                                    <li\r\n                                      onClick={() => router.push(`${ROUTES.INTERVIEW.ADD_CANDIDATE_INFO}?applicationId=${candidate.applicationId}`)}\r\n                                    >\r\n                                      {t(\"add_candidates_info\")}{\" \"}\r\n                                    </li>\r\n                                  )}\r\n                                  {[APPLICATION_STATUS.PENDING, APPLICATION_STATUS.ON_HOLD].includes(candidate.applicationStatus) && (\r\n                                    <li onClick={() => handleReviewCandidate(candidate)}>{t(\"analyze_candidate_resume\")}</li>\r\n                                  )}\r\n                                  {hasManageTopCandidatesPermission && (\r\n                                    <li\r\n                                      onClick={\r\n                                        !disable\r\n                                          ? () =>\r\n                                              handlePromoteDemoteCandidate(\r\n                                                candidate as unknown as CandidateApplication,\r\n                                                APPLICATION_UPDATE_STATUS.DEMOTED\r\n                                              )\r\n                                          : undefined\r\n                                      }\r\n                                    >\r\n                                      {t(\"demote_candidate\")}\r\n                                    </li>\r\n                                  )}\r\n                                  {hasArchiveRestoreCandidatesPermission && (\r\n                                    <li onClick={() => handleArchiveCandidate(candidate)}>{t(\"archive_candidate\")}</li>\r\n                                  )}\r\n                                </ul>\r\n                              )}\r\n                            </div>\r\n                          </td>\r\n                        </tr>\r\n                      );\r\n                    })\r\n                  ) : !loading ? (\r\n                    <tr>\r\n                      <td colSpan={5} className=\"text-center\">\r\n                        {t(topCandidates.length ? \"no_more_candidates_to_fetch\" : \"no_candidates_found\")}\r\n                      </td>\r\n                    </tr>\r\n                  ) : null}\r\n                </tbody>\r\n                {loading && <TableSkeleton rows={3} cols={5} colWidths=\"120,80,100,24,24\" />}\r\n              </table>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Only show Other Candidates section if there are candidates */}\r\n          {candidates.length > 0 && (\r\n            <div className={style.candidates_list_section}>\r\n              <div className={`${style.section_name} d-flex align-items-center justify-content-between`}>\r\n                <div>\r\n                  <h3>{t(\"other_candidates\")}</h3>\r\n                  <p>{t(\"rancked_by_resume\")}</p>\r\n                </div>\r\n                {/* <div className=\"right-action w-25\">\r\n                  <InputWrapper className=\"mb-0 w-100 search-input\">\r\n                    <div className=\"icon-align right\">\r\n                      <Textbox\r\n                        className=\"form-control w-100\"\r\n                        control={control}\r\n                        name=\"search\"\r\n                        type=\"text\"\r\n                        placeholder=\"Search using name\"\r\n                        // onChange={(e) => setSearchStr(e.target.value)}\r\n                      >\r\n                        <InputWrapper.Icon>\r\n                          <SearchIcon />\r\n                        </InputWrapper.Icon>\r\n                      </Textbox>\r\n                    </div>\r\n                  </InputWrapper>\r\n                </div> */}\r\n              </div>\r\n              <div className={`table-responsive ${candidates.length < 3 ? \"min-data\" : \"\"}`} id=\"scrollableCandidatesTableDiv\">\r\n                <InfiniteScroll\r\n                  dataLength={candidates.length}\r\n                  next={() => loadMoreCandidates()}\r\n                  hasMore={hasMore}\r\n                  height={window.innerHeight - 300}\r\n                  loader={\r\n                    loader && (\r\n                      <table className=\"table w-100\">\r\n                        <TableSkeleton rows={3} cols={5} colWidths=\"120,80,100,24,24\" />\r\n                      </table>\r\n                    )\r\n                  }\r\n                  endMessage={\r\n                    !loader && candidates.length ? (\r\n                      <table className=\"table w-100\">\r\n                        <tbody>\r\n                          <tr>\r\n                            <td colSpan={5} style={{ textAlign: \"center\", backgroundColor: \"#fff\" }}>\r\n                              {t(\"no_more_candidates_to_fetch\")}\r\n                            </td>\r\n                          </tr>\r\n                        </tbody>\r\n                      </table>\r\n                    ) : null\r\n                  }\r\n                >\r\n                  <table className=\"table overflow-auto mb-0\">\r\n                    <thead>\r\n                      <tr>\r\n                        <th style={{ width: \"20%\" }}>{t(\"candidate_name\")}</th>\r\n                        <th style={{ width: \"20%\" }}>{t(\"date_submitted\")}</th>\r\n                        {/* <th>{t(\"source\")}</th> */}\r\n                        <th style={{ width: \"20%\" }} className=\"text-center\">\r\n                          {t(\"ats_score\")}\r\n                        </th>\r\n\r\n                        <th style={{ width: \"20%\" }} className=\"text-center\">\r\n                          {t(\"candidates_analysis\")}\r\n                        </th>\r\n                        <th style={{ width: \"20%\" }} className=\"text-center\">\r\n                          {t(\"actions\")}\r\n                        </th>\r\n                      </tr>\r\n                    </thead>\r\n                    {candidates.length > 0 ? (\r\n                      <tbody id=\"scrollableCandidatesTableBody\">\r\n                        {candidates.map((candidate, index) => {\r\n                          const statusClass =\r\n                            candidate.applicationStatus === APPLICATION_STATUS.APPROVED\r\n                              ? \"color-success text-center\"\r\n                              : candidate.applicationStatus === APPLICATION_STATUS.REJECTED\r\n                                ? \"color-danger text-center\"\r\n                                : \"color-dark text-center\";\r\n\r\n                          const isPromoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.PROMOTED;\r\n                          const isDemoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.DEMOTED;\r\n                          const dotClass = isPromoted ? \"green-dot\" : isDemoted ? \"red-dot\" : \"\";\r\n\r\n                          return (\r\n                            <tr\r\n                              key={candidate.applicationId}\r\n                              // ref={lastElementRef}\r\n                            >\r\n                              <td>\r\n                                <div\r\n                                  onClick={() => handleCandidateClick(candidate.applicationId)}\r\n                                  className={`color-primary cursor-pointer ${dotClass} d-inline`}\r\n                                >\r\n                                  {index + 1}.{\" \"}\r\n                                  <span className=\"text-decoration-underline\"> {toTitleCase(candidate.candidateName) || \"Candidate Name\"}</span>\r\n                                </div>\r\n                              </td>\r\n                              <td>\r\n                                {candidate.applicationCreatedTs ? dayjs(candidate.applicationCreatedTs).format(\"MMM D, YYYY\") : \"Not Available\"}\r\n                              </td>\r\n                              <td className=\"text-center\">{candidate.atsScore ?? \"N/A\"}</td>\r\n                              <td className={statusClass}>{candidate.applicationStatus}</td>\r\n                              <td align=\"center\" className=\"position-relative\">\r\n                                <div onClick={() => (activeDropdown ? setActiveDropdown(null) : setActiveDropdown(candidate.candidateId))}>\r\n                                  <Button className=\"clear-btn p-0\">\r\n                                    <ThreeDotsIcon />\r\n                                  </Button>\r\n                                  {activeDropdown === candidate.candidateId && (\r\n                                    <ul\r\n                                      className=\"custom-dropdown\"\r\n                                      ref={(element) => {\r\n                                        if (element) {\r\n                                          dropdownRefs.current[String(candidate.candidateId)] = element;\r\n                                        }\r\n                                      }}\r\n                                    >\r\n                                      {candidate.applicationStatus === APPLICATION_STATUS.APPROVED && hasEditScheduledInterviewsPermission && (\r\n                                        <li\r\n                                          onClick={() =>\r\n                                            router.push(\r\n                                              `${ROUTES.INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`\r\n                                            )\r\n                                          }\r\n                                        >\r\n                                          {t(\"schedule_interview\")}\r\n                                        </li>\r\n                                      )}\r\n                                      {hasAddAdditionalCandidateInfoPermission && (\r\n                                        <li\r\n                                          onClick={() =>\r\n                                            router.push(`${ROUTES.INTERVIEW.ADD_CANDIDATE_INFO}?applicationId=${candidate.applicationId}`)\r\n                                          }\r\n                                        >\r\n                                          {t(\"add_candidates_info\")}\r\n                                        </li>\r\n                                      )}\r\n                                      {[APPLICATION_STATUS.PENDING, APPLICATION_STATUS.ON_HOLD].includes(candidate.applicationStatus) && (\r\n                                        <li onClick={() => handleReviewCandidate(candidate)}>{t(\"analyze_candidate_resume\")}</li>\r\n                                      )}\r\n                                      {/* <li>{t(\"analyze_candidate_resume\")}</li> */}\r\n                                      {candidate.applicationStatus !== APPLICATION_STATUS.REJECTED && hasManageTopCandidatesPermission && (\r\n                                        <li\r\n                                          onClick={\r\n                                            !disable ? () => handlePromoteDemoteCandidate(candidate, APPLICATION_UPDATE_STATUS.PROMOTED) : undefined\r\n                                          }\r\n                                          style={disable ? { pointerEvents: \"none\", opacity: 0.5 } : {}}\r\n                                        >\r\n                                          {t(\"promote_candidate\")}\r\n                                        </li>\r\n                                      )}\r\n                                      {hasArchiveRestoreCandidatesPermission && (\r\n                                        <li onClick={() => handleArchiveCandidate(candidate)}>{t(\"archive_candidate\")}</li>\r\n                                      )}\r\n                                    </ul>\r\n                                  )}\r\n                                </div>\r\n                              </td>\r\n                            </tr>\r\n                          );\r\n                        })}\r\n                      </tbody>\r\n                    ) : (\r\n                      !loading && (\r\n                        <tbody>\r\n                          <tr>\r\n                            <td colSpan={5} style={{ textAlign: \"center\" }}>\r\n                              {t(\"no_candidates_found\")}\r\n                            </td>\r\n                          </tr>\r\n                        </tbody>\r\n                      )\r\n                    )}\r\n                  </table>\r\n                </InfiniteScroll>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      {showReviewModal && selectedCandidate && (\r\n        <CandidateApproveRejectModal onClickCancel={onCancelReviewModal} onSuccess={handleStatusChangeSuccess} candidate={selectedCandidate} />\r\n      )}\r\n\r\n      {showArchiveModal && selectedCandidate && (\r\n        <ArchiveCandidateModal\r\n          onClickCancel={() => setShowArchiveModal(false)}\r\n          applicationId={selectedCandidate.applicationId}\r\n          jobId={Number(paramsPromise.jobId)}\r\n          onSuccess={(reason) => onSubmitArchiveReason(reason)}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default CandidatesList;\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAG9C;AACA;AACA,6CAA6C;AAC7C;AACA;AACA,gEAAgE;AAEhE;AACA,qEAAqE;AACrE,2DAA2D;AAC3D,kEAAkE;AAClE;AACA;AACA;AACA,qRAAqF,EAAE;AAEvF,WAAW;AACX;AAKA;AAGA;AACA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAxCA;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,MAAM,iBAAiB,CAAC,EACtB,MAAM,EACN,YAAY,EAIb;IACC,iCAAiC;IACjC,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAChF,MAAM,kBAAkB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW,IAAI,EAAE;IAChG,MAAM,wCAAwC,gBAAgB,QAAQ,CAAC,mIAAA,CAAA,aAAU,CAAC,0BAA0B;IAC5G,MAAM,qCAAqC,gBAAgB,QAAQ,CAAC,mIAAA,CAAA,aAAU,CAAC,uBAAuB;IACtG,MAAM,uCAAuC,gBAAgB,QAAQ,CAAC,mIAAA,CAAA,aAAU,CAAC,yBAAyB;IAC1G,MAAM,0CAA0C,gBAAgB,QAAQ,CAAC,mIAAA,CAAA,aAAU,CAAC,6BAA6B;IACjH,MAAM,mCAAmC,gBAAgB,QAAQ,CAAC,mIAAA,CAAA,aAAU,CAAC,qBAAqB;IAElG,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB,qMAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IAChC,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IAEtC,4BAA4B;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyD;IAClH,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,EAAE;IACnE,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,EAAE;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,IAAI,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,eAAe,qMAAA,CAAA,UAAK,CAAC,MAAM,CAA6C,CAAC;IAC/E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,OAAO,cAAc,KAAK,KAAK,CAAC,oBAAoB,KAAK,EAAE;YAC9D,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,SAAS;QAC9B;IACF,GAAG;QAAC,cAAc,KAAK;KAAC;IAExB,8DAA8D;IAE9D,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU,SAAS,CAAC,eAAe,OAAO;QAE/C,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,iCAA8B,AAAD,EAAE,OAAO,cAAc,KAAK;YAChF,IAAI,UAAU,MAAM,SAAS;gBAC3B,iBAAiB,SAAS,IAAI,CAAC,IAAI;YACrC,OAAO;gBACL,iBAAiB,EAAE;YACrB;QACF,EAAE,OAAM;YACN,iBAAiB,EAAE;QACrB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,+BAA+B,OAAO,WAA2D;QACrG,WAAW;QACX,qBAAqB;QACrB,IAAI;YACF,MAAM,UAAU;gBACd,aAAa,UAAU,WAAW;gBAClC,eAAe,UAAU,aAAa;gBACtC;YACF;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,yBAAsB,AAAD,EAAE;YAE9C,IAAI,UAAU,MAAM,SAAS;gBAC3B,IAAI,WAAW,wIAAA,CAAA,4BAAyB,CAAC,QAAQ,EAAE;oBACjD,wDAAwD;oBACxD,MAAM,YAAY;oBAElB,+BAA+B;oBAC/B,cAAc,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,UAAU,aAAa;oBAEtF,wBAAwB;oBACxB,MAAM,WAAoC;wBACxC,eAAe,UAAU,aAAa;wBACtC,sBAAsB,UAAU,oBAAoB;wBACpD,UAAU,UAAU,QAAQ;wBAC5B,eAAe,UAAU,aAAa;wBACtC,uBAAuB,wIAAA,CAAA,4BAAyB,CAAC,QAAQ;wBACzD,aAAa,UAAU,WAAW;wBAClC,UAAU,UAAU,QAAQ;wBAC5B,YAAY,UAAU,UAAU;wBAChC,mBAAmB,UAAU,iBAAiB;wBAC9C,qBAAqB,UAAU,mBAAmB;wBAClD,sBAAsB,IAAI,OAAO,WAAW;wBAC5C,mBAAmB,UAAU,iBAAiB,IAAI;wBAClD,QAAQ,UAAU,MAAM,IAAI;wBAC5B,kBAAkB,UAAU,gBAAgB,IAAI;oBAClD;oBAEA,iBAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAS;gBAChD,OAAO,IAAI,WAAW,wIAAA,CAAA,4BAAyB,CAAC,OAAO,EAAE;oBACvD,sDAAsD;oBACtD,MAAM,UAAU;oBAEhB,6BAA6B;oBAC7B,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,QAAQ,aAAa;oBAEvF,0BAA0B;oBAC1B,MAAM,UAAgC;wBACpC,aAAa,QAAQ,WAAW;wBAChC,eAAe,QAAQ,aAAa;wBACpC,eAAe,QAAQ,aAAa;wBACpC,mBAAmB,QAAQ,iBAAiB;wBAC5C,mBAAmB,QAAQ,iBAAiB,IAAI;wBAChD,sBAAsB,QAAQ,oBAAoB;wBAClD,sBAAsB,IAAI,OAAO,WAAW;wBAC5C,UAAU;wBACV,QAAQ,QAAQ,MAAM,IAAI;wBAC1B,mBAAmB;wBACnB,qBAAqB,QAAQ,mBAAmB,IAAI;wBACpD,uBAAuB,wIAAA,CAAA,4BAAyB,CAAC,OAAO;wBACxD,UAAU,QAAQ,QAAQ;wBAC1B,UAAU,QAAQ,QAAQ;wBAC1B,YAAY,QAAQ,UAAU;oBAChC;oBAEA,cAAc,CAAC,OAAS;+BAAI;4BAAM;yBAAQ;gBAC5C;YACF,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAM,CACR,SAAU;YACR,kBAAkB;YAClB,WAAW;YACX,qBAAqB;QACvB;IACF;IAEA,MAAM,kCAAkC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,gBAAgB,CAAC,EAAE,QAAQ,KAAK,EAAE,YAAoB,EAAE;QACjH,IAAI,CAAC,UAAU,OAAO;QACtB,UAAU;QACV,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,8BAA2B,AAAD,EAAE;gBACjD,MAAM;gBACN,OAAO,mIAAA,CAAA,gBAAa;gBACpB,WAAW;gBACX,UAAU;gBACV,OAAO,OAAO,cAAc,KAAK;YACnC;YAEA,QAAQ,GAAG,CAAC,YAAY;YACxB,IAAI,UAAU,MAAM,SAAS;gBAC3B,MAAM,gBAAwC,SAAS,IAAI,EAAE,MAAM;gBAEnE,QAAQ,GAAG,CAAC,iBAAiB;gBAE7B,cAAc,CAAC,OAAU,QAAQ,gBAAgB;2BAAI;2BAAS;qBAAc;gBAC5E,IAAI,cAAc,MAAM,GAAG,mIAAA,CAAA,gBAAa,EAAE;oBACxC,WAAW;gBACb,OAAO;oBACL,WAAW;gBACb;gBACA,UAAU,gBAAgB,cAAc,MAAM;YAChD,OAAO;gBACL,WAAW;YACb;QACF,EAAE,OAAM;YACN,WAAW;QACb,SAAU;YACR,UAAU;QACZ;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,UAAU,SAAS,gCAAgC,QAAQ,OAAO;IACzE;IAEA,sCAAsC;IACtC,oCAAoC;IACpC,2BAA2B;IAC3B,2DAA2D;IAC3D,iEAAiE;IACjE,wEAAwE;IACxE,UAAU;IACV,gDAAgD;IAChD,OAAO;IACP,+BAA+B;IAC/B,KAAK;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,MAAM,UAAU,OAAO;IACvC,GAAG;QAAC,UAAU;QAAI,UAAU;KAAM;IAElC,MAAM,mCAAmC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjD,CAAA,GAAA,kIAAA,CAAA,UAAQ,AAAD,EAAE;QACP,IAAI,UAAU,MAAM,UAAU,OAAO,gCAAgC,GAAG,MAAM;IAChF,GAAG,OACH;QAAC,UAAU;QAAI,UAAU;QAAO;QAAW;KAAgC;IAG7E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,MAAM,UAAU,OAAO;QACrC,OAAO;YACL,iCAAiC,MAAM;QACzC;IACF,GAAG;QAAC,UAAU;QAAI,UAAU;QAAO;KAAiC;IAEpE,MAAM,yBAAyB,CAAC;QAC9B,qBAAqB;QACrB,oBAAoB;QACpB,kBAAkB;IACpB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI,CAAC,mBAAmB;QACxB,IAAI;YACF,MAAM,CAAA,GAAA,iLAAA,CAAA,2BAAwB,AAAD,EAAE,kBAAkB,aAAa,EAAE,OAAO;YACvE,IAAI,kBAAkB,gBAAgB,EAAE;gBACtC,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,kBAAkB,aAAa;YACnG,OAAO;gBACL,cAAc,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,kBAAkB,aAAa;YAChG;YACA,oBAAoB;YACpB,qBAAqB;QACvB,EAAE,OAAM,CAAC;IACX;IAEA,MAAM,4BAA4B,CAAC,WAA2D;QAC5F,IAAI,WAAW,kBAAkB;YAC/B,IAAI,WAAW,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE;gBAC1C,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,UAAU,aAAa;YAC3F,OAAO;gBACL,iBAAiB,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,aAAa,KAAK,UAAU,aAAa,GAAG;4BAAE,GAAG,CAAC;4BAAE,mBAAmB;wBAAO,IAAI;YAClI;QACF,OAAO;YACL,cAAc,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,aAAa,KAAK,UAAU,aAAa,GAAG;wBAAE,GAAG,CAAC;wBAAE,mBAAmB;oBAAO,IAAI;QAC/H;QACA,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,kBAAkB;IACpE;IACA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,kBACA,aAAa,OAAO,CAAC,eAAe,IACpC,CAAC,aAAa,OAAO,CAAC,eAAe,EAAE,SAAS,MAAM,MAAM,KAC5D,CAAC,AAAC,MAAM,MAAM,CAAa,OAAO,CAAC,gCACnC;gBACA,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAe;IAEnB,qBACE;;YACG,mCAAqB,8OAAC,uJAAA,CAAA,UAAc;;;;;0BAErC,8OAAC;gBAAQ,WAAW,GAAG,yJAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC,EAAE,yJAAA,CAAA,UAAK,CAAC,oBAAoB,EAAE;0BACtE,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,oJAAA,CAAA,UAAa;oDAAC,SAAS,IAAM,OAAO,IAAI;;;;;;gDACxC,EAAE;gDAAkB;8DAAC,8OAAC;8DAAM,oBAAoB,KAAK;;;;;;;;;;;;sDAExD,8OAAC;4CAAI,WAAU;sDACZ,oDACC,8OAAC,4IAAA,CAAA,UAAM;gDACL,WAAU;gDACV,SAAS,IACP,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,EAAE,cAAc,KAAK,CAAC,OAAO,EAAE,oBAAoB,KAAK,CAAC,aAAa,EAAE,oBAAoB,WAAW,EAAE;0DAI7J,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQf,8OAAC;4BAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,uBAAuB;;8CAC3C,8OAAC;oCAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,YAAY;;sDAChC,8OAAC;;gDAAG;gDAAE,EAAE;gDAAsB;;;;;;;sDAC9B,8OAAC;;gDAAE;gDAAE,EAAE;gDAA2B;;;;;;;;;;;;;8CAEpC,8OAAC;oCAAI,WAAW,CAAC,iBAAiB,EAAE,cAAc,MAAM,GAAG,IAAI,aAAa,IAAI;8CAC9E,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;0DACC,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;;gEAAG;gEAAE,EAAE;gEAAkB;;;;;;;sEACnD,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;;gEAAG;gEAAE,EAAE;gEAAkB;;;;;;;sEACnD,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;4DAAG,WAAU;sEACpC,EAAE;;;;;;sEAGL,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;4DAAG,WAAU;sEACpC,EAAE;;;;;;sEAGL,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;4DAAG,WAAU;;gEACpC;gEACA,EAAE;gEAAY;;;;;;;;;;;;;;;;;;0DAIrB,8OAAC;0DACE,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,WAAW;oDAC5B,MAAM,aAAa,UAAU,qBAAqB,KAAK,wIAAA,CAAA,4BAAyB,CAAC,QAAQ;oDACzF,MAAM,YAAY,UAAU,qBAAqB,KAAK,wIAAA,CAAA,4BAAyB,CAAC,OAAO;oDACvF,MAAM,WAAW,aAAa,cAAc,YAAY,YAAY;oDACpE,MAAM,cACJ,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,GACvD,kBACA,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,GACzD,iBACA;oDACR,qBACE,8OAAC;;0EACC,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;0EACxB,cAAA,8OAAC;oEACC,SAAS,IAAM,qBAAqB,UAAU,aAAa;oEAC3D,WAAW,CAAC,8BAA8B,EAAE,SAAS,SAAS,CAAC;;wEAE9D,QAAQ;wEAAE;sFAAE,8OAAC;4EAAK,WAAU;sFAA6B,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,UAAU,aAAa;;;;;;;;;;;;;;;;;0EAGjG,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;0EACvB,UAAU,oBAAoB,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,UAAU,oBAAoB,EAAE,MAAM,CAAC,iBAAiB;;;;;;0EAElG,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,WAAU;0EACpC,UAAU,QAAQ;;;;;;0EAErB,8OAAC;gEAAG,OAAO;oEAAE,OAAO;oEAAO,WAAW;gEAAS;gEAAG,WAAW;0EAC1D,UAAU,iBAAiB;;;;;;0EAI9B,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,OAAM;gEAAS,WAAU;0EACpD,cAAA,8OAAC;oEAAI,SAAS,IAAO,iBAAiB,kBAAkB,QAAQ,kBAAkB,UAAU,WAAW;;sFACrG,8OAAC,4IAAA,CAAA,UAAM;4EAAC,WAAU;sFAChB,cAAA,8OAAC,oJAAA,CAAA,UAAa;;;;;;;;;;wEAEf,mBAAmB,UAAU,WAAW,kBACvC,8OAAC;4EAAG,WAAU;;gFACX,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,IAAI,sDAC9D,8OAAC;oFACC,SAAS,IACP,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,eAAe,EAAE,mBAAmB,UAAU,aAAa,EAAE,OAAO,EAAE,mBAAmB,oBAAoB,KAAK,EAAE,OAAO,EAAE,cAAc,KAAK,CAAC,aAAa,EAAE,mBAAmB,oBAAoB,WAAW,GAAG;;wFAIxS,EAAE;wFAAuB;;;;;;;gFAG7B,yDACC,8OAAC;oFACC,SAAS,IAAM,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,eAAe,EAAE,UAAU,aAAa,EAAE;;wFAE3G,EAAE;wFAAwB;;;;;;;gFAG9B;oFAAC,0IAAA,CAAA,qBAAkB,CAAC,OAAO;oFAAE,0IAAA,CAAA,qBAAkB,CAAC,OAAO;iFAAC,CAAC,QAAQ,CAAC,UAAU,iBAAiB,mBAC5F,8OAAC;oFAAG,SAAS,IAAM,sBAAsB;8FAAa,EAAE;;;;;;gFAEzD,kDACC,8OAAC;oFACC,SACE,CAAC,UACG,IACE,6BACE,WACA,wIAAA,CAAA,4BAAyB,CAAC,OAAO,IAErC;8FAGL,EAAE;;;;;;gFAGN,uDACC,8OAAC;oFAAG,SAAS,IAAM,uBAAuB;8FAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;uDAhE5D,UAAU,WAAW;;;;;gDAwElC,KACE,CAAC,wBACH,8OAAC;8DACC,cAAA,8OAAC;wDAAG,SAAS;wDAAG,WAAU;kEACvB,EAAE,cAAc,MAAM,GAAG,gCAAgC;;;;;;;;;;2DAG5D;;;;;;4CAEL,yBAAW,8OAAC,yJAAA,CAAA,UAAa;gDAAC,MAAM;gDAAG,MAAM;gDAAG,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAM5D,WAAW,MAAM,GAAG,mBACnB,8OAAC;4BAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,uBAAuB;;8CAC3C,8OAAC;oCAAI,WAAW,GAAG,yJAAA,CAAA,UAAK,CAAC,YAAY,CAAC,kDAAkD,CAAC;8CACvF,cAAA,8OAAC;;0DACC,8OAAC;0DAAI,EAAE;;;;;;0DACP,8OAAC;0DAAG,EAAE;;;;;;;;;;;;;;;;;8CAqBV,8OAAC;oCAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW,MAAM,GAAG,IAAI,aAAa,IAAI;oCAAE,IAAG;8CAChF,cAAA,8OAAC,+KAAA,CAAA,UAAc;wCACb,YAAY,WAAW,MAAM;wCAC7B,MAAM,IAAM;wCACZ,SAAS;wCACT,QAAQ,OAAO,WAAW,GAAG;wCAC7B,QACE,wBACE,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC,yJAAA,CAAA,UAAa;gDAAC,MAAM;gDAAG,MAAM;gDAAG,WAAU;;;;;;;;;;;wCAIjD,YACE,CAAC,UAAU,WAAW,MAAM,iBAC1B,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;0DACC,cAAA,8OAAC;8DACC,cAAA,8OAAC;wDAAG,SAAS;wDAAG,OAAO;4DAAE,WAAW;4DAAU,iBAAiB;wDAAO;kEACnE,EAAE;;;;;;;;;;;;;;;;;;;;qDAKT;kDAGN,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;8DACC,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;0EAAI,EAAE;;;;;;0EAChC,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;0EAAI,EAAE;;;;;;0EAEhC,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,WAAU;0EACpC,EAAE;;;;;;0EAGL,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,WAAU;0EACpC,EAAE;;;;;;0EAEL,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,WAAU;0EACpC,EAAE;;;;;;;;;;;;;;;;;gDAIR,WAAW,MAAM,GAAG,kBACnB,8OAAC;oDAAM,IAAG;8DACP,WAAW,GAAG,CAAC,CAAC,WAAW;wDAC1B,MAAM,cACJ,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,GACvD,8BACA,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,GACzD,6BACA;wDAER,MAAM,aAAa,UAAU,qBAAqB,KAAK,wIAAA,CAAA,4BAAyB,CAAC,QAAQ;wDACzF,MAAM,YAAY,UAAU,qBAAqB,KAAK,wIAAA,CAAA,4BAAyB,CAAC,OAAO;wDACvF,MAAM,WAAW,aAAa,cAAc,YAAY,YAAY;wDAEpE,qBACE,8OAAC;;8EAIC,8OAAC;8EACC,cAAA,8OAAC;wEACC,SAAS,IAAM,qBAAqB,UAAU,aAAa;wEAC3D,WAAW,CAAC,6BAA6B,EAAE,SAAS,SAAS,CAAC;;4EAE7D,QAAQ;4EAAE;4EAAE;0FACb,8OAAC;gFAAK,WAAU;;oFAA4B;oFAAE,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,UAAU,aAAa,KAAK;;;;;;;;;;;;;;;;;;8EAG1F,8OAAC;8EACE,UAAU,oBAAoB,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,UAAU,oBAAoB,EAAE,MAAM,CAAC,iBAAiB;;;;;;8EAElG,8OAAC;oEAAG,WAAU;8EAAe,UAAU,QAAQ,IAAI;;;;;;8EACnD,8OAAC;oEAAG,WAAW;8EAAc,UAAU,iBAAiB;;;;;;8EACxD,8OAAC;oEAAG,OAAM;oEAAS,WAAU;8EAC3B,cAAA,8OAAC;wEAAI,SAAS,IAAO,iBAAiB,kBAAkB,QAAQ,kBAAkB,UAAU,WAAW;;0FACrG,8OAAC,4IAAA,CAAA,UAAM;gFAAC,WAAU;0FAChB,cAAA,8OAAC,oJAAA,CAAA,UAAa;;;;;;;;;;4EAEf,mBAAmB,UAAU,WAAW,kBACvC,8OAAC;gFACC,WAAU;gFACV,KAAK,CAAC;oFACJ,IAAI,SAAS;wFACX,aAAa,OAAO,CAAC,OAAO,UAAU,WAAW,EAAE,GAAG;oFACxD;gFACF;;oFAEC,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,IAAI,sDAC9D,8OAAC;wFACC,SAAS,IACP,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,eAAe,EAAE,mBAAmB,UAAU,aAAa,EAAE,OAAO,EAAE,mBAAmB,oBAAoB,KAAK,EAAE,OAAO,EAAE,cAAc,KAAK,CAAC,aAAa,EAAE,mBAAmB,oBAAoB,WAAW,GAAG;kGAIxS,EAAE;;;;;;oFAGN,yDACC,8OAAC;wFACC,SAAS,IACP,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,eAAe,EAAE,UAAU,aAAa,EAAE;kGAG9F,EAAE;;;;;;oFAGN;wFAAC,0IAAA,CAAA,qBAAkB,CAAC,OAAO;wFAAE,0IAAA,CAAA,qBAAkB,CAAC,OAAO;qFAAC,CAAC,QAAQ,CAAC,UAAU,iBAAiB,mBAC5F,8OAAC;wFAAG,SAAS,IAAM,sBAAsB;kGAAa,EAAE;;;;;;oFAGzD,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,IAAI,kDAC9D,8OAAC;wFACC,SACE,CAAC,UAAU,IAAM,6BAA6B,WAAW,wIAAA,CAAA,4BAAyB,CAAC,QAAQ,IAAI;wFAEjG,OAAO,UAAU;4FAAE,eAAe;4FAAQ,SAAS;wFAAI,IAAI,CAAC;kGAE3D,EAAE;;;;;;oFAGN,uDACC,8OAAC;wFAAG,SAAS,IAAM,uBAAuB;kGAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;2DAlE9D,UAAU,aAAa;;;;;oDA0ElC;;;;;2DAGF,CAAC,yBACC,8OAAC;8DACC,cAAA,8OAAC;kEACC,cAAA,8OAAC;4DAAG,SAAS;4DAAG,OAAO;gEAAE,WAAW;4DAAS;sEAC1C,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAc1B,mBAAmB,mCAClB,8OAAC,iKAAA,CAAA,UAA2B;gBAAC,eAAe;gBAAqB,WAAW;gBAA2B,WAAW;;;;;;YAGnH,oBAAoB,mCACnB,8OAAC,2JAAA,CAAA,UAAqB;gBACpB,eAAe,IAAM,oBAAoB;gBACzC,eAAe,kBAAkB,aAAa;gBAC9C,OAAO,OAAO,cAAc,KAAK;gBACjC,WAAW,CAAC,SAAW,sBAAsB;;;;;;;;AAKvD;uCAEe", "debugId": null}}, {"offset": {"line": 2561, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2567, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/candidates-list/%5BjobId%5D/page.tsx"], "sourcesContent": ["\"use client\";\nimport CandidatesList from \"@/components/views/resume/CandidatesList\";\nimport React from \"react\";\n\nconst page = ({ params, searchParams }: { params: Promise<{ jobId: string }>; searchParams: Promise<{ title: string; jobUniqueId: string }> }) => {\n  return (\n    <div>\n      <CandidatesList params={params} searchParams={searchParams} />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,YAAY,EAAyG;IAC3I,qBACE,8OAAC;kBACC,cAAA,8OAAC,uJAAA,CAAA,UAAc;YAAC,QAAQ;YAAQ,cAAc;;;;;;;;;;;AAGpD;uCAEe", "debugId": null}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}