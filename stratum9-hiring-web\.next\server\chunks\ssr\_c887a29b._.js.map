{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/home.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"advantage_card\": \"home-module-scss-module__7j9g1q__advantage_card\",\n  \"banner_content\": \"home-module-scss-module__7j9g1q__banner_content\",\n  \"banner_image\": \"home-module-scss-module__7j9g1q__banner_image\",\n  \"benefitList\": \"home-module-scss-module__7j9g1q__benefitList\",\n  \"benefitRow\": \"home-module-scss-module__7j9g1q__benefitRow\",\n  \"card\": \"home-module-scss-module__7j9g1q__card\",\n  \"cardEnterprise\": \"home-module-scss-module__7j9g1q__cardEnterprise\",\n  \"cardPro\": \"home-module-scss-module__7j9g1q__cardPro\",\n  \"card_body\": \"home-module-scss-module__7j9g1q__card_body\",\n  \"card_image\": \"home-module-scss-module__7j9g1q__card_image\",\n  \"expeience_bg_img\": \"home-module-scss-module__7j9g1q__expeience_bg_img\",\n  \"expeience_section\": \"home-module-scss-module__7j9g1q__expeience_section\",\n  \"expeience_text\": \"home-module-scss-module__7j9g1q__expeience_text\",\n  \"frame_img\": \"home-module-scss-module__7j9g1q__frame_img\",\n  \"growth_section\": \"home-module-scss-module__7j9g1q__growth_section\",\n  \"hero_section\": \"home-module-scss-module__7j9g1q__hero_section\",\n  \"home_page\": \"home-module-scss-module__7j9g1q__home_page\",\n  \"iconImg\": \"home-module-scss-module__7j9g1q__iconImg\",\n  \"img\": \"home-module-scss-module__7j9g1q__img\",\n  \"page_heading\": \"home-module-scss-module__7j9g1q__page_heading\",\n  \"planTitle\": \"home-module-scss-module__7j9g1q__planTitle\",\n  \"quotes\": \"home-module-scss-module__7j9g1q__quotes\",\n  \"star_content\": \"home-module-scss-module__7j9g1q__star_content\",\n  \"star_founder\": \"home-module-scss-module__7j9g1q__star_founder\",\n  \"star_founder_img\": \"home-module-scss-module__7j9g1q__star_founder_img\",\n  \"star_founder_info\": \"home-module-scss-module__7j9g1q__star_founder_info\",\n  \"star_icon\": \"home-module-scss-module__7j9g1q__star_icon\",\n  \"star_img\": \"home-module-scss-module__7j9g1q__star_img\",\n  \"subscription_plans\": \"home-module-scss-module__7j9g1q__subscription_plans\",\n  \"team_content\": \"home-module-scss-module__7j9g1q__team_content\",\n  \"team_img\": \"home-module-scss-module__7j9g1q__team_img\",\n  \"team_info\": \"home-module-scss-module__7j9g1q__team_info\",\n  \"top_brand_image\": \"home-module-scss-module__7j9g1q__top_brand_image\",\n  \"top_brand_section\": \"home-module-scss-module__7j9g1q__top_brand_section\",\n  \"wrapper\": \"home-module-scss-module__7j9g1q__wrapper\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/logo-group.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 864, height: 175, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/ABsVEyUODAkZDAcALwYDACACAAEdAQAAFAAAACEAAAAwAAcLAAwQGAAzAQEAJw0DBQ4pCQ8hEQgKEwgJCCkAAAATbrADSGnAdlEAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 2 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8M,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/hirng-hero.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 823, height: 664, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAA0UlEQVR42gHGADn/ALq5uuG1r671dH+Um5C18vRvi7nFMTU6WHZvZuF4cV/+AI2Mja6Qjo7MXGZ5g22Px890jrrNXGJplISAdNZwaljWAGRkY7t1eX3bgYB/1nl+gt08ODnVcmQ+msOjTN3DokreAIJ+d9SXlJDcmZKN2np8ft1KSUq9OzYkUKiMQLvFpU3dAH5qNY+4m0zRenRdmnqUwcxQZYaYHiEmNHh2ca6cmI7WAN64UPbLqErhREExWG2PxsWPserwUFpqp4CJj/2Di5H/gP1p6tpwhIIAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8X,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/upload-resume-screening.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 3088, height: 1900, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAaUlEQVR42iWNSRLFIAhEvf8l/7hOpVQQUNMBsuhqhgddzOwSUZhNqGuujTndo/d5GWOg1obeCTw0l60Rfq832GdFVcHMCDBq/4jjJHw/f7TaHRABEUPcA3pgSY/o/BBAXGa2a62V2nvjBhv5m5iMT4SMAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAkP,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/scored-results-img.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 3088, height: 2056, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAg0lEQVR42j3KSwrCMBSF4ex/C66jGxA6sAMLThxICFUwoCaxzctLHsc2gw4+Dgd+ZvQne7eglIxa67oFOee222fLrPE1Cs5Z+BDhI4GIWtSC83XCcOHoRwFxGyB5D4q2BRt2OHboxhMmaTCrO4LmSOSREzVMqwfUS8AauXrufu6NFBT+Gv6XUm4TaU0AAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+IAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAsR,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/performance-based-image.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 767, height: 492, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAsElEQVR42gGlAFr/AKSintJ5b2PodnFq6JSSjc9ya2KkdG5kozQ0MkkAAAAAAHR2eKNJQzu0T01Ju56cm+B/al/ijYB24UJCQWUAAAAAABERERJOTktWfn15hqimorWXjoemX1tXbTg3NTwNDQ0MAIKAeoLP0Mvs397X/Onm3v7p5Nv+4t7V9dXSyeWBf3qGACUlIyVgXltjd3Rwfo2KhZajoJmwu7ixzIeFgJUlJCMnBdZOQv9gbEAAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkV,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/dashboard-admin-img.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 1544, height: 1038, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAc0lEQVR42k1OyQ4CIRTj/3/Qi0eNMxOEt7A+AxU52UvTpJsjYlNVlFLQ7YM5J/7hHpcgclqGilo7WjeYGVrrmx1JBUlBlIznESDppzNohUQLHKcGHwSvi3C7Hzi9bn2dHv7NcJoNLIoQedePMdZUXTz3ny/ZnJpUl5pezAAAAABJRU5ErkJggg==\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,gJAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAkQ,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/StarIcon.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 29, height: 27, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/client1.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 64, height: 64, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+AAICAgMyNC1BhIl4v6Cok/aiqZT2hot5vjI0LUECAgIDADU2L0GPlIHZd3lq/2xlVv+emoL/rLCZ/5ibhtk1Ni9BAI+Sf75lZFn/LiIe/1s4KP+AWkD/pJqB/7a5nv+UloG/AKankPYxLCn/JRgW/08rH/95STD/jm9V/767nv+/v6P2ALW0m/ZFPTb/MR0Y/zIcF/9hOij/k2pO/8S+oP/Hxqj2AKKhir+QjXj/Lyck/yYYFv84Ix3/iHBY/8rFpf+npYy+ADs7M0F+gnLZYGNZ/1U8Lv9rTjv/tq6R/7Crj9k9PDJBAAICAgMcHx1BYGNWvo2BaPa2qIn2o52Cvzs5L0ECAgIDnZ1+oxfOsJ0AAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsd,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/quotes.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 109, height: 109, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAw0lEQVR42oWPvwqCUBTGb5JNCSUJuYtC4GDYHgk3VDB6hMAh6UkchAbvJji4OHXHprbe4C6XO9w3aHase3oBf3DgO98H5w9CYywVURQd4jg+uq7reJ7ngAYPMoQxThljHyHEUFUVgQINHmQoSZIz53yQUn4JIW1d1y1o8CBDQRBsm6Z59H3/Ksvynuf5teu6J6X0rVadkKZpE8Mw5mocvih0XZ/atr0uiuIWhuHuf6hpmossy1LLslbQ+76/2StmitEvf8o8SZGnB91LAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,6HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0W,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/frame.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 538, height: 689, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAYAAADaxo44AAAA00lEQVR42gHIADf/ABscGCdwdGaxkZWD+KClkPh2eWqxGxwYJwBydGWtYmFW/VhDNv+Sf2b/rq+W/Xt9a60AiYl39iggHv9KKh7/eU42/6qdgf+5up72AKGfif84KyX/MBwX/2I9Kv+slnn/yciq/wDDwqX/bGtd/zQlIP9TPTD/t62Q/8zIqf8AoKSO/293av+CdF//q5p8/8K6mv+/tpf/AKOkjf+wrJL/ubGU/8O8nP+9s5T/t62O/wC+uZz/xLye/8S7nP/FvZ3/wbiZ/7Wsjf9UfnWmTQNGrQAAAABJRU5ErkJggg==\", blurWidth: 6, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,4HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkY,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/growth1.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 539, height: 693, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAYAAADaxo44AAAAqUlEQVR42l3NOw6CQBSF4WEDWPjoNSyCIEMCZIbCsQCNaK/UBhuMsUFN1CXIVkl+wdLiK+7NOTnCdd3W8zwCKTGJQvo+UspWBEFAGIYsdEy93xH5c5RSiP6ZJAk6jig3S9ZLQ5ZlCK01xhhWacbjeuFe36iqM6LbQEURp2NJ82l4vt4cigLhOA6z6ZRFHLLNc9Ku2YeFbdv0Bp3RcMh4MvndwrKstsOf9gt0OlJ+myy8ewAAAABJRU5ErkJggg==\", blurWidth: 6, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0U,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/growth2.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 539, height: 693, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAYAAADaxo44AAAAw0lEQVR42l3DTYsBYQDA8We+xewz9z1sbW2OG7upPdnaLSEKpRw4kLOkKAeRAw7iwoWDg7xk0jRSjppiGJ9m6k+ODj9xcWx3a264Xm0c58RGX3E8HlxxOts0KkX2OwNDXxAP/6PrS4RlWWzXMwadJu1mjW6rznw2QRimyajXJp8IEv/7IZtOMh0PEd1en2q5RCadIhoO8v72SqGQQ4QiMQKBX3xf33x6vXx4PPj9PoTUNB6kRNMkqqoiX1SEoijuHU/cG3xva/d+ntVbAAAAAElFTkSuQmCC\", blurWidth: 6, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0W,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/growth3.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 539, height: 693, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAYAAADaxo44AAAAw0lEQVR42l3Fu2rCABiA0T/VlFKaOLR0ij5AS0uXru1QsVgIdNBBRNDFJ3B1cRAUFAdRcVHiPSSKEiOojxb4FEeHw5HheBTYS5eVM2fh2IxmM7r9XiBLz2O389hbbQadJs5mjTWdINvDEd/3cVoVGuUSc9dlYi+Q/tAiVyiSz+b4+02SSJpUa3XkP5Xh6TlK2jSJf32j6Y/8xBPIy9sHd/cRYkaMz/dXtAcdw4gimh7hJhQmdKaqKuHLt4iiKIGIcCU4Af0HYKK63Al7AAAAAElFTkSuQmCC\", blurWidth: 6, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0W,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/app-store.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 279, height: 83, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/ACwtLfZmZmb9KCgp/Tc4OP02Nzf9MjIy/S0tLv0XGBj3ADQ1NfV7e3v8MTEx/EtLS/w8PT38QEBB/Ds8PPwhIiL2XRMaolbx3HQAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 2 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAI,aAAa;IAA8M,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/play-store.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 276, height: 83, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/ACtFSPg0QC77Jycl+yorK/sqKyv7Gxsc+xwcHfsWFhf1ADI/SPg+Ny38Kion/Dk6OvxISUn8Nzg4/D8/P/wqKir2CtUYnNMZ/aQAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 2 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAI,aAAa;IAA8M,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/Facebook.tsx"], "sourcesContent": ["import React, { <PERSON> } from \"react\";\n\nconst Facebook: FC = () => {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 64 64\" fill=\"none\">\n      <rect x=\"0.666667\" y=\"0.666667\" width=\"62.6667\" height=\"62.6667\" rx=\"31.3333\" stroke=\"white\" strokeOpacity=\"0.22\" strokeWidth=\"1.33333\" />\n      <path\n        d=\"M34.4779 48V33.4044H39.3751L40.1098 27.7145H34.4779V24.0824C34.4779 22.4355 34.9333 21.3132 37.2976 21.3132L40.308 21.312V16.2228C39.7874 16.1551 38.0003 16 35.9203 16C31.577 16 28.6034 18.6511 28.6034 23.5188V27.7145H23.6914V33.4044H28.6034V48H34.4779Z\"\n        fill=\"white\"\n      />\n    </svg>\n  );\n};\n\nexport default Facebook;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,WAAe;IACnB,qBACE,8OAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;;0BAC/D,8OAAC;gBAAK,GAAE;gBAAW,GAAE;gBAAW,OAAM;gBAAU,QAAO;gBAAU,IAAG;gBAAU,QAAO;gBAAQ,eAAc;gBAAO,aAAY;;;;;;0BAC9H,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/XIcon.tsx"], "sourcesContent": ["import React, { FC } from \"react\";\n\nconst XIcon: FC = () => {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 64 64\" fill=\"none\">\n      <rect x=\"0.666667\" y=\"0.666667\" width=\"62.6667\" height=\"62.6667\" rx=\"31.3333\" stroke=\"white\" strokeOpacity=\"0.22\" strokeWidth=\"1.33333\" />\n      <g clipPath=\"url(#clip0_630_2379)\">\n        <path\n          d=\"M34.9786 29.5498L46.6352 16H43.8729L33.7515 27.7651L25.6676 16H16.3438L28.5682 33.7909L16.3438 48H19.1061L29.7946 35.5756L38.3318 48H47.6557L34.9779 29.5498H34.9786ZM31.1951 33.9477L29.9565 32.1761L20.1015 18.0795H24.3443L32.2975 29.4559L33.5361 31.2275L43.8742 46.0151H39.6314L31.1951 33.9484V33.9477Z\"\n          fill=\"white\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_630_2379\">\n          <rect width=\"32\" height=\"32\" fill=\"white\" transform=\"translate(16 16)\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n};\n\nexport default XIcon;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,QAAY;IAChB,qBACE,8OAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;;0BAC/D,8OAAC;gBAAK,GAAE;gBAAW,GAAE;gBAAW,OAAM;gBAAU,QAAO;gBAAU,IAAG;gBAAU,QAAO;gBAAQ,eAAc;gBAAO,aAAY;;;;;;0BAC9H,8OAAC;gBAAE,UAAS;0BACV,cAAA,8OAAC;oBACC,GAAE;oBACF,MAAK;;;;;;;;;;;0BAGT,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK9D;uCAEe", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/LinkIn.tsx"], "sourcesContent": ["import React, { FC } from \"react\";\n\nconst LinkIn: FC = () => {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 64 64\" fill=\"none\">\n      <rect x=\"0.666667\" y=\"0.666667\" width=\"62.6667\" height=\"62.6667\" rx=\"31.3333\" stroke=\"white\" strokeOpacity=\"0.22\" strokeWidth=\"1.33333\" />\n      <path\n        d=\"M44.7999 44.8009V35.4249C44.7999 30.8169 43.8079 27.2969 38.4319 27.2969C35.8399 27.2969 34.1119 28.7049 33.4079 30.0489H33.3439V27.7129H28.2559V44.8009H33.5679V36.3209C33.5679 34.0809 33.9839 31.9369 36.7359 31.9369C39.4559 31.9369 39.4879 34.4649 39.4879 36.4489V44.7689H44.7999V44.8009Z\"\n        fill=\"white\"\n      />\n      <path d=\"M19.6162 27.7109H24.9282V44.7989H19.6162V27.7109Z\" fill=\"white\" />\n      <path\n        d=\"M22.2722 19.2031C20.5762 19.2031 19.2002 20.5791 19.2002 22.2751C19.2002 23.9711 20.5762 25.3791 22.2722 25.3791C23.9682 25.3791 25.3442 23.9711 25.3442 22.2751C25.3442 20.5791 23.9682 19.2031 22.2722 19.2031Z\"\n        fill=\"white\"\n      />\n    </svg>\n  );\n};\n\nexport default LinkIn;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,SAAa;IACjB,qBACE,8OAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;;0BAC/D,8OAAC;gBAAK,GAAE;gBAAW,GAAE;gBAAW,OAAM;gBAAU,QAAO;gBAAU,IAAG;gBAAU,QAAO;gBAAQ,eAAc;gBAAO,aAAY;;;;;;0BAC9H,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/Instagram.tsx"], "sourcesContent": ["import React, { FC } from \"react\";\n\nconst Instagram: FC = () => {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 64 64\" fill=\"none\">\n      <rect x=\"0.666667\" y=\"0.666667\" width=\"62.6667\" height=\"62.6667\" rx=\"31.3333\" stroke=\"white\" strokeOpacity=\"0.22\" strokeWidth=\"1.33333\" />\n      <g clipPath=\"url(#clip0_630_2388)\">\n        <path\n          d=\"M39.9995 16H23.9999C19.6004 16 16.0001 19.6003 16.0001 23.9998V40.0002C16.0001 44.3985 19.6004 48 23.9999 48H39.9995C44.399 48 47.9993 44.3985 47.9993 40.0002V23.9998C47.9993 19.6003 44.399 16 39.9995 16ZM45.3326 40.0002C45.3326 42.9401 42.9411 45.3333 39.9995 45.3333H23.9999C21.0596 45.3333 18.6669 42.9401 18.6669 40.0002V23.9998C18.6669 21.0591 21.0596 18.6667 23.9999 18.6667H39.9995C42.9411 18.6667 45.3326 21.0591 45.3326 23.9998V40.0002Z\"\n          fill=\"white\"\n        />\n        <path\n          d=\"M40.6669 25.3358C41.7715 25.3358 42.6669 24.4404 42.6669 23.3359C42.6669 22.2313 41.7715 21.3359 40.6669 21.3359C39.5624 21.3359 38.667 22.2313 38.667 23.3359C38.667 24.4404 39.5624 25.3358 40.6669 25.3358Z\"\n          fill=\"white\"\n        />\n        <path\n          d=\"M31.9998 24C27.5807 24 24 27.5811 24 31.9998C24 36.4169 27.5807 40.0004 31.9998 40.0004C36.4177 40.0004 39.9996 36.4169 39.9996 31.9998C39.9996 27.5811 36.4177 24 31.9998 24ZM31.9998 37.3337C29.0546 37.3337 26.6667 34.9457 26.6667 31.9998C26.6667 29.0539 29.0546 26.6667 31.9998 26.6667C34.945 26.6667 37.3329 29.0539 37.3329 31.9998C37.3329 34.9457 34.945 37.3337 31.9998 37.3337Z\"\n          fill=\"white\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_630_2388\">\n          <rect width=\"32\" height=\"32\" fill=\"white\" transform=\"translate(16 16)\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n};\n\nexport default Instagram;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAgB;IACpB,qBACE,8OAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;;0BAC/D,8OAAC;gBAAK,GAAE;gBAAW,GAAE;gBAAW,OAAM;gBAAU,QAAO;gBAAU,IAAG;gBAAU,QAAO;gBAAQ,eAAc;gBAAO,aAAY;;;;;;0BAC9H,8OAAC;gBAAE,UAAS;;kCACV,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK9D;uCAEe", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/footer.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"app_links\": \"footer-module-scss-module__CiQmEa__app_links\",\n  \"footer\": \"footer-module-scss-module__CiQmEa__footer\",\n  \"footer_bottom\": \"footer-module-scss-module__CiQmEa__footer_bottom\",\n  \"footer_left\": \"footer-module-scss-module__CiQmEa__footer_left\",\n  \"footer_right\": \"footer-module-scss-module__CiQmEa__footer_right\",\n  \"footer_top\": \"footer-module-scss-module__CiQmEa__footer_top\",\n  \"links_heading\": \"footer-module-scss-module__CiQmEa__links_heading\",\n  \"logo_footer\": \"footer-module-scss-module__CiQmEa__logo_footer\",\n  \"social_media\": \"footer-module-scss-module__CiQmEa__social_media\",\n  \"terms_policy\": \"footer-module-scss-module__CiQmEa__terms_policy\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/footer/Footer.tsx"], "sourcesContent": ["import Image from \"next/image\";\n\nimport Logo from \"@/../public/assets/images/logo.svg\";\nimport AppStore from \"@/../public/assets/images/app-store.png\";\nimport PlayStore from \"@/../public/assets/images/play-store.png\";\nimport Link from \"next/link\";\nimport Facebook from \"../svgComponents/Facebook\";\nimport XIcon from \"../svgComponents/XIcon\";\nimport LinkIn from \"../svgComponents/LinkIn\";\nimport Instagram from \"../svgComponents/Instagram\";\nimport style from \"@/styles/footer.module.scss\";\nconst Footer = () => {\n  return (\n    <>\n      <footer className={style.footer}>\n        <div className=\"container\">\n          <div className={style.footer_top}>\n            <div className=\"row row-center\">\n              <div className=\"col-md-4 col-lg-6\">\n                <div className={style.footer_left}>\n                  <Image src={Logo} alt=\"logo\" className={style.logo_footer} />\n                  <div className={style.social_media}>\n                    <Link href=\"https://www.facebook.com/profile.php?id=100078198215999&mibextid=b06tZ0\" target=\"_blank\" aria-label=\"Facebook\">\n                      <Facebook />\n                    </Link>\n                    <Link href=\"https://twitter.com/9thStratum\" target=\"_blank\" aria-label=\"Twitter\">\n                      <XIcon />\n                    </Link>\n                    <Link href=\"https://www.linkedin.com/in/aaronsalko/\" target=\"_blank\" aria-label=\"Linkedin\">\n                      <LinkIn />\n                    </Link>\n                    <Link href=\"https://www.instagram.com/9thstratum/\" target=\"_blank\" aria-label=\"Instagram\">\n                      <Instagram />\n                    </Link>\n                  </div>\n                </div>\n              </div>\n              <div className=\"col-md-4 offset-md-4 col-lg-3 offset-lg-3\">\n                <div className={style.footer_right}>\n                  <p className={style.links_heading}>Download the app now</p>\n                  <div className={style.app_links}>\n                    <Link href=\"https://play.google.com/store/apps/details?id=com.stratum9&pli=1\" target=\"_blank\">\n                      <Image src={PlayStore} alt=\"app screen\" className=\"img-fluid\" />\n                    </Link>\n                    <Link\n                      href=\"https://apps.apple.com/us/app/stratum-9/id6478380143\n \"\n                      target=\"_blank\"\n                    >\n                      <Image src={AppStore} alt=\"app screen\" className=\"img-fluid\" />\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className={style.footer_bottom}>\n            <p>{`${new Date().getFullYear()} © Stratum9 ${\"all_right_reserve\"}`}</p>\n            <div className={style.terms_policy}>\n              <Link href={\"/privacy-policy\"} target=\"_blank\">\n                Privacy Policy\n              </Link>\n              |\n              <Link href={\"/terms-and-conditions\"} target=\"_blank\">\n                Terms and Conditions\n              </Link>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AACA,MAAM,SAAS;IACb,qBACE;kBACE,cAAA,8OAAC;YAAO,WAAW,qJAAA,CAAA,UAAK,CAAC,MAAM;sBAC7B,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,UAAU;kCAC9B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,WAAW;;0DAC/B,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,oSAAA,CAAA,UAAI;gDAAE,KAAI;gDAAO,WAAW,qJAAA,CAAA,UAAK,CAAC,WAAW;;;;;;0DACzD,8OAAC;gDAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,YAAY;;kEAChC,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA0E,QAAO;wDAAS,cAAW;kEAC9G,cAAA,8OAAC,+IAAA,CAAA,UAAQ;;;;;;;;;;kEAEX,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiC,QAAO;wDAAS,cAAW;kEACrE,cAAA,8OAAC,4IAAA,CAAA,UAAK;;;;;;;;;;kEAER,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA0C,QAAO;wDAAS,cAAW;kEAC9E,cAAA,8OAAC,6IAAA,CAAA,UAAM;;;;;;;;;;kEAET,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAwC,QAAO;wDAAS,cAAW;kEAC5E,cAAA,8OAAC,gJAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,YAAY;;0DAChC,8OAAC;gDAAE,WAAW,qJAAA,CAAA,UAAK,CAAC,aAAa;0DAAE;;;;;;0DACnC,8OAAC;gDAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,SAAS;;kEAC7B,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAmE,QAAO;kEACnF,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DAAC,KAAK,sTAAA,CAAA,UAAS;4DAAE,KAAI;4DAAa,WAAU;;;;;;;;;;;kEAEpD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDAEL,QAAO;kEAEP,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DAAC,KAAK,oTAAA,CAAA,UAAQ;4DAAE,KAAI;4DAAa,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7D,8OAAC;wBAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,aAAa;;0CACjC,8OAAC;0CAAG,GAAG,IAAI,OAAO,WAAW,GAAG,YAAY,EAAE,qBAAqB;;;;;;0CACnE,8OAAC;gCAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,YAAY;;kDAChC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM;wCAAmB,QAAO;kDAAS;;;;;;oCAExC;kDAEP,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM;wCAAyB,QAAO;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;uCAEe", "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/home/<USER>"], "sourcesContent": ["\"use client\";\nimport Button from \"@/components/formElements/Button\";\nimport styles from \"@/styles/home.module.scss\";\nimport Image from \"next/image\";\nimport brands from \"../../../public/assets/images/logo-group.png\";\nimport HiringHero from \"../../../public/assets/images/hirng-hero.png\";\nimport uploadResumeImg from \"../../../public/assets/images/upload-resume-screening.png\";\nimport scoredResultsImg from \"../../../public/assets/images/scored-results-img.png\";\nimport performanceBasedHiringImg from \"../../../public/assets/images/performance-based-image.png\";\nimport DashboardImg from \"../../../public/assets/images/dashboard-admin-img.png\";\nimport Star from \"../../../public/assets/images/StarIcon.svg\";\nimport client1 from \"../../../public/assets/images/client1.png\";\nimport quotes from \"../../../public/assets/images/quotes.png\";\nimport frame from \"../../../public/assets/images/frame.png\";\nimport growth1 from \"../../../public/assets/images/growth1.png\";\nimport growth2 from \"../../../public/assets/images/growth2.png\";\nimport growth3 from \"../../../public/assets/images/growth3.png\";\nimport Footer from \"@/components/footer/Footer\";\nconst HomePage = () => {\n  return (\n    <div className={styles.home_page}>\n      <section className={`${styles.hero_section}`}>\n        <div className=\"container\">\n          <div className=\"row align-items-center g-5\">\n            <div className=\"col-md-6\">\n              <div className={`${styles.banner_content}`}>\n                <h1>\n                  Smarter Hiring with <span>AI Intelligence</span>\n                </h1>\n                <p>\n                  Say goodbye to gut-based hiring and resume overload. Stratum 9’s Hiring Manager Module brings science, structure, and insight to the\n                  heart of recruitment—so you can hire top performers, not just top profiles.\n                </p>\n                <Button className=\"dark-btn rounded-md\">Get Started</Button>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={`${styles.banner_image}`}>\n                <Image src={HiringHero} alt=\"main banner\" width={640} height={320} />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      <section className={styles.top_brand_section}>\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-md-5\">\n              <div className={`${styles.page_heading}`}>\n                <h2>\n                  Top Brands Partnered with <span>Stratum 9 Hiring</span>\n                </h2>\n                <p>We are trusted by top brands globally for their hiring needs.</p>\n              </div>\n            </div>\n            <div className=\"offset-md-1 col-md-6\">\n              <div className={`${styles.top_brand_image}`}>\n                <Image src={brands} alt=\"top brands\" />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      <section className={styles.advantage_section}>\n        <div className=\"container\">\n          <div className=\"row g-5\">\n            <div className=\"col-md-8 mb-5\">\n              <div className={styles.page_heading}>\n                <h2>\n                  What Powers the <span>Stratum 9 Advantage</span>\n                </h2>\n                <p>\n                  Designed for Precision. Built for Performance. Discover the features that turn traditional hiring into a smart, structured, and\n                  scalable process. Powered by AI, guided by psychology, and tailored for real-world results.\n                </p>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={styles.advantage_card}>\n                <div className={styles.card_body}>\n                  <h3>Structured Interviews. Scored Results</h3>\n                  <p>\n                    Interview guides generated around key performance skills. Score candidates based on what matters—drive, grit, leadership,\n                    coachability, and decision-making.\n                  </p>\n                </div>\n                <div className={styles.card_image}>\n                  <Image src={scoredResultsImg} alt=\"Scored Results\" />\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={styles.advantage_card}>\n                <div className={styles.card_body}>\n                  <h3>Performance-Based Hiring Philosophy</h3>\n                  <p>\n                    Go beyond credentials. Our module evaluates candidates based on behavioral performance, adaptability, emotional intelligence, and\n                    core competencies aligned to real-world success.\n                  </p>\n                </div>\n                <div className={styles.card_image}>\n                  <Image src={performanceBasedHiringImg} alt=\"Performance Based Hiring\" />\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={styles.advantage_card}>\n                <div className={styles.card_body}>\n                  <h3>AI-Powered Screening & Interviews</h3>\n                  <p>\n                    Instantly generate job descriptions, match resumes using AI, and conduct structured interviews with preloaded performance\n                    questions tailored to your organization’s culture and values.\n                  </p>\n                </div>\n                <div className={styles.card_image}>\n                  <Image src={uploadResumeImg} alt=\"AI Screening\" />\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={styles.advantage_card}>\n                <div className={styles.card_body}>\n                  <h3>Data-Driven Hiring Decisions</h3>\n                  <p>\n                    View real-time dashboards, top 10 candidate rankings, source tracking, interview summaries, and final assessments with AI-backed\n                    scoring metrics.\n                  </p>\n                </div>\n                <div className={styles.card_image}>\n                  <Image src={DashboardImg} alt=\"Performance Based Hiring\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      <section className={styles.growth_section}>\n        <div className=\"container\">\n          <div className=\"row mb-5 g-5\">\n            <div className=\"col-md-10 mb-5\">\n              <div className={`${styles.page_heading}`}>\n                <h2>\n                  Built for Teams That <span>Value Growth</span>\n                </h2>\n                <p>We are trusted by top brands globally for their hiring needs.</p>\n              </div>\n            </div>\n            <div className=\"col-md-4\">\n              <div className={styles.team_img}>\n                <Image src={growth1} alt=\"rectangle\" className={styles.img} width={500} height={300} />\n                <div className={styles.team_content}>\n                  <div className={styles.team_info}>\n                    <h4>HR Managers</h4>\n                    <p>Streamline hiring, reduce manual overhead, and track all candidates from job creation to final offer—inside one platform.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-4\">\n              <div className={styles.team_img}>\n                <Image src={growth2} alt=\"Team Brands\" className={styles.img} />\n                <div className={styles.team_content}>\n                  <div className={styles.team_info}>\n                    <h4>Hiring Teams & Departments</h4>\n                    <p>Empower interviewers with structured workflows, scheduling, note-taking, and automated assessment templates.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-4\">\n              <div className={styles.team_img}>\n                <Image src={growth3} alt=\"Team Brands\" className={styles.img} />\n                <div className={styles.team_content}>\n                  <div className={styles.team_info}>\n                    <h4>Executives & Founders</h4>\n                    <p>Make strategic hiring decisions based on real performance indicators—not just experience lists.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* <section className={styles.subscription_plans}>\n        <div className=\"container\">\n          <div className={`${styles.page_heading} text-center`}>\n            <h2>Subscription Plans</h2>\n            <p>We are trusted by top brands globally for their hiring needs.`</p>\n          </div>\n          <div className=\"row\">\n            <div className=\"col-md-8\">\n              <div className=\"row\">\n                <div className=\"col-md-4\">\n                  <div className={styles.subscription_card}></div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className={styles.subscription_card}></div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className={styles.subscription_card}></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section> */}\n\n      <section className={styles.expeience_section}>\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-md-6\">\n              <div className={styles.page_heading}>\n                <h2>\n                  HR Managers Love <br /> <span>The Stratum 9 Expeience</span>\n                </h2>\n                <p>We are trusted by top brands globally for their hiring needs.</p>\n              </div>\n              <div className={styles.star_content}>\n                <div className={styles.star_icon}>\n                  <Image src={Star} alt=\"star\" />\n                  <Image src={Star} alt=\"star\" />\n                  <Image src={Star} alt=\"star\" />\n                  <Image src={Star} alt=\"star\" />\n                  <Image src={Star} alt=\"star\" />\n                </div>\n                {/* <hr /> */}\n                <p className={styles.expeience_text}>\n                  Stratum 9 helped us not only find great talent—but the right kind of talent for our culture. We now hire with confidence. The\n                  platform is intuitive, fair, and truly performance-first. Our managers love the visibility it gives across all candidates.\n                </p>\n                <div className={styles.star_founder}>\n                  <div className={styles.star_founder_img}>\n                    <Image src={client1} alt=\"Ellipse_id\" className={styles.star_img} width={50} height={50} />\n                    <div className={styles.star_founder_info}>\n                      <h4>Maria Benecroft</h4>\n                      <p>\n                        Founder, <span>EasyHirey</span>\n                      </p>\n                    </div>\n                  </div>\n                  <Image src={quotes} alt=\"Quotes\" className={styles.quotes} />\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={styles.expeience_bg_img}>\n                <div className={styles.frame_img}>\n                  <Image src={frame} alt=\"path\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      <Footer />\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;;;;;;;;;AAkBA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,8OAAC;gBAAQ,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,EAAE;0BAC1C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,cAAc,EAAE;;sDACxC,8OAAC;;gDAAG;8DACkB,8OAAC;8DAAK;;;;;;;;;;;;sDAE5B,8OAAC;sDAAE;;;;;;sDAIH,8OAAC,4IAAA,CAAA,UAAM;4CAAC,WAAU;sDAAsB;;;;;;;;;;;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,EAAE;8CACtC,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCAAC,KAAK,sTAAA,CAAA,UAAU;wCAAE,KAAI;wCAAc,OAAO;wCAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMxE,8OAAC;gBAAQ,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;0BAC1C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,EAAE;;sDACtC,8OAAC;;gDAAG;8DACwB,8OAAC;8DAAK;;;;;;;;;;;;sDAElC,8OAAC;sDAAE;;;;;;;;;;;;;;;;;0CAGP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,eAAe,EAAE;8CACzC,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCAAC,KAAK,sTAAA,CAAA,UAAM;wCAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC;gBAAQ,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;0BAC1C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,YAAY;;sDACjC,8OAAC;;gDAAG;8DACc,8OAAC;8DAAK;;;;;;;;;;;;sDAExB,8OAAC;sDAAE;;;;;;;;;;;;;;;;;0CAMP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,UAAU;sDAC/B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,4UAAA,CAAA,UAAgB;gDAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;0CAIxC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,UAAU;sDAC/B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,sVAAA,CAAA,UAAyB;gDAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;0CAIjD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,UAAU;sDAC/B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,sVAAA,CAAA,UAAe;gDAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,UAAU;sDAC/B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,8UAAA,CAAA,UAAY;gDAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAQ,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;0BACvC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,EAAE;;sDACtC,8OAAC;;gDAAG;8DACmB,8OAAC;8DAAK;;;;;;;;;;;;sDAE7B,8OAAC;sDAAE;;;;;;;;;;;;;;;;;0CAGP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC7B,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAK,0SAAA,CAAA,UAAO;4CAAE,KAAI;4CAAY,WAAW,mJAAA,CAAA,UAAM,CAAC,GAAG;4CAAE,OAAO;4CAAK,QAAQ;;;;;;sDAChF,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,YAAY;sDACjC,cAAA,8OAAC;gDAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;kEAC9B,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC7B,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAK,0SAAA,CAAA,UAAO;4CAAE,KAAI;4CAAc,WAAW,mJAAA,CAAA,UAAM,CAAC,GAAG;;;;;;sDAC5D,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,YAAY;sDACjC,cAAA,8OAAC;gDAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;kEAC9B,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC7B,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAK,0SAAA,CAAA,UAAO;4CAAE,KAAI;4CAAc,WAAW,mJAAA,CAAA,UAAM,CAAC,GAAG;;;;;;sDAC5D,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,YAAY;sDACjC,cAAA,8OAAC;gDAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;kEAC9B,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCjB,8OAAC;gBAAQ,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;0BAC1C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,YAAY;;0DACjC,8OAAC;;oDAAG;kEACe,8OAAC;;;;;oDAAK;kEAAC,8OAAC;kEAAK;;;;;;;;;;;;0DAEhC,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,YAAY;;0DACjC,8OAAC;gDAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;kEAC9B,8OAAC,6HAAA,CAAA,UAAK;wDAAC,KAAK,4SAAA,CAAA,UAAI;wDAAE,KAAI;;;;;;kEACtB,8OAAC,6HAAA,CAAA,UAAK;wDAAC,KAAK,4SAAA,CAAA,UAAI;wDAAE,KAAI;;;;;;kEACtB,8OAAC,6HAAA,CAAA,UAAK;wDAAC,KAAK,4SAAA,CAAA,UAAI;wDAAE,KAAI;;;;;;kEACtB,8OAAC,6HAAA,CAAA,UAAK;wDAAC,KAAK,4SAAA,CAAA,UAAI;wDAAE,KAAI;;;;;;kEACtB,8OAAC,6HAAA,CAAA,UAAK;wDAAC,KAAK,4SAAA,CAAA,UAAI;wDAAE,KAAI;;;;;;;;;;;;0DAGxB,8OAAC;gDAAE,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;0DAAE;;;;;;0DAIrC,8OAAC;gDAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,YAAY;;kEACjC,8OAAC;wDAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,gBAAgB;;0EACrC,8OAAC,6HAAA,CAAA,UAAK;gEAAC,KAAK,0SAAA,CAAA,UAAO;gEAAE,KAAI;gEAAa,WAAW,mJAAA,CAAA,UAAM,CAAC,QAAQ;gEAAE,OAAO;gEAAI,QAAQ;;;;;;0EACrF,8OAAC;gEAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;;kFACtC,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;;4EAAE;0FACQ,8OAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;kEAIrB,8OAAC,6HAAA,CAAA,UAAK;wDAAC,KAAK,wSAAA,CAAA,UAAM;wDAAE,KAAI;wDAAS,WAAW,mJAAA,CAAA,UAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0CAI/D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,gBAAgB;8CACrC,cAAA,8OAAC;wCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;kDAC9B,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAK,sSAAA,CAAA,UAAK;4CAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnC,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe", "debugId": null}}, {"offset": {"line": 1954, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}