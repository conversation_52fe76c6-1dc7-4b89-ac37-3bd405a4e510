(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_8c2aef6c._.js", {

"[project]/src/components/svgComponents/AiMarkIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function AiMarkIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "25",
        height: "25",
        viewBox: "0 0 32 32",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M13.294 7.43666L14.097 9.66666C14.989 12.1417 16.938 14.0907 19.413 14.9827L21.643 15.7857C21.844 15.8587 21.844 16.1437 21.643 16.2157L19.413 17.0187C16.938 17.9107 14.989 19.8597 14.097 22.3347L13.294 24.5647C13.221 24.7657 12.936 24.7657 12.864 24.5647L12.061 22.3347C11.169 19.8597 9.22001 17.9107 6.74501 17.0187L4.51501 16.2157C4.31401 16.1427 4.31401 15.8577 4.51501 15.7857L6.74501 14.9827C9.22001 14.0907 11.169 12.1417 12.061 9.66666L12.864 7.43666C12.936 7.23466 13.221 7.23466 13.294 7.43666Z",
                fill: "black"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/AiMarkIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M23.3321 2.07725L23.7391 3.20625C24.1911 4.45925 25.1781 5.44625 26.4311 5.89825L27.5601 6.30525C27.6621 6.34225 27.6621 6.48625 27.5601 6.52325L26.4311 6.93025C25.1781 7.38225 24.1911 8.36925 23.7391 9.62225L23.3321 10.7513C23.2951 10.8533 23.1511 10.8533 23.1141 10.7513L22.7071 9.62225C22.2551 8.36925 21.2681 7.38225 20.0151 6.93025L18.8861 6.52325C18.7841 6.48625 18.7841 6.34225 18.8861 6.30525L20.0151 5.89825C21.2681 5.44625 22.2551 4.45925 22.7071 3.20625L23.1141 2.07725C23.1511 1.97425 23.2961 1.97425 23.3321 2.07725Z",
                fill: "black"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/AiMarkIcon.tsx",
                lineNumber: 10,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M23.3321 21.2484L23.7391 22.3774C24.1911 23.6304 25.1781 24.6174 26.4311 25.0694L27.5601 25.4764C27.6621 25.5134 27.6621 25.6574 27.5601 25.6944L26.4311 26.1014C25.1781 26.5534 24.1911 27.5404 23.7391 28.7934L23.3321 29.9224C23.2951 30.0244 23.1511 30.0244 23.1141 29.9224L22.7071 28.7934C22.2551 27.5404 21.2681 26.5534 20.0151 26.1014L18.8861 25.6944C18.7841 25.6574 18.7841 25.5134 18.8861 25.4764L20.0151 25.0694C21.2681 24.6174 22.2551 23.6304 22.7071 22.3774L23.1141 21.2484C23.1511 21.1464 23.2961 21.1464 23.3321 21.2484Z",
                fill: "black"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/AiMarkIcon.tsx",
                lineNumber: 14,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/AiMarkIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = AiMarkIcon;
const __TURBOPACK__default__export__ = AiMarkIcon;
var _c;
__turbopack_context__.k.register(_c, "AiMarkIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/AIVerifiedIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function AIVerifiedIcon() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "150",
        height: "150",
        viewBox: "0 0 196 196",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                "clip-path": "url(#clip0_13085_4945)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M84.1489 44.693C65.223 49.5916 50.4346 64.1256 45.0945 82.8875L136.833 59.1387C126.378 48.6518 112.34 42.9375 97.907 42.9375C93.328 42.9375 88.709 43.5115 84.1489 44.693Z",
                        fill: "#436EB6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 7,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M111.678 151.027C131.441 145.911 146.439 130.616 151.26 110.852L57.5745 135.105C71.3789 150.047 91.9152 156.142 111.678 151.027Z",
                        fill: "#436EB6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 11,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M189.667 81.8553L193.432 75.8991L187.438 72.1947L190.55 65.8727L184.196 62.8263L186.618 56.2101L179.977 53.8554L181.682 47.0183L174.828 45.3847L175.797 38.4047L168.809 37.5091L169.03 30.466L161.984 30.3168L161.455 23.2905L154.435 23.8918L153.163 16.9623L146.246 18.3057L144.244 11.5506L137.51 13.6215L134.802 7.11666L128.327 9.89184L124.942 3.71286L118.797 7.16081L114.775 1.37708L109.031 5.45786L104.412 0.134554L99.135 4.80401L93.9799 0L89.2284 5.20347L83.5919 0.975519L79.4207 6.65413L73.3678 3.0485L69.8253 9.13918L63.4234 6.19791L60.5473 12.6313L53.868 10.3859L51.692 17.0884L44.8129 15.5663L43.3622 22.4622L36.3591 21.6801L35.6485 28.6895L28.6033 28.6559L28.6411 35.701L21.6317 36.418L22.4201 43.419L15.5263 44.876L17.0548 51.7551L10.3544 53.9353L12.604 60.6125L6.17478 63.4928L9.12236 69.8926L3.03588 73.4414L6.64572 79.4922L0.969212 83.6675L5.20136 89.2999L0 94.0535L4.80821 99.2044L0.142964 104.486L5.47047 109.096L1.3939 114.842L7.18184 118.86L3.73809 125.007L9.92128 128.388L7.1503 134.865L13.6573 137.569L11.5906 144.305L18.3477 146.303L17.0085 153.22L23.9402 154.487L23.3452 161.507L30.3714 162.031L30.5249 169.076L37.568 168.849L38.4699 175.837L45.4478 174.864L47.0877 181.716L53.9226 180.006L56.2815 186.646L62.8957 184.22L65.9463 190.571L72.2662 187.455L75.9748 193.445L81.9268 189.675L86.2514 195.238L91.7702 190.857L96.6626 195.928L101.683 190.985L107.086 195.507L111.554 190.058L117.407 193.981L121.269 188.088L127.507 191.366L130.722 185.096L137.271 187.693L139.8 181.116L146.589 183.002L148.405 176.195L155.356 177.349L156.438 170.386L163.473 170.794L163.809 163.755L170.846 163.412L170.432 156.377L177.393 155.29L176.235 148.34L183.04 146.519L181.148 139.733L187.722 137.197L185.119 130.648L191.387 127.429L188.103 121.196L193.992 117.329L190.064 111.478L195.51 107.006L190.983 101.607L195.922 96.5827L190.846 91.6946L195.224 86.1736L189.658 81.8532L189.667 81.8553ZM119.366 180.73C73.6727 192.56 26.8751 165.008 15.047 119.316C3.21669 73.6201 30.7667 26.8226 76.4605 14.9944C122.154 3.16413 168.952 30.7162 180.782 76.41C192.61 122.104 165.06 168.901 119.366 180.73Z",
                        fill: "#436EB6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 15,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M167.926 110.497L167.228 113.896L165.458 113.532L166.156 110.133L167.926 110.497Z",
                        fill: "#436EB6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 19,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M45.6875 146.125L43.3979 143.516L44.7561 142.324L47.0456 144.931L45.6875 146.123V146.125Z",
                        fill: "#436EB6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 20,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M28.0042 85.4308L28.7022 82.0312L30.4745 82.395L29.7765 85.7946L28.0063 85.4308H28.0042Z",
                        fill: "#436EB6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 21,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M150.24 49.8047L152.532 52.4138L151.174 53.6058L148.882 50.9989L150.24 49.8068V49.8047Z",
                        fill: "#436EB6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 22,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M25.7139 109.445C25.2033 107.54 25.2725 105.96 25.9212 104.705C26.564 103.428 27.7819 102.549 29.5752 102.068C31.3684 101.588 32.8627 101.74 34.058 102.525C35.2473 103.287 36.0973 104.621 36.6078 106.526L36.806 107.266L33.3092 108.203L33.0479 107.228C32.8317 106.421 32.5186 105.88 32.1087 105.606C31.6928 105.309 31.1822 105.241 30.577 105.404C29.9718 105.566 29.5633 105.879 29.3516 106.344C29.1339 106.787 29.1331 107.412 29.3494 108.219C29.6136 109.205 30.1026 109.999 30.8164 110.601C31.5526 111.196 32.5736 111.824 33.8793 112.483C34.9698 113.056 35.8681 113.584 36.5742 114.067C37.2743 114.528 37.9321 115.145 38.5474 115.917C39.1627 116.689 39.6205 117.635 39.9208 118.756C40.4313 120.662 40.354 122.256 39.6888 123.539C39.0177 124.8 37.7855 125.671 35.9922 126.151C34.199 126.632 32.6965 126.494 31.4848 125.738C30.267 124.959 29.4029 123.617 28.8924 121.711L28.505 120.265L32.0018 119.329L32.4523 121.01C32.8607 122.534 33.7037 123.125 34.9814 122.783C36.2591 122.44 36.6937 121.507 36.2853 119.983C36.021 118.996 35.5208 118.205 34.7846 117.61C34.0709 117.008 33.0611 116.378 31.7553 115.719C30.6649 115.146 29.7696 114.629 29.0695 114.168C28.3633 113.685 27.7026 113.057 27.0873 112.285C26.472 111.513 26.0142 110.566 25.7139 109.445ZM52.8461 115.077C53.9392 119.156 52.6029 121.701 48.8371 122.71C47.0438 123.19 45.5414 123.052 44.3296 122.296C43.1119 121.517 42.2478 120.175 41.7372 118.27L41.5751 117.664L45.0719 116.727L45.2971 117.568C45.7115 119.115 46.5576 119.717 47.8353 119.374C48.5302 119.188 48.9887 118.837 49.2108 118.321C49.4329 117.805 49.3998 117.009 49.1115 115.933L47.9583 111.629C47.6806 113.193 46.7236 114.195 45.0873 114.633C43.7424 114.993 42.5994 114.807 41.6582 114.074C40.7171 113.341 40.0123 112.101 39.5438 110.352L38.3906 106.049C37.8861 104.166 37.9776 102.58 38.6652 101.29C39.3528 100.001 40.6044 99.113 42.42 98.6265C44.2357 98.14 45.7636 98.2831 47.0037 99.056C48.2439 99.8288 49.1162 101.157 49.6207 103.04L52.8461 115.077ZM45.6994 110.865C46.9771 110.523 47.4087 109.578 46.9943 108.032L45.8591 103.795C45.4507 102.271 44.6077 101.68 43.33 102.022C42.0523 102.365 41.6177 103.298 42.0261 104.822L43.1613 109.059C43.5757 110.605 44.4217 111.208 45.6994 110.865ZM59.4973 94.3389L65.8038 117.875L62.1053 118.866L55.7987 95.3299L59.4973 94.3389ZM71.8032 116.268L68.4745 117.16L62.168 93.6233L66.808 92.38L74.3823 105.45L70.6074 91.362L73.9025 90.4791L80.209 114.015L76.4096 115.033L67.2355 99.2207L71.8032 116.268ZM86.1522 112.423L82.8235 113.315L76.5169 89.7785L81.1569 88.5352L88.7313 101.605L84.9564 87.5172L88.2514 86.6343L94.558 110.171L90.7585 111.189L81.5844 95.3759L86.1522 112.423ZM102.333 93.4921L103.234 96.8544L98.1568 98.2149L100.004 105.108L106.392 103.396L107.293 106.758L97.206 109.461L90.8995 85.9247L100.986 83.2219L101.887 86.5842L95.499 88.296L97.2558 94.8525L102.333 93.4921ZM117.318 104.072C117.082 103.727 116.896 103.392 116.761 103.068C116.626 102.744 116.441 102.145 116.207 101.27L115.216 97.5719C114.928 96.4959 114.533 95.7848 114.032 95.4386C113.525 95.07 112.845 94.9998 111.993 95.2281L110.716 95.5704L113.283 105.153L109.585 106.144L103.278 82.6078L108.86 81.1123C110.765 80.6017 112.275 80.6777 113.389 81.34C114.498 81.98 115.298 83.219 115.791 85.0571L116.286 86.9064C116.935 89.3272 116.555 91.135 115.145 92.3295C116.115 92.4539 116.905 92.891 117.514 93.6407C118.118 94.3679 118.593 95.3816 118.942 96.6817L119.915 100.313C120.095 100.985 120.265 101.529 120.423 101.943C120.576 102.334 120.797 102.707 121.084 103.063L117.318 104.072ZM109.815 92.2081L111.261 91.8207C112 91.6225 112.498 91.2848 112.755 90.8076C113.034 90.3245 113.053 89.6346 112.813 88.738L112.191 86.418C111.963 85.5662 111.641 84.9918 111.225 84.6948C110.832 84.3918 110.31 84.3274 109.66 84.5016L107.878 84.9791L109.815 92.2081ZM125.147 76.7482L128.543 75.8383L131.218 100.348L125.704 101.825L115.766 79.2618L119.498 78.2618L127.467 96.7039L125.147 76.7482ZM133.967 74.3847L140.274 97.9209L136.575 98.912L130.269 75.3757L133.967 74.3847ZM148.105 81.2275L149.006 84.5898L143.929 85.9502L145.776 92.843L152.164 91.1312L153.065 94.4935L142.978 97.1963L136.672 73.6601L146.759 70.9573L147.66 74.3196L141.271 76.0314L143.028 82.5879L148.105 81.2275ZM162.216 92.0415L157.139 93.4019L148.21 70.5684L151.808 69.6044L158.765 87.6329L155.573 68.5954L159.138 67.6404L165.996 85.8394L162.903 66.6314L166.131 65.7665L169.815 90.0054L164.906 91.3208L160.201 79.1397L162.216 92.0415Z",
                        fill: "#436EB6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 23,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M51.0894 57.3542L49.0756 56.0873L47.4451 57.984L48.9999 59.7849L47.78 61.2039L39.2978 51.1756L41.2424 48.9136L52.4301 55.7947L51.0894 57.3542ZM47.5597 55.1506L41.9459 51.6196L46.2794 56.6399L47.5597 55.1506ZM47.3313 42.4979L56.0634 52.0874L54.5565 53.4596L45.8244 43.8701L47.3313 42.4979ZM60.347 33.0923L61.9587 32.1414L66.8254 44.3287L64.2083 45.8726L55.8947 35.7189L57.6661 34.6739L64.3822 42.995L60.347 33.0923ZM72.9924 33.0346L73.6868 34.7524L71.093 35.8009L72.5165 39.3223L75.7802 38.003L76.4746 39.7207L71.3213 41.8039L66.4607 29.7796L71.6139 27.6965L72.3083 29.4142L69.0446 30.7335L70.3986 34.0831L72.9924 33.0346ZM83.8828 37.5586C83.7629 37.371 83.6699 37.1897 83.6038 37.0149C83.5376 36.8401 83.4498 36.5181 83.3403 36.049L82.8772 34.0642C82.7425 33.4869 82.5448 33.1017 82.2842 32.9089C82.0207 32.704 81.6605 32.6549 81.2034 32.7615L80.5177 32.9215L81.7176 38.0638L79.7329 38.5269L76.7858 25.8967L79.7809 25.1978C80.8034 24.9592 81.6053 25.0258 82.1867 25.3975C82.7653 25.7571 83.1697 26.4302 83.3998 27.4165L83.6314 28.4089C83.9345 29.708 83.7008 30.6631 82.9301 31.2742C83.4441 31.3572 83.8567 31.6033 84.1679 32.0127C84.4762 32.4101 84.7118 32.9576 84.8746 33.6552L85.3293 35.6039C85.4135 35.9648 85.4943 36.2567 85.5717 36.4796C85.6462 36.6905 85.7569 36.893 85.9037 37.0871L83.8828 37.5586ZM80.0967 31.1172L80.8726 30.9362C81.2695 30.8435 81.5404 30.6725 81.6851 30.4231C81.8419 30.1709 81.8641 29.8042 81.7519 29.3231L81.4614 28.0781C81.3547 27.621 81.1933 27.3099 80.9772 27.1447C80.7731 26.9767 80.4967 26.9334 80.1478 27.0148L79.1915 27.2379L80.0967 31.1172ZM89.7425 23.3006L91.1863 36.1895L89.1609 36.4164L87.7171 23.5275L89.7425 23.3006ZM99.6167 28.7035L99.599 30.5562L96.9682 30.5311L96.917 35.8854L94.8791 35.8659L95.003 22.897L100.394 22.9485L100.377 24.8012L97.0233 24.7692L96.9859 28.6784L99.6167 28.7035ZM107.046 23.4013L105.482 36.2761L103.459 36.0303L105.023 23.1554L107.046 23.4013ZM115.633 30.6744L115.192 32.4739L112.474 31.8076L111.57 35.4966L114.989 36.3349L114.548 38.1344L109.149 36.8107L112.238 24.2143L117.636 25.538L117.195 27.3375L113.776 26.4991L112.916 30.0081L115.633 30.6744ZM125.84 28.3285C126.776 28.7169 127.373 29.2592 127.632 29.9553C127.892 30.6515 127.82 31.4844 127.417 32.454L124.902 38.5114C124.499 39.4811 123.96 40.1197 123.284 40.4275C122.608 40.7352 121.802 40.6948 120.867 40.3064L117.889 39.0701L122.863 27.0922L125.84 28.3285ZM120.482 38.1405L121.543 38.5811C121.851 38.709 122.123 38.7148 122.358 38.5986C122.605 38.4871 122.814 38.2261 122.984 37.8154L125.571 31.5869C125.741 31.1762 125.779 30.8441 125.683 30.5905C125.599 30.3417 125.404 30.1533 125.096 30.0254L124.035 29.5849L120.482 38.1405ZM143.684 139.898L145.664 141.218L147.343 139.364L145.836 137.523L147.092 136.136L155.31 146.382L153.307 148.593L142.303 141.422L143.684 139.898ZM147.155 142.193L152.675 145.87L148.474 140.738L147.155 142.193ZM147.032 154.859L138.572 145.029L140.116 143.699L148.577 153.529L147.032 154.859ZM133.755 163.899L132.117 164.804L127.597 152.484L130.257 151.015L138.28 161.399L136.48 162.394L130.002 153.886L133.755 163.899ZM121.096 163.595L120.454 161.857L123.078 160.887L121.761 157.324L118.459 158.545L117.817 156.807L123.03 154.88L127.527 167.045L122.314 168.972L121.671 167.234L124.973 166.014L123.72 162.625L121.096 163.595ZM110.351 158.762C110.466 158.952 110.554 159.136 110.615 159.313C110.677 159.489 110.756 159.813 110.853 160.285L111.263 162.282C111.382 162.862 111.569 163.253 111.825 163.452C112.082 163.664 112.441 163.723 112.901 163.629L113.591 163.487L112.529 158.315L114.525 157.905L117.134 170.609L114.121 171.228C113.092 171.439 112.293 171.351 111.721 170.964C111.153 170.589 110.766 169.905 110.563 168.913L110.358 167.915C110.089 166.608 110.348 165.66 111.135 165.069C110.624 164.973 110.218 164.716 109.918 164.298C109.62 163.893 109.399 163.339 109.255 162.637L108.853 160.677C108.778 160.314 108.705 160.02 108.634 159.795C108.565 159.582 108.46 159.377 108.318 159.179L110.351 158.762ZM113.963 165.302L113.183 165.462C112.784 165.544 112.508 165.708 112.357 165.953C112.194 166.201 112.161 166.567 112.261 167.051L112.518 168.304C112.612 168.763 112.765 169.079 112.977 169.25C113.177 169.423 113.452 169.474 113.803 169.402L114.765 169.204L113.963 165.302ZM104.033 172.848L103.012 159.919L105.044 159.758L106.065 172.687L104.033 172.848ZM94.4045 167.175L94.4663 165.324L97.0958 165.411L97.2743 160.06L99.3112 160.128L98.8788 173.09L93.4902 172.91L93.552 171.058L96.9037 171.17L97.034 167.263L94.4045 167.175ZM86.7796 172.272L88.7238 159.449L90.7388 159.755L88.7947 172.578L86.7796 172.272ZM78.4133 164.752L78.9066 162.966L81.6034 163.711L82.6146 160.05L79.2213 159.113L79.7146 157.327L85.0724 158.807L81.6194 171.308L76.2617 169.828L76.7549 168.043L80.1482 168.98L81.1101 165.497L78.4133 164.752ZM68.3298 166.963C67.3986 166.564 66.807 166.016 66.5552 165.317C66.3034 164.618 66.384 163.786 66.797 162.821L69.3769 156.791C69.7899 155.825 70.3361 155.192 71.0155 154.892C71.6948 154.591 72.5001 154.64 73.4313 155.039L76.3953 156.307L71.2937 168.231L68.3298 166.963ZM73.7927 157.209L72.7366 156.757C72.43 156.626 72.1583 156.617 71.9216 156.731C71.6735 156.839 71.462 157.098 71.2871 157.507L68.6343 163.708C68.4594 164.116 68.4182 164.448 68.5108 164.703C68.5921 164.952 68.786 165.143 69.0926 165.274L70.1488 165.726L73.7927 157.209Z",
                        fill: "#436EB6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 27,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                    id: "clip0_13085_4945",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        width: "195.93",
                        height: "195.928",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                        lineNumber: 34,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                    lineNumber: 33,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/AIVerifiedIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = AIVerifiedIcon;
const __TURBOPACK__default__export__ = AIVerifiedIcon;
var _c;
__turbopack_context__.k.register(_c, "AIVerifiedIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function BackArrowIcon({ onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: "cursor-pointer me-3",
        width: "26",
        height: "26",
        viewBox: "0 0 32 32",
        fill: "none",
        onClick: onClick,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z",
            fill: "#333333"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = BackArrowIcon;
const __TURBOPACK__default__export__ = BackArrowIcon;
var _c;
__turbopack_context__.k.register(_c, "BackArrowIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/CheckSecondaryIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function CheckSecondaryIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "19",
        height: "19",
        viewBox: "0 0 24 24",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                "clip-path": "url(#clip0_13082_4925)",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M12 0C5.38346 0 0 5.38346 0 12C0 18.6165 5.38346 24 12 24C18.6165 24 24 18.6165 24 12C24 5.38346 18.6165 0 12 0ZM18.7068 8.84211L11.0376 16.4511C10.5865 16.9023 9.86466 16.9323 9.38346 16.4812L5.32331 12.782C4.84211 12.3308 4.81203 11.5789 5.23308 11.0977C5.68421 10.6165 6.43609 10.5865 6.91729 11.0376L10.1353 13.985L16.9925 7.12782C17.4737 6.64662 18.2256 6.64662 18.7068 7.12782C19.188 7.60902 19.188 8.3609 18.7068 8.84211Z",
                    fill: "#CB9932"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/CheckSecondaryIcon.tsx",
                    lineNumber: 7,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/CheckSecondaryIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                    id: "clip0_13082_4925",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        width: "24",
                        height: "24",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/CheckSecondaryIcon.tsx",
                        lineNumber: 14,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/CheckSecondaryIcon.tsx",
                    lineNumber: 13,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/CheckSecondaryIcon.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/CheckSecondaryIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = CheckSecondaryIcon;
const __TURBOPACK__default__export__ = CheckSecondaryIcon;
var _c;
__turbopack_context__.k.register(_c, "CheckSecondaryIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/PreviewResumeIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function PreviewResumeIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "25",
        height: "24",
        viewBox: "0 0 32 32",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M19.4419 1.28516V1.28613C19.546 1.28464 19.6494 1.30297 19.7456 1.34277L19.8169 1.37695C19.8854 1.41401 19.948 1.46142 20.0024 1.51758L27.5933 9.10742V9.1084L27.645 9.16602C27.7603 9.30714 27.8241 9.48419 27.8247 9.66797V27.8438C27.8247 28.605 27.5222 29.3347 26.9839 29.873C26.4456 30.4114 25.7159 30.7138 24.9546 30.7139H7.04346C6.37733 30.7139 5.73491 30.483 5.22412 30.0645L5.01416 29.873C4.47584 29.3347 4.17334 28.6051 4.17334 27.8438V4.15527C4.17338 3.39401 4.47586 2.66428 5.01416 2.12598L5.22412 1.93457C5.7349 1.5161 6.37736 1.28517 7.04346 1.28516H19.4419ZM7.04541 2.87012C6.7472 2.87016 6.45962 2.97377 6.23096 3.16113L6.13721 3.24707C5.89621 3.48807 5.76029 3.81446 5.76025 4.15527V27.8438C5.76026 28.1846 5.89617 28.5119 6.13721 28.7529L6.23096 28.8379C6.45963 29.0253 6.74717 29.1289 7.04541 29.1289H24.9546L25.0806 29.123C25.2063 29.1109 25.3296 29.0796 25.4468 29.0312C25.6029 28.9668 25.7452 28.8723 25.8647 28.7529C25.9842 28.6336 26.0794 28.4919 26.144 28.3359L26.186 28.2168C26.2227 28.096 26.2417 27.9704 26.2417 27.8438V10.4609H21.1753C20.5059 10.4609 19.8635 10.195 19.3901 9.72168C18.9168 9.2483 18.6509 8.60595 18.6509 7.93652V2.87012H7.04541ZM20.2349 7.93652C20.2349 8.18554 20.3332 8.4245 20.5093 8.60059L20.5786 8.66309C20.7456 8.79983 20.9556 8.87597 21.1733 8.87598H25.1196L20.2349 3.99121V7.93652Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M12.3069 6.4873C12.7821 6.33047 13.2913 6.29936 13.7844 6.39746L13.9934 6.44727C14.4755 6.58088 14.9162 6.83691 15.2717 7.19238L15.4182 7.34961C15.7449 7.72854 15.9685 8.18664 16.0667 8.67969L16.1008 8.8916C16.162 9.38833 16.0941 9.89378 15.9016 10.3584C15.7093 10.8228 15.4003 11.2282 15.0061 11.5361L14.8313 11.6621C14.3537 11.9812 13.7924 12.1513 13.218 12.1514H13.217C12.5433 12.1504 11.8938 11.9165 11.3772 11.4932L11.1643 11.2998C10.6198 10.7553 10.3138 10.0171 10.3127 9.24707L10.3206 9.03223C10.3575 8.5332 10.5227 8.05091 10.802 7.63281L10.928 7.45801C11.2359 7.06383 11.6414 6.75485 12.1057 6.5625L12.3069 6.4873ZM13.0872 7.93359C12.7852 7.964 12.5009 8.097 12.2844 8.31348C12.037 8.56093 11.8982 8.89713 11.8977 9.24707L11.9124 9.44043C11.9409 9.63256 12.0116 9.81665 12.1204 9.97949L12.2395 10.1338C12.3699 10.2775 12.5314 10.3909 12.7122 10.4658L12.8977 10.5273C13.0859 10.5743 13.283 10.5791 13.4749 10.541L13.6624 10.4893C13.8453 10.4238 14.0121 10.3182 14.1506 10.1797C14.3352 9.99509 14.461 9.75994 14.512 9.50391L14.5364 9.31055C14.5426 9.18148 14.5296 9.05212 14.4983 8.92676L14.4368 8.74121C14.3618 8.56046 14.2485 8.39894 14.1047 8.26855L13.9504 8.14941C13.7335 8.00453 13.4788 7.92685 13.218 7.92676L13.0872 7.93359Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M14.0181 12.252L14.2183 12.2568C15.2193 12.3076 16.1692 12.7274 16.8813 13.4395L17.02 13.585C17.693 14.328 18.0686 15.2965 18.0698 16.3037V16.7266C18.0698 16.9104 18.0056 17.0875 17.8901 17.2285L17.8374 17.2871C17.6888 17.4356 17.487 17.5186 17.2769 17.5186C17.0931 17.5185 16.9158 17.4552 16.7749 17.3398L16.7163 17.2871C16.5677 17.1385 16.4849 16.9367 16.4849 16.7266V16.3037L16.4722 16.0605C16.4236 15.5765 16.2328 15.1174 15.9243 14.7412L15.7612 14.5605C15.3566 14.1559 14.8257 13.9064 14.2612 13.8496L14.0171 13.8369H12.4175C11.8453 13.8377 11.2937 14.0369 10.855 14.3965L10.6743 14.5605C10.2119 15.023 9.95162 15.6497 9.95068 16.3037V16.7266C9.95062 16.9104 9.8865 17.0875 9.771 17.2285L9.71826 17.2871C9.56964 17.4356 9.36781 17.5186 9.15771 17.5186C8.97399 17.5185 8.7967 17.4552 8.65576 17.3398L8.59717 17.2871C8.44859 17.1385 8.3658 16.9367 8.36572 16.7266V16.3037L8.37061 16.1025C8.42148 15.1015 8.84113 14.1515 9.55322 13.4395L9.69873 13.3018C10.4418 12.6286 11.4101 12.2532 12.4175 12.252H14.0181Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 16,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M17.1772 21.002L17.2554 21.0059C17.4369 21.0238 17.6077 21.1034 17.7378 21.2334C17.8864 21.382 17.9701 21.5838 17.9702 21.7939C17.9702 22.0041 17.8864 22.2058 17.7378 22.3545C17.6076 22.4847 17.437 22.5651 17.2554 22.583L17.1772 22.5869H8.77197C8.58813 22.5869 8.411 22.5227 8.27002 22.4072L8.21143 22.3545C8.06294 22.2059 7.97998 22.004 7.97998 21.7939C7.98005 21.5838 8.06284 21.382 8.21143 21.2334L8.27002 21.1807C8.41096 21.0653 8.58824 21.002 8.77197 21.002H17.1772Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 21,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M21.9321 24.6504C22.1422 24.6504 22.3441 24.7334 22.4927 24.8818L22.5454 24.9404C22.6609 25.0814 22.725 25.2585 22.7251 25.4424C22.7251 25.6526 22.6413 25.8543 22.4927 26.0029C22.344 26.1516 22.1423 26.2354 21.9321 26.2354H8.77197C8.58813 26.2353 8.411 26.1712 8.27002 26.0557L8.21143 26.0029C8.06294 25.8543 7.97998 25.6525 7.97998 25.4424C7.98005 25.2323 8.06284 25.0304 8.21143 24.8818L8.27002 24.8291C8.41096 24.7137 8.58824 24.6505 8.77197 24.6504H21.9321Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = PreviewResumeIcon;
const __TURBOPACK__default__export__ = PreviewResumeIcon;
var _c;
__turbopack_context__.k.register(_c, "PreviewResumeIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/StarIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function StarIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "16",
        height: "16",
        viewBox: "0 0 20 19",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M15.9184 11.82C15.6594 12.071 15.5404 12.434 15.5994 12.79L16.4884 17.71C16.5634 18.127 16.3874 18.549 16.0384 18.79C15.6964 19.04 15.2414 19.07 14.8684 18.87L10.4394 16.56C10.2854 16.478 10.1144 16.434 9.93939 16.429H9.66839C9.57439 16.443 9.48239 16.473 9.39839 16.519L4.96839 18.84C4.74939 18.95 4.50139 18.989 4.25839 18.95C3.66639 18.838 3.27139 18.274 3.36839 17.679L4.25839 12.759C4.31739 12.4 4.19839 12.035 3.93939 11.78L0.328388 8.28C0.0263875 7.987 -0.0786125 7.547 0.0593875 7.15C0.193388 6.754 0.535388 6.465 0.948388 6.4L5.91839 5.679C6.29639 5.64 6.62839 5.41 6.79839 5.07L8.98839 0.58C9.04039 0.48 9.10739 0.388 9.18839 0.31L9.27839 0.24C9.32539 0.188 9.37939 0.145 9.43939 0.11L9.54839 0.07L9.71839 0H10.1394C10.5154 0.039 10.8464 0.264 11.0194 0.6L13.2384 5.07C13.3984 5.397 13.7094 5.624 14.0684 5.679L19.0384 6.4C19.4584 6.46 19.8094 6.75 19.9484 7.15C20.0794 7.551 19.9664 7.991 19.6584 8.28L15.9184 11.82Z",
            fill: "#CB9932"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/StarIcon.tsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/StarIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = StarIcon;
const __TURBOPACK__default__export__ = StarIcon;
var _c;
__turbopack_context__.k.register(_c, "StarIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/commonModals/ResumeModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
// import { useTranslations } from "next-intl";
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
// 🔁 Static test link
// const resumeLink = "https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf";
const ResumeModal = ({ isOpen, onClose, resumeLink })=>{
    _s();
    // const t = useTranslations("common");
    // const [fileError, setFileError] = useState<string>("");
    // 🧠 Ref to detect clicks outside the modal
    const modalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // 🧩 Detect outside click and close modal
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ResumeModal.useEffect": ()=>{
            const handleOutsideClick = {
                "ResumeModal.useEffect.handleOutsideClick": (event)=>{
                    if (modalRef.current && !modalRef.current.contains(event.target)) {
                        onClose();
                    }
                }
            }["ResumeModal.useEffect.handleOutsideClick"];
            if (isOpen) {
                document.addEventListener("mousedown", handleOutsideClick);
            }
            return ({
                "ResumeModal.useEffect": ()=>{
                    document.removeEventListener("mousedown", handleOutsideClick);
                }
            })["ResumeModal.useEffect"];
        }
    }["ResumeModal.useEffect"], [
        isOpen,
        onClose
    ]);
    // ✅ Prevent rendering if modal not open
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "modal theme-modal show-modal modal-lg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "modal-dialog modal-dialog-centered",
            ref: modalRef,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-header",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "m-0",
                                children: "Resume Preview"
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/ResumeModal.tsx",
                                lineNumber: 50,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: "modal-close-btn",
                                onClick: onClose,
                                type: "button",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/ResumeModal.tsx",
                                    lineNumber: 52,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/ResumeModal.tsx",
                                lineNumber: 51,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/ResumeModal.tsx",
                        lineNumber: 49,
                        columnNumber: 11
                    }, this),
                    resumeLink && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-body",
                        style: {
                            height: "80vh"
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("iframe", {
                            src: resumeLink,
                            className: "w-100 h-100",
                            title: "Resume Preview",
                            style: {
                                border: "none"
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/commonModals/ResumeModal.tsx",
                            lineNumber: 59,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/commonModals/ResumeModal.tsx",
                        lineNumber: 58,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/commonModals/ResumeModal.tsx",
                lineNumber: 47,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/ResumeModal.tsx",
            lineNumber: 46,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/commonModals/ResumeModal.tsx",
        lineNumber: 45,
        columnNumber: 5
    }, this);
};
_s(ResumeModal, "iXNJws+mDn9J+ZcpHudMXHGV85c=");
_c = ResumeModal;
const __TURBOPACK__default__export__ = ResumeModal;
var _c;
__turbopack_context__.k.register(_c, "ResumeModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/commonModals/ConfirmationModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-client] (ecmascript)");
;
;
;
;
const ConfirmationModal = ({ isOpen, onClose, onConfirm, title, message, confirmButtonText = "Confirm", cancelButtonText, children, loading, loadingText = "Proceeding..." })=>{
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "modal theme-modal show-modal",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-dialog modal-dialog-centered",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "modal-content",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "modal-header justify-content-center pb-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "m-0",
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                                    lineNumber: 39,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    className: `modal-close-btn ${loading ? "fade-close" : ""}`,
                                    onClick: onClose,
                                    disabled: loading,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                                        lineNumber: 42,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                                    lineNumber: 41,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                            lineNumber: 38,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "modal-body pt-0",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-center pb-4 font16",
                                    children: [
                                        message,
                                        children
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                                    lineNumber: 47,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "button-align",
                                    children: [
                                        cancelButtonText && cancelButtonText.trim() !== "" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            className: "dark-outline-btn rounded-md w-100",
                                            onClick: onClose,
                                            disabled: loading,
                                            children: cancelButtonText
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                                            lineNumber: 54,
                                            columnNumber: 19
                                        }, this),
                                        confirmButtonText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            className: `primary-btn rounded-md w-100 ${loading ? "opacity-75" : ""}`,
                                            onClick: onConfirm,
                                            disabled: loading,
                                            children: loading ? loadingText : confirmButtonText
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                                            lineNumber: 59,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                                    lineNumber: 52,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                            lineNumber: 45,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                    lineNumber: 37,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
                lineNumber: 36,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/ConfirmationModal.tsx",
            lineNumber: 35,
            columnNumber: 7
        }, this)
    }, void 0, false);
};
_c = ConfirmationModal;
const __TURBOPACK__default__export__ = ConfirmationModal;
var _c;
__turbopack_context__.k.register(_c, "ConfirmationModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/constants/jobRequirementConstant.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "APPLICATION_STATUS": (()=>APPLICATION_STATUS),
    "CATEGORY_OPTION": (()=>CATEGORY_OPTION),
    "COMPLIANCE_LINK": (()=>COMPLIANCE_LINK),
    "COMPLIANCE_OPTIONS": (()=>COMPLIANCE_OPTIONS),
    "CURRENCY_SYMBOL": (()=>CURRENCY_SYMBOL),
    "CURSOR_POINT": (()=>CURSOR_POINT),
    "DEPARTMENT_OPTION": (()=>DEPARTMENT_OPTION),
    "EXPERIENCE_LEVEL_OPTIONS": (()=>EXPERIENCE_LEVEL_OPTIONS),
    "FILE_NAME": (()=>FILE_NAME),
    "FILE_SIZE_LIMIT": (()=>FILE_SIZE_LIMIT),
    "FILE_TYPE": (()=>FILE_TYPE),
    "HIRING_TYPE": (()=>HIRING_TYPE),
    "HIRING_TYPE_KEY": (()=>HIRING_TYPE_KEY),
    "JOB_GENERATION_UPLOAD_MESSAGES": (()=>JOB_GENERATION_UPLOAD_MESSAGES),
    "LOCATION_TYPE_OPTIONS": (()=>LOCATION_TYPE_OPTIONS),
    "MAX_FILE_SIZE": (()=>MAX_FILE_SIZE),
    "SALARY_CYCLE_OPTIONS": (()=>SALARY_CYCLE_OPTIONS),
    "SALARY_REMOVE_SYMBOL_REGEX": (()=>SALARY_REMOVE_SYMBOL_REGEX),
    "SKILL_CATEGORY": (()=>SKILL_CATEGORY),
    "SKILL_TYPE": (()=>SKILL_TYPE),
    "SUN_EDITOR_BUTTON_LIST": (()=>SUN_EDITOR_BUTTON_LIST),
    "TONE_STYLE_OPTIONS": (()=>TONE_STYLE_OPTIONS)
});
const CATEGORY_OPTION = [
    {
        label: "Full time",
        value: "full_time"
    },
    {
        label: "Part time",
        value: "part_time"
    },
    {
        label: "Contract",
        value: "contract"
    },
    {
        label: "Internship",
        value: "internship"
    },
    {
        label: "Freelance",
        value: "freelance"
    }
];
const SALARY_CYCLE_OPTIONS = [
    {
        label: "Per Hour",
        value: "per hour"
    },
    {
        label: "Per Month",
        value: "per month"
    },
    {
        label: "Per Annum",
        value: "per annum"
    }
];
const LOCATION_TYPE_OPTIONS = [
    {
        label: "Remote",
        value: "remote"
    },
    {
        label: "Hybrid",
        value: "hybrid"
    },
    {
        label: "On-site",
        value: "onsite"
    }
];
const TONE_STYLE_OPTIONS = [
    {
        label: "Professional & Formal",
        value: "Professional_Formal"
    },
    {
        label: "Conversational & Approachable",
        value: "Conversational_Approachable"
    },
    {
        label: "Bold & Energetic",
        value: "Bold_Energetic"
    },
    {
        label: "Inspirational & Mission-Driven",
        value: "Inspirational_Mission-Driven"
    },
    {
        label: "Technical & Precise",
        value: "Technical_Precise"
    },
    {
        label: "Creative & Fun",
        value: "Creative_Fun"
    },
    {
        label: "Inclusive & Human-Centered",
        value: "Inclusive_Human-Centered"
    },
    {
        label: "Minimalist & Straightforward",
        value: "Minimalist_Straightforward"
    }
];
const COMPLIANCE_OPTIONS = [
    {
        label: "Equal Employment Opportunity (EEO) Statement",
        value: "Equal Employment Opportunity (EEO) Statement"
    },
    {
        label: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)",
        value: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)"
    },
    {
        label: "Disability Accommodation Statement",
        value: "Disability Accommodation Statement"
    },
    {
        label: "Veterans Preference Statement (For Government Agencies and Federal Contractors)",
        value: "Veterans Preference Statement (For Government Agencies and Federal Contractors)"
    },
    {
        label: "Diversity & Inclusion Commitment",
        value: "Diversity & Inclusion Commitment"
    },
    {
        label: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)",
        value: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)"
    },
    {
        label: "Background Check and Drug-Free Workplace Policy (If Applicable)",
        value: "Background Check and Drug-Free Workplace Policy (If Applicable)"
    },
    {
        label: "Work Authorization & Immigration Statement",
        value: "Work Authorization & Immigration Statement"
    }
];
const EXPERIENCE_LEVEL_OPTIONS = [
    {
        label: "General",
        value: "General"
    },
    {
        label: "No experience necessary",
        value: "No experience necessary"
    },
    {
        label: "Entry-Level Position",
        value: "Entry-Level Position"
    },
    {
        label: "Mid-Level Professional",
        value: "Mid-Level Professional"
    },
    {
        label: "Senior/Experienced Professional",
        value: "Senior/Experienced Professional"
    },
    {
        label: "Managerial/Executive Level",
        value: "Managerial/Executive Level"
    },
    {
        label: "Specialized Expert",
        value: "Specialized Expert"
    }
];
const DEPARTMENT_OPTION = [
    {
        label: "IT",
        value: "IT"
    },
    {
        label: "HR",
        value: "HR"
    },
    {
        label: "Marketing",
        value: "Marketing"
    },
    {
        label: "Finance",
        value: "Finance"
    },
    {
        label: "Sales",
        value: "Sales"
    }
];
const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const FILE_TYPE = "application/pdf";
const FILE_NAME = ".pdf";
const SALARY_REMOVE_SYMBOL_REGEX = /[\$\s]/g;
const CURRENCY_SYMBOL = "$";
const SUN_EDITOR_BUTTON_LIST = [
    [
        "font",
        "fontSize",
        "formatBlock"
    ],
    [
        "bold",
        "underline",
        "italic"
    ],
    [
        "fontColor",
        "hiliteColor"
    ],
    [
        "align",
        "list",
        "lineHeight"
    ]
];
const HIRING_TYPE = {
    INTERNAL: "internal",
    EXTERNAL: "external"
};
const SKILL_CATEGORY = {
    Personal_Health: "Personal Health",
    Social_Interaction: "Social Interaction",
    Mastery_Of_Emotions: "Mastery of Emotions",
    Mentality: "Mentality",
    Cognitive_Abilities: "Cognitive Abilities"
};
const APPLICATION_STATUS = {
    PENDING: "Pending",
    APPROVED: "Approved",
    REJECTED: "Rejected",
    HIRED: "Hired",
    ON_HOLD: "On-Hold",
    FINAL_REJECT: "Final-Reject"
};
const SKILL_TYPE = {
    ROLE: "role",
    CULTURE: "culture"
};
const HIRING_TYPE_KEY = "hiringType";
const CURSOR_POINT = {
    cursor: "pointer"
};
const COMPLIANCE_LINK = "https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf";
const JOB_GENERATION_UPLOAD_MESSAGES = [
    "Analyzing your job description...",
    "Extracting key requirements...",
    "Processing document content...",
    "Identifying skills and qualifications...",
    "Parsing job details...",
    "Almost ready..."
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/CandidatesServices/candidatesApplicationServices.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addApplicantAdditionalInfo": (()=>addApplicantAdditionalInfo),
    "fetchCandidateProfile": (()=>fetchCandidateProfile),
    "fetchCandidatesApplications": (()=>fetchCandidatesApplications),
    "fetchTopCandidatesApplications": (()=>fetchTopCandidatesApplications),
    "generateFinalSummary": (()=>generateFinalSummary),
    "getApplicationFinalSummary": (()=>getApplicationFinalSummary),
    "getCandidateInterviewHistory": (()=>getCandidateInterviewHistory),
    "promoteDemoteCandidate": (()=>promoteDemoteCandidate),
    "updateJobApplicationStatus": (()=>updateJobApplicationStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
;
;
const fetchCandidatesApplications = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.GET_CANDIDATES_WITH_APPLICATIONS, {
        ...data
    });
};
const fetchTopCandidatesApplications = (jobId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.GET_TOP_CANDIDATES_WITH_APPLICATIONS, {
        jobId
    });
};
const promoteDemoteCandidate = async (payload)=>{
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["put"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.PROMOTE_DEMOTE_CANDIDATE, payload);
};
const addApplicantAdditionalInfo = async (payload)=>{
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.ADDITIONAL_INFO, payload);
};
const fetchCandidateProfile = (jobApplicationId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.GET_CANDIDATE_DETAILS, {
        jobApplicationId
    });
};
const updateJobApplicationStatus = async (jobApplicationId, status)=>{
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["put"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.UPDATE_JOB_APPLICATION_STATUS.replace(":jobApplicationId", jobApplicationId.toString()), {
        status
    });
};
const getCandidateInterviewHistory = async (applicationId)=>{
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.GET_CANDIDATE_INTERVIEW_HISTORY.replace(":applicationId", applicationId));
};
const getApplicationFinalSummary = async (jobApplicationId)=>{
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.GET_APPLICATION_FINAL_SUMMARY.replace(":jobApplicationId", jobApplicationId));
};
const generateFinalSummary = async (jobApplicationId)=>{
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.GENERATE_FINAL_SUMMARY, {
        jobApplicationId: jobApplicationId.toString()
    });
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/assets/images/noImageFound.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/noImageFound.23a7dd84.svg");}}),
"[project]/public/assets/images/noImageFound.svg.mjs { IMAGE => \"[project]/public/assets/images/noImageFound.svg (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/images/noImageFound.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 464,
    height: 464,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/FinalAssessmentIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function FinalAssessmentIcon() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "22",
        height: "22",
        viewBox: "0 0 32 32",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M25.4107 3.76469H22.4252C22.0367 2.66931 20.9905 1.88231 19.7636 1.88231H18.6612C18.2727 0.78675 17.226 0 15.9989 0C14.7719 0 13.7251 0.78675 13.3367 1.88238H12.2342C11.0072 1.88238 9.96111 2.66931 9.57261 3.76475H6.58717C5.0303 3.76475 3.76367 5.03137 3.76367 6.58825V29.1765C3.76367 30.7334 5.0303 32 6.58717 32H25.4107C26.9676 32 28.2342 30.7334 28.2342 29.1765V6.58825C28.2342 5.03131 26.9676 3.76469 25.4107 3.76469ZM11.293 4.70587C11.293 4.18694 11.7152 3.76469 12.2342 3.76469H14.1166C14.6364 3.76469 15.0578 3.34331 15.0578 2.8235C15.0578 2.30456 15.48 1.88231 15.999 1.88231C16.5179 1.88231 16.9402 2.3045 16.9402 2.8235C16.9402 3.34331 17.3615 3.76469 17.8814 3.76469H19.7637C20.2827 3.76469 20.7049 4.18687 20.7049 4.70587V5.64706H11.293V4.70587ZM26.3519 29.1765C26.3519 29.6954 25.9297 30.1177 25.4107 30.1177H6.58717C6.06823 30.1177 5.64598 29.6955 5.64598 29.1765V6.58825C5.64598 6.06931 6.06817 5.64706 6.58717 5.64706H9.41073V6.58825C9.41073 7.10806 9.83211 7.52944 10.3519 7.52944H21.646C22.1658 7.52944 22.5872 7.10806 22.5872 6.58825V5.64706H25.4107C25.9297 5.64706 26.3519 6.06925 26.3519 6.58825V29.1765H26.3519Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/FinalAssessmentIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M13.1766 11.293H9.41189C8.89214 11.293 8.4707 11.7143 8.4707 12.2342C8.4707 12.754 8.89208 13.1753 9.41189 13.1753H13.1766C13.6963 13.1753 14.1178 12.754 14.1178 12.2342C14.1178 11.7143 13.6963 11.293 13.1766 11.293Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/FinalAssessmentIcon.tsx",
                lineNumber: 7,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M13.1766 17.8809H9.41189C8.89214 17.8809 8.4707 18.3022 8.4707 18.822C8.4707 19.3418 8.89208 19.7632 9.41189 19.7632H13.1766C13.6963 19.7632 14.1178 19.3419 14.1178 18.822C14.1177 18.3022 13.6963 17.8809 13.1766 17.8809Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/FinalAssessmentIcon.tsx",
                lineNumber: 8,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M13.1766 24.4707H9.41189C8.89214 24.4707 8.4707 24.8921 8.4707 25.4119C8.4707 25.9317 8.89208 26.3531 9.41189 26.3531H13.1766C13.6963 26.3531 14.1178 25.9317 14.1178 25.4119C14.1178 24.8921 13.6963 24.4707 13.1766 24.4707Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/FinalAssessmentIcon.tsx",
                lineNumber: 9,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M23.254 10.6272C22.8865 10.2597 22.2905 10.2597 21.923 10.6273L19.765 12.7853L18.5481 11.5685C18.1806 11.2009 17.5846 11.2009 17.2171 11.5685C16.8495 11.936 16.8495 12.5319 17.2171 12.8995L19.0995 14.7819C19.2833 14.9656 19.5241 15.0575 19.765 15.0575C20.0059 15.0575 20.2468 14.9656 20.4305 14.7818L23.2541 11.9583C23.6215 11.5907 23.6215 10.9948 23.254 10.6272Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/FinalAssessmentIcon.tsx",
                lineNumber: 10,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M23.254 16.2757C22.8865 15.9081 22.2905 15.9081 21.923 16.2757L19.765 18.4337L18.5481 17.2169C18.1806 16.8494 17.5846 16.8494 17.2171 17.2169C16.8495 17.5845 16.8495 18.1804 17.2171 18.5479L19.0995 20.4303C19.2833 20.614 19.5241 20.7059 19.765 20.7059C20.0059 20.7059 20.2468 20.614 20.4305 20.4302L23.2541 17.6067C23.6215 17.2391 23.6215 16.6432 23.254 16.2757Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/FinalAssessmentIcon.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M21.096 25.4134L22.3128 24.1965C22.6803 23.829 22.6803 23.2331 22.3128 22.8656C21.9453 22.498 21.3493 22.498 20.9818 22.8656L19.765 24.0824L18.5481 22.8655C18.1806 22.498 17.5846 22.498 17.2171 22.8655C16.8495 23.2331 16.8495 23.829 17.2171 24.1965L18.434 25.4134L17.2171 26.6303C16.8495 26.9978 16.8495 27.5937 17.2171 27.9613C17.4008 28.145 17.6418 28.2369 17.8826 28.2369C18.1235 28.2369 18.3644 28.145 18.5481 27.9612L19.765 26.7444L20.9819 27.9613C21.1656 28.145 21.4065 28.2369 21.6474 28.2369C21.8883 28.2369 22.1291 28.145 22.3129 27.9612C22.6805 27.5936 22.6805 26.9978 22.3129 26.6302L21.096 25.4134Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/FinalAssessmentIcon.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/FinalAssessmentIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = FinalAssessmentIcon;
const __TURBOPACK__default__export__ = FinalAssessmentIcon;
var _c;
__turbopack_context__.k.register(_c, "FinalAssessmentIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/assessmentService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAssessmentQuestion": (()=>createAssessmentQuestion),
    "createFinalAssessment": (()=>createFinalAssessment),
    "getAssessmentStatus": (()=>getAssessmentStatus),
    "getAssessmentToken": (()=>getAssessmentToken),
    "getFinalAssessmentByCandidate": (()=>getFinalAssessmentByCandidate),
    "getFinalAssessmentQuestions": (()=>getFinalAssessmentQuestions),
    "shareAssessmentToCandidate": (()=>shareAssessmentToCandidate),
    "submitAssessment": (()=>submitAssessment),
    "verifyCandidateEmail": (()=>verifyCandidateEmail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
;
;
const getAssessmentStatus = (jobApplicationId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].assessment.GET_ASSESSMENT_STATUS, {
        jobApplicationId
    });
};
const createFinalAssessment = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].assessment.CREATE_FINAL_ASSESSMENT, data);
};
const getFinalAssessmentQuestions = (finalAssessmentId, jobId, jobApplicationId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].assessment.GET_FINAL_ASSESSMENT_QUESTIONS, {
        finalAssessmentId,
        jobId,
        jobApplicationId
    });
};
const createAssessmentQuestion = (data)=>{
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].assessment.CREATE_ASSESSMENT_QUESTION;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(url, data);
};
const shareAssessmentToCandidate = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].assessment.SHARE_ASSESSMENT, data);
};
const getFinalAssessmentByCandidate = (finalAssessmentId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].assessment.GET_FINAL_ASSESSMENT_BY_CANDIDATE, {
        finalAssessmentId
    });
};
const submitAssessment = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].assessment.SUBMIT_ASSESSMENT, data);
};
const verifyCandidateEmail = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].assessment.VERIFY_CANDIDATE_EMAIL, data);
};
const getAssessmentToken = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].assessment.GENERATE_ASSESSMENT_TOKEN, data);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/loader/questionGeneratorLoader.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "loader": "questionGeneratorLoader-module__dUgNXW__loader",
  "loaderAnim": "questionGeneratorLoader-module__dUgNXW__loaderAnim",
  "loader_container": "questionGeneratorLoader-module__dUgNXW__loader_container",
  "loader_text": "questionGeneratorLoader-module__dUgNXW__loader_text",
  "loader_wrapper": "questionGeneratorLoader-module__dUgNXW__loader_wrapper",
  "question_generator_loader_overlay": "questionGeneratorLoader-module__dUgNXW__question_generator_loader_overlay",
});
}}),
"[project]/src/components/loader/QuestionGeneratorLoader.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$questionGeneratorLoader$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/loader/questionGeneratorLoader.module.css [app-client] (css module)");
"use client";
;
;
const QuestionGeneratorLoader = ({ show })=>{
    if (!show) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$questionGeneratorLoader$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].question_generator_loader_overlay,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$questionGeneratorLoader$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loader_wrapper,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$questionGeneratorLoader$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loader_container,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$questionGeneratorLoader$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loader
                    }, void 0, false, {
                        fileName: "[project]/src/components/loader/QuestionGeneratorLoader.tsx",
                        lineNumber: 17,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/loader/QuestionGeneratorLoader.tsx",
                    lineNumber: 16,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$questionGeneratorLoader$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loader_text,
                    children: "Generating Questions..."
                }, void 0, false, {
                    fileName: "[project]/src/components/loader/QuestionGeneratorLoader.tsx",
                    lineNumber: 19,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/loader/QuestionGeneratorLoader.tsx",
            lineNumber: 15,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/loader/QuestionGeneratorLoader.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
};
_c = QuestionGeneratorLoader;
const __TURBOPACK__default__export__ = QuestionGeneratorLoader;
var _c;
__turbopack_context__.k.register(_c, "QuestionGeneratorLoader");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
const FinalAssessmentConfirmModal = ({ onClickCancel, onClickGenerate, disabled })=>{
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "modal theme-modal show-modal",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "modal-dialog modal-dialog-centered modal-xl",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-header text-center pb-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                children: t("generate_final_candidate_assessment")
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                                lineNumber: 21,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "m-0 textMd w100 text-center",
                                children: t("before_proceeding_please_review_the_candidates_overall_performance")
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                                lineNumber: 22,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: "modal-close-btn",
                                onClick: onClickCancel,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                                    lineNumber: 24,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                                lineNumber: 23,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                        lineNumber: 20,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-body position-relative",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "alert alert-warning mb-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "m-0",
                                    children: t("final_assessment_warning_message")
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                                    lineNumber: 29,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                                lineNumber: 28,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "action-btn justify-content-end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    className: "primary-btn rounded-md",
                                    onClick: onClickGenerate,
                                    disabled: disabled,
                                    children: t("generate_final_assessment")
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                                    lineNumber: 33,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                                lineNumber: 32,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                        lineNumber: 27,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
                lineNumber: 19,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
            lineNumber: 18,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
};
_s(FinalAssessmentConfirmModal, "h6+q2O3NJKPY5uL0BIJGLIanww8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = FinalAssessmentConfirmModal;
const __TURBOPACK__default__export__ = FinalAssessmentConfirmModal;
var _c;
__turbopack_context__.k.register(_c, "FinalAssessmentConfirmModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/styles/conductInterview.module.scss.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "additional": "conductInterview-module-scss-module__yztraq__additional",
  "completed": "conductInterview-module-scss-module__yztraq__completed",
  "conduct_interview": "conductInterview-module-scss-module__yztraq__conduct_interview",
  "conduct_interview_page": "conductInterview-module-scss-module__yztraq__conduct_interview_page",
  "current": "conductInterview-module-scss-module__yztraq__current",
  "question_info_box": "conductInterview-module-scss-module__yztraq__question_info_box",
  "summary_card_height": "conductInterview-module-scss-module__yztraq__summary_card_height",
});
}}),
"[project]/src/components/views/conductInterview/CandidateProfile.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$chartjs$2d$2$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-chartjs-2/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/chart.js/dist/chart.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$circular$2d$progressbar$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-circular-progressbar/dist/index.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$avatar$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/react-avatar/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$AiMarkIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/AiMarkIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$AIVerifiedIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/AIVerifiedIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$CheckSecondaryIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/CheckSecondaryIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$PreviewResumeIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/PreviewResumeIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$StarIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/StarIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/loader/Loader.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$ResumeModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/ResumeModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$ConfirmationModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/ConfirmationModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/jobRequirementConstant.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/CandidatesServices/candidatesApplicationServices.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/noImageFound.svg.mjs { IMAGE => "[project]/public/assets/images/noImageFound.svg (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$FinalAssessmentIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/FinalAssessmentIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$assessmentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/assessmentService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$QuestionGeneratorLoader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/loader/QuestionGeneratorLoader.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$FinalAssessmentConfirmModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/FinalAssessmentConfirmModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$conductInterview$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/conductInterview.module.scss.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const BUTTON_STATES = {
    IDLE: "idle",
    LOADING: "loading",
    GENERATING: "generating"
};
/**
 * CandidateProfile Component
 *
 * A comprehensive candidate profile view that displays candidate information,
 * skill assessments, interview history, and provides hiring/rejection functionality.
 *
 * Features:
 * - Tabbed interface for skill assessment and interview history
 * - Interactive charts for skill visualization
 * - Real-time data loading with loading states
 * - Hire/Reject candidate functionality
 * - AI-powered analysis and recommendations
 *
 * @param props - Component props containing candidate ID parameter
 * @returns JSX.Element - The rendered candidate profile component
 */ const CandidateProfile = ({ params })=>{
    _s();
    // ============================================================================
    // HOOKS AND EXTERNAL DATA
    // ============================================================================
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "CandidateProfile.useSelector[authData]": (state)=>state.auth.authData
    }["CandidateProfile.useSelector[authData]"]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])();
    // Extract candidate ID from params
    const paramsPromise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["use"])(params);
    const jobApplicationId = paramsPromise.jobApplicationId;
    // ============================================================================
    // STATE DECLARATIONS
    // ============================================================================
    // UI State - Controls tab selection and active skill display
    /** Controls which main tab is active (true = Skill Assessment, false = Interview History) */ const [selectedTab, setSelectedTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    /** Currently selected skill tab for detailed view */ const [activeSkillTab, setActiveSkillTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    /** Animation value for circular progress bar (0-100) */ const [animationValue, setAnimationValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // Data State - Stores fetched candidate information
    /** Main candidate profile data from API */ const [candidateProfileData, setCandidateProfileData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    /** Candidate's interview history across all rounds */ const [candidateInterviewHistory, setCandidateInterviewHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    /** Detailed skill-specific assessment data */ const [skillSpecificAssessment, setSkillSpecificAssessment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    /** Final assessment summary and recommendations */ const [finalAssessment, setFinalAssessment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Loading State - Tracks API call progress
    /** Loading state for final assessment and skill assessment API call (combined) */ const [isLoadingFinalAssessment, setIsLoadingFinalAssessment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    /** Loading state for interview history API call */ const [isLoadingInterviewHistory, setIsLoadingInterviewHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    /** Processing state for hire/reject actions */ const [isProcessing, setIsProcessing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    /** Loading state for generate final summary action */ const [isGeneratingFinalSummary, setIsGeneratingFinalSummary] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    /** Flag to track if user has generated final summary */ const [hasFinalSummaryBeenGenerated, setHasFinalSummaryBeenGenerated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Data Loaded Flags - Prevents unnecessary API calls
    /** Flag indicating if skill assessment data has been loaded */ const [skillAssessmentLoaded, setSkillAssessmentLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    /** Flag indicating if final assessment data has been loaded */ const [finalAssessmentLoaded, setFinalAssessmentLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    /** Flag indicating if interview history data has been loaded */ const [interviewHistoryLoaded, setInterviewHistoryLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    /** Flag incdicating if candidate profile data has been loaded */ const [candidateProfileLoaded, setCandidateProfileLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // const searchParams = useSearchParams() || new URLSearchParams();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [buttonState, setButtonState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(BUTTON_STATES.IDLE);
    const [assessmentStatus, setAssessmentStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    const [showConfirmModal, setShowConfirmModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Confirmation modal states for different actions
    const [confirmationModal, setConfirmationModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isOpen: false,
        action: "",
        title: "",
        message: "",
        confirmButtonText: "",
        onConfirm: {
            "CandidateProfile.useState": ()=>{}
        }["CandidateProfile.useState"],
        loading: false
    });
    console.log(skillAssessmentLoaded, finalAssessmentLoaded, interviewHistoryLoaded);
    const [resumeModal, setResumeModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isOpen: false,
        resumeLink: null
    });
    // const showFinalAssessmentConfirmation = () => {
    //   if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {
    //     toastMessageError(t("job_id_and_job_application_id_are_required"));
    //     return;
    //   }
    //   setShowConfirmModal(true);
    // };
    // Helper functions to show confirmation modals for different actions
    const showCreateFinalAssessmentConfirmation = ()=>{
        setConfirmationModal({
            isOpen: true,
            action: "createFinalAssessment",
            title: t("create_final_assessment"),
            message: t("are_you_sure_you_want_to_create_final_assessment"),
            confirmButtonText: t("create"),
            onConfirm: handleCreateFinalAssessmentConfirmed,
            loading: false
        });
    };
    const showGenerateFinalSummaryConfirmation = ()=>{
        setConfirmationModal({
            isOpen: true,
            action: "generateFinalSummary",
            title: t("generate_final_summary"),
            message: "Once the final summary is generated for this candidate, they will no longer be eligible to participate in any additional interview rounds. If any interview round remains incomplete, please ensure that it is conducted before proceeding with the final summary for this candidate.",
            confirmButtonText: t("generate"),
            onConfirm: handleGenerateFinalSummaryConfirmed,
            loading: false
        });
    };
    const showHireConfirmation = ()=>{
        setConfirmationModal({
            isOpen: true,
            action: "hire",
            title: t("hire_candidate"),
            message: t("are_you_sure_you_want_to_hire_this_candidate"),
            confirmButtonText: t("hire"),
            onConfirm: ()=>handleHireRejectCandidateConfirmed(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].HIRED),
            loading: false
        });
    };
    const showRejectConfirmation = ()=>{
        setConfirmationModal({
            isOpen: true,
            action: "reject",
            title: t("reject_candidate"),
            message: t("are_you_sure_you_want_to_reject_this_candidate"),
            confirmButtonText: t("reject"),
            onConfirm: ()=>handleHireRejectCandidateConfirmed(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].FINAL_REJECT),
            loading: false
        });
    };
    const closeConfirmationModal = ()=>{
        setConfirmationModal({
            isOpen: false,
            action: "",
            title: "",
            message: "",
            confirmButtonText: "",
            onConfirm: ()=>{},
            loading: false
        });
    };
    /**
   * Handle creating final assessment (confirmed action)
   */ const handleCreateFinalAssessmentConfirmed = async ()=>{
        if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("job_id_and_job_application_id_are_required"));
            closeConfirmationModal();
            return;
        }
        try {
            // Update modal loading state
            setConfirmationModal((prev)=>({
                    ...prev,
                    loading: true
                }));
            setIsLoading(true);
            setButtonState(BUTTON_STATES.GENERATING);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$assessmentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createFinalAssessment"])({
                jobId: candidateProfileData?.jobId,
                jobApplicationId: candidateProfileData?.jobApplicationId
            });
            if (response && response.data && response.data.success) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t(response?.data?.message || "final_assessment_created_successfully"));
                // Get the finalAssessmentId from the response data
                const finalAssessmentId = response.data.data?.assessmentId;
                if (finalAssessmentId) {
                    // Redirect to the final assessment page with the finalAssessmentId and default status values
                    // For a newly created assessment, both isShared and isSubmitted will be false
                    router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${finalAssessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`);
                }
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message || "failed_to_create_final_assessment"));
            }
        } catch (error) {
            console.error("Error creating final assessment:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("an_error_occurred_while_creating_the_final_assessment"));
        } finally{
            setIsLoading(false);
            setButtonState(BUTTON_STATES.IDLE);
            closeConfirmationModal();
        }
    };
    /**
   * Handle creating final assessment (original function for backward compatibility)
   */ const handleCreateFinalAssessment = async ()=>{
        if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("job_id_and_job_application_id_are_required"));
            return;
        }
        try {
            setIsLoading(true);
            setButtonState(BUTTON_STATES.GENERATING);
            setShowConfirmModal(false);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$assessmentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createFinalAssessment"])({
                jobId: candidateProfileData?.jobId,
                jobApplicationId: candidateProfileData?.jobApplicationId
            });
            if (response && response.data && response.data.success) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t(response?.data?.message || "final_assessment_created_successfully"));
                // Get the finalAssessmentId from the response data
                const finalAssessmentId = response.data.data?.assessmentId;
                if (finalAssessmentId) {
                    // Redirect to the final assessment page with the finalAssessmentId and default status values
                    // For a newly created assessment, both isShared and isSubmitted will be false
                    router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${finalAssessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`);
                }
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message || "failed_to_create_final_assessment"));
            }
        } catch (error) {
            console.error("Error creating final assessment:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("an_error_occurred_while_creating_the_final_assessment"));
        } finally{
            setIsLoading(false);
            setButtonState(BUTTON_STATES.IDLE);
        }
    };
    /**
   * Close the confirmation modal
   */ const handleCancelConfirmModal = ()=>{
        setShowConfirmModal(false);
    };
    // Effect to check assessment status when component mounts
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidateProfile.useEffect": ()=>{
            // Automatically call getAssessmentStatus when the component mounts
            const checkStatus = {
                "CandidateProfile.useEffect.checkStatus": async ()=>{
                    if (!jobApplicationId) {
                        return;
                    }
                    try {
                        setIsLoading(true);
                        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$assessmentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAssessmentStatus"])(jobApplicationId);
                        if (response?.data) {
                            const assessmentData = response?.data?.data;
                            // Update assessment status state
                            setAssessmentStatus({
                                exists: !!assessmentData.assessmentId,
                                isAssessmentShared: assessmentData.isAssessmentShared || false,
                                isAssessmentSubmitted: assessmentData.isAssessmentSubmitted || false,
                                assessmentId: assessmentData.assessmentId || null
                            });
                        } else {
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message || "failed_to_get_assessment_status"));
                        }
                    } catch (error) {
                        console.error(error);
                    } finally{
                        setIsLoading(false);
                    }
                }
            }["CandidateProfile.useEffect.checkStatus"];
            checkStatus();
        }
    }["CandidateProfile.useEffect"], [
        t
    ]);
    // ============================================================================
    // CHART CONFIGURATION
    // ============================================================================
    // Register Chart.js components
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Chart"].register(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["RadialLinearScale"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["CategoryScale"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LinearScale"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["BarElement"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["PointElement"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LineElement"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Title"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Tooltip"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chart$2e$js$2f$dist$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Legend"]);
    // ============================================================================
    // UTILITY FUNCTIONS
    // ============================================================================
    /**
   * Generates bar chart data for skill visualization
   * @returns BarChartData object configured for skill scores display
   */ const generateBarChartData = ()=>{
        const labels = skillSpecificAssessment?.skillsScores.map((item)=>item.skill_name) || [];
        const values = skillSpecificAssessment?.skillsScores.map((item)=>item.skill_marks * 10) || [];
        return {
            labels,
            datasets: [
                {
                    data: values,
                    backgroundColor: labels.map((label, index)=>{
                        const skillScore = values[index];
                        // Color changes only for perfect scores (100/100)
                        return skillScore === 100 ? "#ffc107" : "rgba(119,167,255,0.8)";
                    }),
                    borderRadius: 8,
                    borderSkipped: false,
                    barPercentage: 0.5
                }
            ]
        };
    };
    /**
   * Generates radar chart data for behavioral assessment visualization
   * @returns RadarChartData object configured for behavioral metrics
   */ const generateRadarChartData = ()=>{
        return {
            labels: [
                "Body Language",
                "Affability",
                "Posture",
                "Eye-Contact",
                "Confidence"
            ],
            datasets: [
                {
                    label: "Behavioral Assessment",
                    data: [
                        28,
                        48,
                        40,
                        19,
                        96
                    ],
                    fill: true,
                    backgroundColor: "rgba(49, 65, 75, 0.2)",
                    borderColor: "rgb(54, 162, 235)",
                    pointBackgroundColor: "rgb(54, 162, 235)",
                    pointBorderColor: "#fff",
                    pointHoverBackgroundColor: "#fff",
                    pointHoverBorderColor: "rgb(54, 162, 235)"
                }
            ]
        };
    };
    /**
   * Calculates the number of filled bars for skill success probability visualization
   * @param probability - Success probability percentage (0-100)
   * @returns Number of bars to fill (0-10)
   */ const calculateFilledBars = (probability)=>{
        return Math.round(probability / 10);
    };
    // ============================================================================
    // COMPUTED VALUES
    // ============================================================================
    /** Chart data for bar chart visualization */ const barChartData = generateBarChartData();
    /** Chart data for radar chart visualization */ const radarChartData = generateRadarChartData();
    // ============================================================================
    // EVENT HANDLERS
    // ============================================================================
    /**
   * Handles switching to Interview History view
   * Loads interview history data if not already loaded
   *
   * @async
   * @function handleInterviewHistoryClick
   * @returns {Promise<void>}
   */ const handleInterviewHistoryClick = async ()=>{
        setSelectedTab(false);
        // Load interview history data if not already loaded
        await loadCandidateInterviewHistory();
    };
    /**
   * Handles switching to Skill Specific Assessment view
   * Only works if final summary has been generated
   *
   * @async
   * @function handleSkillAssessmentClick
   * @returns {Promise<void>}
   */ const handleSkillAssessmentClick = async ()=>{
        if (!hasFinalSummaryBeenGenerated) return;
        setSelectedTab(true);
        // Load assessment data if not already loaded (both final assessment and skill data)
        if (!skillAssessmentLoaded || !finalAssessmentLoaded) {
            await loadFinalAssessment();
        }
    };
    /**
   * Handles hire or reject candidate action (confirmed action)
   * Updates the job application status and refreshes all data
   *
   * @async
   * @function handleHireRejectCandidateConfirmed
   * @param {string} status - The new application status (HIRED or FINAL_REJECT)
   * @returns {Promise<void>}
   */ const handleHireRejectCandidateConfirmed = async (status)=>{
        if (!candidateProfileData) {
            closeConfirmationModal();
            return;
        }
        try {
            // Update modal loading state
            setConfirmationModal((prev)=>({
                    ...prev,
                    loading: true
                }));
            setIsProcessing(true);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateJobApplicationStatus"])(Number(candidateProfileData.jobApplicationId), status);
            if (response && response.data && response.data.success) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t(response.data.message));
                await refreshAllData();
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message));
            }
        } catch (error) {
            console.error("Error updating job application status:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
        } finally{
            setIsProcessing(false);
            closeConfirmationModal();
        }
    };
    /**
   * Handles hire or reject candidate action (original function for backward compatibility)
   * Updates the job application status and refreshes all data
   *
   * @async
   * @function handleHireRejectCandidate
   * @param {string} status - The new application status (HIRED or FINAL_REJECT)
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isProcessing state during operation
   * - Shows success/error toast messages
   * - Refreshes all candidate data on success
   *
   * @example
   * // Hire the candidate
   * handleHireRejectCandidate(APPLICATION_STATUS.HIRED);
   *
   * // Reject the candidate
   * handleHireRejectCandidate(APPLICATION_STATUS.FINAL_REJECT);
   */ // const handleHireRejectCandidate = async (status: string): Promise<void> => {
    //   if (!candidateProfileData) return;
    //   setIsProcessing(true);
    //   try {
    //     const response = await updateJobApplicationStatus(Number(candidateProfileData.jobApplicationId), status);
    //     if (response && response.data && response.data.success) {
    //       toastMessageSuccess(t(response.data.message));
    //       await refreshAllData();
    //     } else {
    //       toastMessageError(t(response?.data?.message));
    //     }
    //   } catch (error) {
    //     console.error("Error updating job application status:", error);
    //     toastMessageError(t("something_went_wrong"));
    //   } finally {
    //     setIsProcessing(false);
    //   }
    // };
    // ============================================================================
    // API FUNCTIONS
    // ============================================================================
    /**
   * Loads candidate interview history from the API
   * Prevents multiple simultaneous calls using loading state
   *
   * @async
   * @function loadCandidateInterviewHistory
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isLoadingInterviewHistory state
   * - Updates candidateInterviewHistory state on success
   * - Updates interviewHistoryLoaded flag
   * - Shows error toast on failure
   */ const loadCandidateInterviewHistory = async ()=>{
        if (isLoadingInterviewHistory) return;
        setIsLoadingInterviewHistory(true);
        try {
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCandidateInterviewHistory"])(jobApplicationId);
            if (response?.data?.success) {
                setCandidateInterviewHistory(response.data.data);
                setInterviewHistoryLoaded(true);
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message));
            }
        } catch (error) {
            console.error("Error loading interview history:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
        } finally{
            setIsLoadingInterviewHistory(false);
        }
    };
    /**
   * Loads final assessment and skill score data from the API
   * Prevents multiple simultaneous calls using loading state
   * Now loads both final assessment and skill score data in a single API call
   *
   * @async
   * @function loadFinalAssessment
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isLoadingFinalAssessment state
   * - Updates finalAssessment state on success
   * - Updates skillSpecificAssessment state on success
   * - Updates finalAssessmentLoaded and skillAssessmentLoaded flags
   * - Shows error toast on failure
   */ const loadFinalAssessment = async ()=>{
        if (isLoadingFinalAssessment || finalAssessmentLoaded && skillAssessmentLoaded) return;
        setIsLoadingFinalAssessment(true);
        try {
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getApplicationFinalSummary"])(jobApplicationId);
            if (response?.data?.success) {
                // Extract final assessment data
                if (response.data.data.formattedFinalAssessment) {
                    setFinalAssessment(response.data.data.formattedFinalAssessment);
                    setSkillSpecificAssessment(response.data.data.candidateProfileSkillScoreData);
                    setFinalAssessmentLoaded(true);
                }
                // Extract skill score data
                if (response.data.data.candidateProfileSkillScoreData) {
                    setSkillSpecificAssessment(response.data.data.candidateProfileSkillScoreData);
                    setSkillAssessmentLoaded(true);
                }
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message));
            }
        } catch (error) {
            console.error("Error loading final assessment:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
        } finally{
            setIsLoadingFinalAssessment(false);
        }
    };
    /**
   * Loads candidate profile data from the API
   *
   * @async
   * @function loadCandidateProfile
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates candidateProfileData state on success
   * - Shows error toast on failure
   */ const loadCandidateProfile = async ()=>{
        setCandidateProfileLoaded(true);
        try {
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchCandidateProfile"])(jobApplicationId);
            if (response?.data?.success) {
                setCandidateProfileData(response.data.data);
                setCandidateProfileLoaded(false);
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message));
                setCandidateProfileLoaded(false);
            }
        } catch (error) {
            console.error("Error loading candidate profile:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
            setCandidateProfileLoaded(false);
        }
    };
    /**
   * Refreshes all candidate data by resetting loaded flags and reloading data
   * Used after status updates to ensure data consistency
   *
   * @async
   * @function refreshAllData
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Resets all data loaded flags
   * - Reloads candidate profile
   * - Reloads tab-specific data based on current tab
   */ const refreshAllData = async ()=>{
        // Reset loaded flags to force refresh
        setSkillAssessmentLoaded(false);
        setFinalAssessmentLoaded(false);
        setInterviewHistoryLoaded(false);
        // Refresh candidate profile
        await loadCandidateProfile();
        // Refresh assessment data based on current tab
        if (selectedTab) {
            await Promise.all([
                loadSkillSpecificAssessment(),
                loadFinalAssessment()
            ]);
        } else {
            await loadCandidateInterviewHistory();
        }
    };
    /**
   * Handles generating final summary for the candidate (confirmed action)
   * Triggers API call to generate comprehensive final summary based on interview data
   *
   * @async
   * @function handleGenerateFinalSummaryConfirmed
   * @returns {Promise<void>}
   */ const handleGenerateFinalSummaryConfirmed = async ()=>{
        if (!candidateProfileData?.jobApplicationId || isGeneratingFinalSummary) {
            closeConfirmationModal();
            return;
        }
        try {
            // Update modal loading state
            setConfirmationModal((prev)=>({
                    ...prev,
                    loading: true
                }));
            setIsGeneratingFinalSummary(true);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateFinalSummary"])(candidateProfileData.jobApplicationId);
            if (response?.data?.success) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t(response?.data?.message || "final_summary_generated_successfully"));
                // Set flag to indicate final summary has been generated by user
                setHasFinalSummaryBeenGenerated(true);
                // Switch to Skill Specific Assessment tab automatically
                setSelectedTab(true);
                // Load skill-specific assessment data
                await Promise.all([
                    loadFinalAssessment(),
                    loadSkillSpecificAssessment()
                ]);
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message || "failed_to_generate_final_summary"));
            }
        } catch (error) {
            console.error("Error generating final summary:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
        } finally{
            setIsGeneratingFinalSummary(false);
            closeConfirmationModal();
        }
    };
    /**
   * Handles generating final summary for the candidate (original function for backward compatibility)
   * Triggers API call to generate comprehensive final summary based on interview data
   *
   * @async
   * @function handleGenerateFinalSummary
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isGeneratingFinalSummary state
   * - Shows success/error toast messages
   * - Refreshes final assessment data on success
   */ // const handleGenerateFinalSummary = async (): Promise<void> => {
    //   if (!candidateProfileData?.jobApplicationId || isGeneratingFinalSummary) return;
    //   setIsGeneratingFinalSummary(true);
    //   try {
    //     const response = await generateFinalSummary(candidateId, candidateProfileData.jobApplicationId);
    //     if (response?.data?.success) {
    //       toastMessageSuccess(t(response?.data?.message || "final_summary_generated_successfully"));
    //       // Set flag to indicate final summary has been generated by user
    //       setHasFinalSummaryBeenGenerated(true);
    //       // Switch to Skill Specific Assessment tab automatically
    //       setSelectedTab(true);
    //       // Load skill-specific assessment data
    //       await Promise.all([loadFinalAssessment(), loadSkillSpecificAssessment()]);
    //     } else {
    //       toastMessageError(t(response?.data?.message || "failed_to_generate_final_summary"));
    //     }
    //   } catch (error) {
    //     console.error("Error generating final summary:", error);
    //     toastMessageError(t("something_went_wrong"));
    //   } finally {
    //     setIsGeneratingFinalSummary(false);
    //   }
    // };
    // ============================================================================
    // COMPUTED VALUES AND MEMOIZED DATA
    // ============================================================================
    /**
   * Memoized AI interviewer analysis for the current user
   * Finds analysis data where the interviewer ID matches the current user
   */ const aiInterviewerAnalysis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CandidateProfile.useMemo[aiInterviewerAnalysis]": ()=>candidateInterviewHistory?.find({
                "CandidateProfile.useMemo[aiInterviewerAnalysis]": (record)=>record.interviewerId === authData?.id && record.interviewerPerformanceAiAnalysis
            }["CandidateProfile.useMemo[aiInterviewerAnalysis]"])?.interviewerPerformanceAiAnalysis
    }["CandidateProfile.useMemo[aiInterviewerAnalysis]"], [
        candidateInterviewHistory,
        authData?.id
    ]);
    /**
   * Memoized all interviewer analysis data for admin users
   * Returns all interviewer performance AI analysis data for admin users
   */ const allInterviewerAnalysis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CandidateProfile.useMemo[allInterviewerAnalysis]": ()=>{
            if (!candidateInterviewHistory || !authData || authData.account_type !== "admin") return [];
            return candidateInterviewHistory.filter({
                "CandidateProfile.useMemo[allInterviewerAnalysis]": (record)=>record.interviewerPerformanceAiAnalysis
            }["CandidateProfile.useMemo[allInterviewerAnalysis]"]).map({
                "CandidateProfile.useMemo[allInterviewerAnalysis]": (record)=>({
                        interviewerId: record.interviewerId,
                        interviewerName: record.interviewerName,
                        interviewerImage: record.interviewerImage,
                        roundNumber: record.roundNumber,
                        analysis: record.interviewerPerformanceAiAnalysis
                    })
            }["CandidateProfile.useMemo[allInterviewerAnalysis]"]);
        }
    }["CandidateProfile.useMemo[allInterviewerAnalysis]"], [
        candidateInterviewHistory,
        authData
    ]);
    // ============================================================================
    // EFFECTS
    // ============================================================================
    /**
   * Initial data loading effect
   * Loads candidate profile and interview history data in parallel when component mounts or candidateId changes
   */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidateProfile.useEffect": ()=>{
            const initializeData = {
                "CandidateProfile.useEffect.initializeData": async ()=>{
                    // Set loading states immediately to prevent "No data" messages
                    setCandidateProfileLoaded(true);
                    setIsLoadingInterviewHistory(true);
                    // Load candidate profile and interview history data in parallel
                    await Promise.all([
                        loadCandidateProfile(),
                        loadCandidateInterviewHistory()
                    ]);
                }
            }["CandidateProfile.useEffect.initializeData"];
            initializeData();
        }
    }["CandidateProfile.useEffect"], [
        jobApplicationId
    ]);
    /**
   * Effect to set the first skill as active when skill assessment data is loaded
   * Ensures there's always an active skill tab when data is available
   */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidateProfile.useEffect": ()=>{
            if (skillSpecificAssessment?.skillsScores && skillSpecificAssessment.skillsScores.length > 0 && !activeSkillTab) {
                setActiveSkillTab(skillSpecificAssessment.skillsScores[0].skill_name);
            }
        }
    }["CandidateProfile.useEffect"], [
        skillSpecificAssessment,
        activeSkillTab
    ]);
    /**
   * Effect to animate the circular progress bar
   * Creates a smooth animation from 0 to the final success probability value
   */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidateProfile.useEffect": ()=>{
            const timer = requestAnimationFrame({
                "CandidateProfile.useEffect.timer": ()=>setAnimationValue(finalAssessment?.overallSuccessProbability || 0)
            }["CandidateProfile.useEffect.timer"]);
            return ({
                "CandidateProfile.useEffect": ()=>cancelAnimationFrame(timer)
            })["CandidateProfile.useEffect"];
        }
    }["CandidateProfile.useEffect"], [
        finalAssessment?.overallSuccessProbability
    ]);
    // ============================================================================
    // RENDER LOGIC
    // ============================================================================
    /**
   * Renders the skill details section for the selected skill
   * Shows strengths, potential gaps, and success probability
   */ const renderSkillDetails = ()=>{
        if (!skillSpecificAssessment?.skillsScores || !activeSkillTab) return null;
        const selectedSkill = skillSpecificAssessment.skillsScores.find((skill)=>skill.skill_name === activeSkillTab);
        if (!selectedSkill) return null;
        const probability = selectedSkill.probability_of_success_in_this_skill?.probabilityOfSuccessInSkill || 0;
        const filledBars = calculateFilledBars(probability);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "row",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "col-md-7",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "skill-sub-title",
                            children: "Strengths"
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                            lineNumber: 926,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            className: "strengths",
                            children: selectedSkill.strengths?.strengths?.map((strength, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    className: "strength-item",
                                    children: strength
                                }, index, false, {
                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                    lineNumber: 929,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                            lineNumber: 927,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "skill-sub-title",
                            children: "Potential Gaps"
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                            lineNumber: 934,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            className: "strengths",
                            children: selectedSkill.potentials_gaps?.potentialGaps?.map((gap, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    className: "strength-item",
                                    children: gap
                                }, index, false, {
                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                    lineNumber: 937,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                            lineNumber: 935,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                    lineNumber: 925,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "col-md-5",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "probability-card",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "skill-sub-title",
                                children: "Skilssssl Success Probability"
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                lineNumber: 945,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "progress-container",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "ms-2 fw-bold",
                                        children: [
                                            probability,
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                        lineNumber: 947,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "probability-bar",
                                        children: Array.from({
                                            length: 10
                                        }, (_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `bar ${index < filledBars ? "filled" : ""}`
                                            }, index, false, {
                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                lineNumber: 950,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                        lineNumber: 948,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                lineNumber: 946,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                        lineNumber: 944,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                    lineNumber: 943,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
            lineNumber: 924,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$QuestionGeneratorLoader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                show: buttonState === BUTTON_STATES.GENERATING
            }, void 0, false, {
                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                lineNumber: 962,
                columnNumber: 7
            }, this),
            showConfirmModal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$FinalAssessmentConfirmModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                onClickCancel: handleCancelConfirmModal,
                onClickGenerate: handleCreateFinalAssessment,
                disabled: isLoading
            }, void 0, false, {
                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                lineNumber: 964,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$ConfirmationModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: confirmationModal.isOpen,
                onClose: closeConfirmationModal,
                onConfirm: confirmationModal.onConfirm,
                title: confirmationModal.title,
                message: confirmationModal.message,
                confirmButtonText: confirmationModal.confirmButtonText,
                loading: confirmationModal.loading,
                loadingText: t("processing")
            }, void 0, false, {
                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                lineNumber: 968,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$conductInterview$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].conduct_interview_page,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "container",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "common-page-header",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "common-page-head-section",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "main-heading",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "clear-btn p-0 m-0",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        onClick: ()=>{
                                                            router.back();
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 986,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 985,
                                                    columnNumber: 19
                                                }, this),
                                                " ",
                                                t("candidate"),
                                                " ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: [
                                                        t("profile"),
                                                        " "
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 992,
                                                    columnNumber: 36
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 984,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            className: "theme-btn clear-btn text-btn primary p-0 m-0",
                                            onClick: ()=>{
                                                if (!isLoading) {
                                                    if (!assessmentStatus?.exists) {
                                                        showCreateFinalAssessmentConfirmation();
                                                    } else if (assessmentStatus?.assessmentId) {
                                                        // Redirect to the final assessment page with the existing assessment ID
                                                        router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${assessmentStatus.assessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`);
                                                    }
                                                }
                                            },
                                            disabled: isLoading,
                                            children: [
                                                buttonState === "loading" || isLoading && buttonState !== "generating" ? "" : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$FinalAssessmentIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1011,
                                                    columnNumber: 100
                                                }, this),
                                                buttonState === "loading" || isLoading && buttonState !== "generating" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    width: 100,
                                                    height: 30,
                                                    borderRadius: 12
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1013,
                                                    columnNumber: 21
                                                }, this) : buttonState === "generating" ? t("generating_questions") : !assessmentStatus?.exists ? t("create_final_assessment") : assessmentStatus.isAssessmentSubmitted ? t("view_final_assessment_result") : t("view_final_assessment")
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 995,
                                            columnNumber: 17
                                        }, this),
                                        candidateProfileData && candidateProfileData.resumeLink && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    onClick: ()=>setResumeModal({
                                                            isOpen: true,
                                                            resumeLink: candidateProfileData?.resumeLink
                                                        }),
                                                    className: "theme-btn clear-btn text-btn primary p-0 m-0",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$PreviewResumeIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            className: "me-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1032,
                                                            columnNumber: 23
                                                        }, this),
                                                        t("preview_candidate_resume")
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1028,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$ResumeModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    isOpen: resumeModal.isOpen,
                                                    onClose: ()=>setResumeModal({
                                                            ...resumeModal,
                                                            isOpen: false
                                                        }),
                                                    resumeLink: resumeModal.resumeLink
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1035,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                    lineNumber: 983,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                lineNumber: 982,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                            lineNumber: 981,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "inner-section profile-section",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "candidate-profile",
                                    children: [
                                        candidateProfileLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            height: 100,
                                            width: 100,
                                            borderRadius: 12
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1048,
                                            columnNumber: 17
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$avatar$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
                                            src: candidateProfileData?.imageUrl || undefined,
                                            name: candidateProfileData?.candidateName || "",
                                            size: "100",
                                            round: true,
                                            className: "candidate-image"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1050,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "candidate-info",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "candidate-name",
                                                    children: candidateProfileLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        width: 150,
                                                        height: 25
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1061,
                                                        columnNumber: 45
                                                    }, this) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toTitleCase"])(candidateProfileData?.candidateName || "-")
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1060,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "info-container",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "info-item",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "info-title",
                                                                    children: t("post_applied_for")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1066,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "info-value",
                                                                    children: candidateProfileLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        width: 100,
                                                                        height: 25
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1069,
                                                                        columnNumber: 49
                                                                    }, this) : candidateProfileData?.jobTitle || "-"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1068,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1065,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "info-item",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "info-title",
                                                                    children: t("department")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1073,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "info-value",
                                                                    children: candidateProfileLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        width: 100,
                                                                        height: 25
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1075,
                                                                        columnNumber: 49
                                                                    }, this) : candidateProfileData?.department || "-"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1074,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1072,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "info-item",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "info-title",
                                                                    children: t("current_round")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1079,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "info-value",
                                                                    children: candidateProfileLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        width: 20,
                                                                        height: 18
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1081,
                                                                        columnNumber: 49
                                                                    }, this) : candidateProfileData?.roundNumber || 0
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1080,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1078,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "info-item",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "info-title",
                                                                    children: t("resume_approved_by")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1085,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "info-value with-img",
                                                                    children: [
                                                                        candidateProfileLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            width: 20,
                                                                            height: 20
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1088,
                                                                            columnNumber: 25
                                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            src: candidateProfileData?.interviewerImage || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                            alt: "Interviewer avatar",
                                                                            height: 20,
                                                                            width: 20
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1090,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        candidateProfileLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            width: 100,
                                                                            height: 25
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1093,
                                                                            columnNumber: 49
                                                                        }, this) : candidateProfileData?.interviewerName || "-"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1086,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1084,
                                                            columnNumber: 19
                                                        }, this),
                                                        candidateProfileLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 40,
                                                            width: 100,
                                                            borderRadius: 12
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1097,
                                                            columnNumber: 21
                                                        }, this) : candidateProfileData ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "button-align",
                                                            children: hasFinalSummaryBeenGenerated && !isGeneratingFinalSummary && assessmentStatus?.exists ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        className: "primary-btn rounded-md minWidth",
                                                                        onClick: showHireConfirmation,
                                                                        disabled: isProcessing || !candidateProfileData,
                                                                        children: isProcessing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1108,
                                                                            columnNumber: 45
                                                                        }, this) : t("hire")
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1103,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        className: "dark-outline-btn rounded-md minWidth",
                                                                        onClick: showRejectConfirmation,
                                                                        disabled: isProcessing || !candidateProfileData,
                                                                        children: isProcessing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1115,
                                                                            columnNumber: 45
                                                                        }, this) : t("reject")
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1110,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true) : /* Show generate final summary button when user hasn't generated final summary yet */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                className: "primary-btn rounded-md minWidth",
                                                                onClick: showGenerateFinalSummaryConfirmation,
                                                                disabled: isGeneratingFinalSummary || !candidateProfileData?.jobApplicationId,
                                                                children: isGeneratingFinalSummary ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1125,
                                                                    columnNumber: 55
                                                                }, this) : t("generate_final_summary")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1120,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1099,
                                                            columnNumber: 21
                                                        }, this) : ""
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1064,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1058,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                    lineNumber: 1046,
                                    columnNumber: 13
                                }, this),
                                !hasFinalSummaryBeenGenerated && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4",
                                    onClick: handleInterviewHistoryClick,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        children: t("interview_history")
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                        lineNumber: 1138,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                    lineNumber: 1137,
                                    columnNumber: 15
                                }, this),
                                hasFinalSummaryBeenGenerated && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "common-tab mb-5",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            className: selectedTab ? "active" : "",
                                            onClick: handleSkillAssessmentClick,
                                            children: t("skill_specific_assessment")
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1145,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            className: !selectedTab ? "active" : "",
                                            onClick: handleInterviewHistoryClick,
                                            children: t("interview_history")
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1148,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                    lineNumber: 1144,
                                    columnNumber: 15
                                }, this),
                                selectedTab && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "assessment-content",
                                    children: [
                                        !finalAssessmentLoaded ? // Loading state - show skeleton loaders
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "row g-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-md-4",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "improvement-areas-card",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 200,
                                                            width: "100%",
                                                            borderRadius: 20
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1160,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1159,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1158,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-md-8",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "improvement-areas-card",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 200,
                                                            width: "100%",
                                                            borderRadius: 20
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1165,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1164,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1163,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1157,
                                            columnNumber: 19
                                        }, this) : finalAssessmentLoaded && finalAssessment ? // Data available - show actual content
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "row g-4",
                                            children: [
                                                finalAssessment && finalAssessment.overallSuccessProbability && finalAssessment.overallSuccessProbability && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-md-4",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "circular-progress-card",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$circular$2d$progressbar$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CircularProgressbar"], {
                                                            className: "circular-progress-bar",
                                                            value: animationValue,
                                                            text: `${animationValue}%`,
                                                            circleRatio: 0.5,
                                                            strokeWidth: 16,
                                                            styles: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$circular$2d$progressbar$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildStyles"])({
                                                                rotation: 0.749,
                                                                strokeLinecap: "butt",
                                                                trailColor: "#eee",
                                                                textColor: "#333",
                                                                textSize: "1.6rem",
                                                                pathColor: "#9ebff7",
                                                                pathTransitionDuration: 0.5
                                                            })
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1175,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1174,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1173,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-md-8",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "summary-text-card row",
                                                        children: [
                                                            finalAssessment && finalAssessment.skillSummary && finalAssessment.skillSummary && finalAssessment.skillSummary.finalSummary.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "col-md-9",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                        className: "sub-tittle mt-0",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$AiMarkIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                className: "me-2"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                lineNumber: 1202,
                                                                                columnNumber: 33
                                                                            }, this),
                                                                            " ",
                                                                            t("ai_summary")
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1201,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                        className: "check-list",
                                                                        children: finalAssessment?.skillSummary?.finalSummary?.map((item, index)=>{
                                                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$CheckSecondaryIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                        className: "me-2"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                        lineNumber: 1208,
                                                                                        columnNumber: 39
                                                                                    }, this),
                                                                                    " ",
                                                                                    item
                                                                                ]
                                                                            }, index, true, {
                                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                lineNumber: 1207,
                                                                                columnNumber: 37
                                                                            }, this);
                                                                        })
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1204,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1200,
                                                                columnNumber: 29
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "col-md-3",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$AIVerifiedIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1216,
                                                                    columnNumber: 27
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1215,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1195,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1194,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1171,
                                            columnNumber: 19
                                        }, this) : // No data available - show message
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "no-final-assessment",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: t("no_final_assessment_data_found")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                lineNumber: 1224,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1223,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "row g-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-12 col-md-6 d-flex",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "skills-score-card skills-graph-card flex-grow-1",
                                                        children: !skillAssessmentLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 300,
                                                            width: "100%",
                                                            borderRadius: 20
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1231,
                                                            columnNumber: 25
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$chartjs$2d$2$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Radar"], {
                                                                data: radarChartData,
                                                                options: {
                                                                    maintainAspectRatio: false
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1237,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1229,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1228,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-12 col-md-6 d-flex",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "skills-score-card skills-graph-card flex-grow-1",
                                                        children: !skillAssessmentLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 300,
                                                            width: "100%",
                                                            borderRadius: 20
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1246,
                                                            columnNumber: 25
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$chartjs$2d$2$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Bar"], {
                                                            data: barChartData,
                                                            options: {
                                                                maintainAspectRatio: false,
                                                                plugins: {
                                                                    legend: {
                                                                        display: false
                                                                    },
                                                                    title: {
                                                                        display: false
                                                                    }
                                                                },
                                                                scales: {
                                                                    x: {
                                                                        grid: {
                                                                            display: false
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1248,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1244,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1243,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1227,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "row g-4",
                                            children: [
                                                !skillAssessmentLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-md-4",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "improvement-areas-card",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 200,
                                                            width: "100%",
                                                            borderRadius: 20
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1265,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1264,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1263,
                                                    columnNumber: 21
                                                }, this) : skillSpecificAssessment && skillSpecificAssessment.skillsScores && skillSpecificAssessment.skillsScores.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-lg-4",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "summary-text-card skills-score-card",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "sub-tittle mt-0",
                                                                children: t("skills_score")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1271,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                className: "skills-list",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                        className: "skills-item",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "skill-name",
                                                                                children: t("career_based_skills")
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                lineNumber: 1274,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "skill-rating",
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$StarIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                        lineNumber: 1276,
                                                                                        columnNumber: 31
                                                                                    }, this),
                                                                                    " ",
                                                                                    skillSpecificAssessment.careerBasedSkillsScore,
                                                                                    "/10"
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                lineNumber: 1275,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1273,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    skillSpecificAssessment && skillSpecificAssessment.skillsScores && skillSpecificAssessment.skillsScores.length > 0 && skillSpecificAssessment.skillsScores.map((item, index)=>{
                                                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                            className: "skills-item",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "skill-name",
                                                                                    children: item.skill_name
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                    lineNumber: 1285,
                                                                                    columnNumber: 35
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "skill-rating",
                                                                                    children: item.skill_marks === 10 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                        className: "skill-badge",
                                                                                        children: t("extreme")
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                        lineNumber: 1288,
                                                                                        columnNumber: 39
                                                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$StarIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                                lineNumber: 1291,
                                                                                                columnNumber: 41
                                                                                            }, this),
                                                                                            " ",
                                                                                            item.skill_marks,
                                                                                            "/10"
                                                                                        ]
                                                                                    }, void 0, true)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                    lineNumber: 1286,
                                                                                    columnNumber: 35
                                                                                }, this)
                                                                            ]
                                                                        }, index, true, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1284,
                                                                            columnNumber: 33
                                                                        }, this);
                                                                    })
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1272,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1270,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1269,
                                                    columnNumber: 21
                                                }, this) : "",
                                                !skillAssessmentLoaded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-lg-8",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "improvement-areas-card",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 200,
                                                            width: "100%",
                                                            borderRadius: 20
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1307,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1306,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1305,
                                                    columnNumber: 21
                                                }, this) : skillSpecificAssessment && skillSpecificAssessment.skillsScores && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-lg-8",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "summary-text-card skills-summary-card",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "sub-tittle mt-0",
                                                                children: t("skills_summary")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1315,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "skills-tags",
                                                                children: skillSpecificAssessment && skillSpecificAssessment.skillsScores && skillSpecificAssessment.skillsScores.map((skill, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: `skill-tag ${activeSkillTab === skill.skill_name ? "active" : ""}`,
                                                                        onClick: ()=>setActiveSkillTab(skill.skill_name),
                                                                        children: skill.skill_name
                                                                    }, index, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1320,
                                                                        columnNumber: 33
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1316,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "strengths-gaps",
                                                                children: renderSkillDetails()
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1330,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1314,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1313,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-12",
                                                    children: !finalAssessmentLoaded ? // Loading state - show skeleton loaders
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "improvement-areas-card",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                height: 18,
                                                                width: "20%",
                                                                borderRadius: 6
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1339,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "row g-3 mt-3",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "col-md-4",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            height: 177,
                                                                            width: "100%",
                                                                            borderRadius: 24
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1342,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1341,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "col-md-4",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            height: 177,
                                                                            width: "100%",
                                                                            borderRadius: 24
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1345,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1344,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "col-md-4",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            height: 177,
                                                                            width: "100%",
                                                                            borderRadius: 24
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1348,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1347,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1340,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1338,
                                                        columnNumber: 23
                                                    }, this) : finalAssessmentLoaded && finalAssessment && finalAssessment.developmentRecommendations && finalAssessment.developmentRecommendations.recommendations && finalAssessment.developmentRecommendations.recommendations.length > 0 ? // Data available - show actual content
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "summary-text-card improvement-areas-card",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "sub-tittle mt-0",
                                                                children: t("improvement_areas")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1359,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "row g-4",
                                                                children: finalAssessment.developmentRecommendations.recommendations.map((recommendation, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "col-md-4",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "improvement-card h-100",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                                    className: "title",
                                                                                    children: recommendation.title
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                    lineNumber: 1364,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                    className: "description",
                                                                                    children: recommendation.description
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                    lineNumber: 1365,
                                                                                    columnNumber: 33
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1363,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    }, index, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1362,
                                                                        columnNumber: 29
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1360,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1358,
                                                        columnNumber: 23
                                                    }, this) : // No data available - show message (this covers both no finalAssessment and no developmentRecommendations)
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "no-improvement-areas",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: t("no_improvement_areas_data_found")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1374,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1373,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1335,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1261,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                    lineNumber: 1154,
                                    columnNumber: 15
                                }, this),
                                !selectedTab && (isLoadingInterviewHistory ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "history-content",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "interview-summary",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "summary-header",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        height: 21,
                                                        width: "20%",
                                                        borderRadius: 6
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1388,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1387,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "interviewer",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 21,
                                                            width: "20%",
                                                            className: "mb-4",
                                                            borderRadius: 6
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1391,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "interviewer-info",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    height: 45,
                                                                    width: 45,
                                                                    borderRadius: 100
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1394,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    height: 18,
                                                                    width: 100,
                                                                    borderRadius: 6
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1395,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1393,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1390,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "summary-scores",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 18,
                                                            width: 100,
                                                            className: "mb-4",
                                                            borderRadius: 6
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1399,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "score-btns mt-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    height: 45,
                                                                    width: 170,
                                                                    className: "mb-4",
                                                                    borderRadius: 12
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1401,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    height: 45,
                                                                    width: 170,
                                                                    className: "mb-4",
                                                                    borderRadius: 12
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1402,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    height: 45,
                                                                    width: 170,
                                                                    className: "mb-4",
                                                                    borderRadius: 12
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1403,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    height: 45,
                                                                    width: 170,
                                                                    className: "mb-4",
                                                                    borderRadius: 12
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1404,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    height: 45,
                                                                    width: 170,
                                                                    className: "mb-4",
                                                                    borderRadius: 12
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1405,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1400,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1398,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "summary-highlights",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 18,
                                                            width: 100,
                                                            className: "mb-4",
                                                            borderRadius: 6
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1409,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                            className: "highlight-list p-0",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                height: 16,
                                                                width: "80%",
                                                                className: "mb-3",
                                                                borderRadius: 6,
                                                                count: 4
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1411,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1410,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1408,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1386,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "interview-summary",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "summary-header",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        height: 21,
                                                        width: "20%",
                                                        borderRadius: 6
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1417,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1416,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "interviewer",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "interviewer-info large",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                height: 45,
                                                                width: 45,
                                                                borderRadius: 100
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1421,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                height: 18,
                                                                width: 100,
                                                                borderRadius: 6
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1422,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1420,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1419,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "summary-highlights",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 18,
                                                            width: 100,
                                                            borderRadius: 6,
                                                            className: "mb-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1426,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                            className: "highlight-list p-0",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                height: 16,
                                                                width: "80%",
                                                                className: "mb-3",
                                                                borderRadius: 6,
                                                                count: 4
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1428,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1427,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1425,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1415,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                    lineNumber: 1385,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "history-content",
                                    children: candidateInterviewHistory && candidateInterviewHistory.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            candidateInterviewHistory.map((historyItem, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "interview-summary",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "summary-header",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                                className: "summary-heading",
                                                                children: [
                                                                    t("round"),
                                                                    " ",
                                                                    historyItem.roundNumber,
                                                                    " ",
                                                                    t("summary")
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1440,
                                                                columnNumber: 29
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1439,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "interviewer",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                    className: "summary-title",
                                                                    children: t("interview_by")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1445,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "interviewer-info",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            src: historyItem.interviewerImage || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                            alt: t("interviewer_avatar"),
                                                                            className: "interviewer-avatar",
                                                                            width: 50,
                                                                            height: 50
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1447,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "interviewer-name",
                                                                            children: historyItem.interviewerName
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1454,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        historyItem.endTime ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "text-muted small",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                children: [
                                                                                    " ",
                                                                                    new Date(historyItem.endTime).toLocaleString(undefined, {
                                                                                        year: "numeric",
                                                                                        month: "short",
                                                                                        day: "numeric",
                                                                                        hour: "2-digit",
                                                                                        minute: "2-digit"
                                                                                    })
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                lineNumber: 1457,
                                                                                columnNumber: 35
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1456,
                                                                            columnNumber: 33
                                                                        }, this) : null
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1446,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1444,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "summary-scores",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                    className: "summary-title",
                                                                    children: t("scores")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1472,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "score-btns",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            className: "secondary-btn rounded-md px-3 py-3",
                                                                            children: [
                                                                                t("hard_skills"),
                                                                                " : ",
                                                                                historyItem.hardSkillMarks
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1474,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        Object.entries(historyItem.skillScores).map(([skillName, score])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                className: "secondary-btn rounded-md px-3 py-3",
                                                                                children: [
                                                                                    skillName,
                                                                                    " : ",
                                                                                    score === 10 ? t("extreme") : score
                                                                                ]
                                                                            }, skillName, true, {
                                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                lineNumber: 1478,
                                                                                columnNumber: 33
                                                                            }, this))
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1473,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1471,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "summary-highlights",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                    className: "summary-title",
                                                                    children: t("highlights")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1485,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                    className: "highlight-list",
                                                                    children: historyItem.interviewSummary?.highlight.map((item, index)=>{
                                                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                            className: "highlight-item",
                                                                            children: item
                                                                        }, index, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1489,
                                                                            columnNumber: 35
                                                                        }, this);
                                                                    })
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1486,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1484,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, index, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                    lineNumber: 1438,
                                                    columnNumber: 25
                                                }, this)),
                                            aiInterviewerAnalysis && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "interview-summary",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "summary-header",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                            className: "summary-heading",
                                                            children: t("your_performance_feedback")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1502,
                                                            columnNumber: 29
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1501,
                                                        columnNumber: 27
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "interviewer",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "interviewer-info large",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    src: authData?.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                    alt: t("interviewer_avatar"),
                                                                    className: "interviewer-avatar",
                                                                    width: 50,
                                                                    height: 50
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1506,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "interviewer-name",
                                                                    children: [
                                                                        authData?.first_name,
                                                                        " ",
                                                                        authData?.last_name
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1513,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1505,
                                                            columnNumber: 29
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1504,
                                                        columnNumber: 27
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "summary-highlights",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                className: "summary-title",
                                                                children: t("highlights")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1519,
                                                                columnNumber: 29
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                className: "highlight-list",
                                                                children: aiInterviewerAnalysis.highlights?.map((item, index)=>{
                                                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                        className: "highlight-item",
                                                                        children: item
                                                                    }, index, false, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1523,
                                                                        columnNumber: 35
                                                                    }, this);
                                                                })
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                lineNumber: 1520,
                                                                columnNumber: 29
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1518,
                                                        columnNumber: 27
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                lineNumber: 1500,
                                                columnNumber: 25
                                            }, this),
                                            authData?.account_type === "admin" && allInterviewerAnalysis.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "admin-interviewer-analysis",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "summary-header",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                            className: "summary-heading",
                                                            children: t("all_interviewer_performance_feedback")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1537,
                                                            columnNumber: 29
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                        lineNumber: 1536,
                                                        columnNumber: 27
                                                    }, this),
                                                    allInterviewerAnalysis.map((analysisData)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "interview-summary",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "interviewer",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "interviewer-info large",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                src: analysisData.interviewerImage || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$noImageFound$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                                alt: t("interviewer_avatar"),
                                                                                className: "interviewer-avatar",
                                                                                width: 50,
                                                                                height: 50
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                lineNumber: 1543,
                                                                                columnNumber: 35
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "interviewer-name",
                                                                                children: [
                                                                                    analysisData.interviewerName,
                                                                                    " - Round ",
                                                                                    analysisData.roundNumber
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                lineNumber: 1550,
                                                                                columnNumber: 35
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                        lineNumber: 1542,
                                                                        columnNumber: 33
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1541,
                                                                    columnNumber: 31
                                                                }, this),
                                                                analysisData.analysis?.highlights && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "summary-highlights",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                            className: "summary-title",
                                                                            children: t("highlights")
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1557,
                                                                            columnNumber: 35
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                            className: "highlight-list",
                                                                            children: analysisData.analysis.highlights.map((item, highlightIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    className: "highlight-item",
                                                                                    children: item
                                                                                }, highlightIndex, false, {
                                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                                    lineNumber: 1560,
                                                                                    columnNumber: 39
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                            lineNumber: 1558,
                                                                            columnNumber: 35
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                                    lineNumber: 1556,
                                                                    columnNumber: 33
                                                                }, this)
                                                            ]
                                                        }, `${analysisData.interviewerId}-${analysisData.roundNumber}`, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                            lineNumber: 1540,
                                                            columnNumber: 29
                                                        }, this))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                                lineNumber: 1535,
                                                columnNumber: 25
                                            }, this)
                                        ]
                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "no-interview-history",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: t("no_interview_history_found")
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                            lineNumber: 1574,
                                            columnNumber: 23
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                        lineNumber: 1573,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                                    lineNumber: 1434,
                                    columnNumber: 17
                                }, this))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                            lineNumber: 1045,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                    lineNumber: 980,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/views/conductInterview/CandidateProfile.tsx",
                lineNumber: 979,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(CandidateProfile, "kr5dCpdemGdOj3ZCQZsao7fDtkM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"]
    ];
});
_c = CandidateProfile;
const __TURBOPACK__default__export__ = CandidateProfile;
var _c;
__turbopack_context__.k.register(_c, "CandidateProfile");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/candidate-profile/[jobApplicationId]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$conductInterview$2f$CandidateProfile$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/conductInterview/CandidateProfile.tsx [app-client] (ecmascript)");
"use client";
;
;
const page = ({ params })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$conductInterview$2f$CandidateProfile$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            params: params
        }, void 0, false, {
            fileName: "[project]/src/app/candidate-profile/[jobApplicationId]/page.tsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/candidate-profile/[jobApplicationId]/page.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = page;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_8c2aef6c._.js.map