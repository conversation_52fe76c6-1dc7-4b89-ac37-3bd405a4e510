{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/AiMarkIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction AiMarkIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"25\" height=\"25\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <path\n        d=\"M13.294 7.43666L14.097 9.66666C14.989 12.1417 16.938 14.0907 19.413 14.9827L21.643 15.7857C21.844 15.8587 21.844 16.1437 21.643 16.2157L19.413 17.0187C16.938 17.9107 14.989 19.8597 14.097 22.3347L13.294 24.5647C13.221 24.7657 12.936 24.7657 12.864 24.5647L12.061 22.3347C11.169 19.8597 9.22001 17.9107 6.74501 17.0187L4.51501 16.2157C4.31401 16.1427 4.31401 15.8577 4.51501 15.7857L6.74501 14.9827C9.22001 14.0907 11.169 12.1417 12.061 9.66666L12.864 7.43666C12.936 7.23466 13.221 7.23466 13.294 7.43666Z\"\n        fill=\"black\"\n      />\n      <path\n        d=\"M23.3321 2.07725L23.7391 3.20625C24.1911 4.45925 25.1781 5.44625 26.4311 5.89825L27.5601 6.30525C27.6621 6.34225 27.6621 6.48625 27.5601 6.52325L26.4311 6.93025C25.1781 7.38225 24.1911 8.36925 23.7391 9.62225L23.3321 10.7513C23.2951 10.8533 23.1511 10.8533 23.1141 10.7513L22.7071 9.62225C22.2551 8.36925 21.2681 7.38225 20.0151 6.93025L18.8861 6.52325C18.7841 6.48625 18.7841 6.34225 18.8861 6.30525L20.0151 5.89825C21.2681 5.44625 22.2551 4.45925 22.7071 3.20625L23.1141 2.07725C23.1511 1.97425 23.2961 1.97425 23.3321 2.07725Z\"\n        fill=\"black\"\n      />\n      <path\n        d=\"M23.3321 21.2484L23.7391 22.3774C24.1911 23.6304 25.1781 24.6174 26.4311 25.0694L27.5601 25.4764C27.6621 25.5134 27.6621 25.6574 27.5601 25.6944L26.4311 26.1014C25.1781 26.5534 24.1911 27.5404 23.7391 28.7934L23.3321 29.9224C23.2951 30.0244 23.1511 30.0244 23.1141 29.9224L22.7071 28.7934C22.2551 27.5404 21.2681 26.5534 20.0151 26.1014L18.8861 25.6944C18.7841 25.6574 18.7841 25.5134 18.8861 25.4764L20.0151 25.0694C21.2681 24.6174 22.2551 23.6304 22.7071 22.3774L23.1141 21.2484C23.1511 21.1464 23.2961 21.1464 23.3321 21.2484Z\"\n        fill=\"black\"\n      />\n    </svg>\n  );\n}\n\nexport default AiMarkIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;KAjBS;uCAmBM", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/AIVerifiedIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction AIVerifiedIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"150\" height=\"150\" viewBox=\"0 0 196 196\" fill=\"none\">\n      <g clip-path=\"url(#clip0_13085_4945)\">\n        <path\n          d=\"M84.1489 44.693C65.223 49.5916 50.4346 64.1256 45.0945 82.8875L136.833 59.1387C126.378 48.6518 112.34 42.9375 97.907 42.9375C93.328 42.9375 88.709 43.5115 84.1489 44.693Z\"\n          fill=\"#436EB6\"\n        />\n        <path\n          d=\"M111.678 151.027C131.441 145.911 146.439 130.616 151.26 110.852L57.5745 135.105C71.3789 150.047 91.9152 156.142 111.678 151.027Z\"\n          fill=\"#436EB6\"\n        />\n        <path\n          d=\"M189.667 81.8553L193.432 75.8991L187.438 72.1947L190.55 65.8727L184.196 62.8263L186.618 56.2101L179.977 53.8554L181.682 47.0183L174.828 45.3847L175.797 38.4047L168.809 37.5091L169.03 30.466L161.984 30.3168L161.455 23.2905L154.435 23.8918L153.163 16.9623L146.246 18.3057L144.244 11.5506L137.51 13.6215L134.802 7.11666L128.327 9.89184L124.942 3.71286L118.797 7.16081L114.775 1.37708L109.031 5.45786L104.412 0.134554L99.135 4.80401L93.9799 0L89.2284 5.20347L83.5919 0.975519L79.4207 6.65413L73.3678 3.0485L69.8253 9.13918L63.4234 6.19791L60.5473 12.6313L53.868 10.3859L51.692 17.0884L44.8129 15.5663L43.3622 22.4622L36.3591 21.6801L35.6485 28.6895L28.6033 28.6559L28.6411 35.701L21.6317 36.418L22.4201 43.419L15.5263 44.876L17.0548 51.7551L10.3544 53.9353L12.604 60.6125L6.17478 63.4928L9.12236 69.8926L3.03588 73.4414L6.64572 79.4922L0.969212 83.6675L5.20136 89.2999L0 94.0535L4.80821 99.2044L0.142964 104.486L5.47047 109.096L1.3939 114.842L7.18184 118.86L3.73809 125.007L9.92128 128.388L7.1503 134.865L13.6573 137.569L11.5906 144.305L18.3477 146.303L17.0085 153.22L23.9402 154.487L23.3452 161.507L30.3714 162.031L30.5249 169.076L37.568 168.849L38.4699 175.837L45.4478 174.864L47.0877 181.716L53.9226 180.006L56.2815 186.646L62.8957 184.22L65.9463 190.571L72.2662 187.455L75.9748 193.445L81.9268 189.675L86.2514 195.238L91.7702 190.857L96.6626 195.928L101.683 190.985L107.086 195.507L111.554 190.058L117.407 193.981L121.269 188.088L127.507 191.366L130.722 185.096L137.271 187.693L139.8 181.116L146.589 183.002L148.405 176.195L155.356 177.349L156.438 170.386L163.473 170.794L163.809 163.755L170.846 163.412L170.432 156.377L177.393 155.29L176.235 148.34L183.04 146.519L181.148 139.733L187.722 137.197L185.119 130.648L191.387 127.429L188.103 121.196L193.992 117.329L190.064 111.478L195.51 107.006L190.983 101.607L195.922 96.5827L190.846 91.6946L195.224 86.1736L189.658 81.8532L189.667 81.8553ZM119.366 180.73C73.6727 192.56 26.8751 165.008 15.047 119.316C3.21669 73.6201 30.7667 26.8226 76.4605 14.9944C122.154 3.16413 168.952 30.7162 180.782 76.41C192.61 122.104 165.06 168.901 119.366 180.73Z\"\n          fill=\"#436EB6\"\n        />\n        <path d=\"M167.926 110.497L167.228 113.896L165.458 113.532L166.156 110.133L167.926 110.497Z\" fill=\"#436EB6\" />\n        <path d=\"M45.6875 146.125L43.3979 143.516L44.7561 142.324L47.0456 144.931L45.6875 146.123V146.125Z\" fill=\"#436EB6\" />\n        <path d=\"M28.0042 85.4308L28.7022 82.0312L30.4745 82.395L29.7765 85.7946L28.0063 85.4308H28.0042Z\" fill=\"#436EB6\" />\n        <path d=\"M150.24 49.8047L152.532 52.4138L151.174 53.6058L148.882 50.9989L150.24 49.8068V49.8047Z\" fill=\"#436EB6\" />\n        <path\n          d=\"M25.7139 109.445C25.2033 107.54 25.2725 105.96 25.9212 104.705C26.564 103.428 27.7819 102.549 29.5752 102.068C31.3684 101.588 32.8627 101.74 34.058 102.525C35.2473 103.287 36.0973 104.621 36.6078 106.526L36.806 107.266L33.3092 108.203L33.0479 107.228C32.8317 106.421 32.5186 105.88 32.1087 105.606C31.6928 105.309 31.1822 105.241 30.577 105.404C29.9718 105.566 29.5633 105.879 29.3516 106.344C29.1339 106.787 29.1331 107.412 29.3494 108.219C29.6136 109.205 30.1026 109.999 30.8164 110.601C31.5526 111.196 32.5736 111.824 33.8793 112.483C34.9698 113.056 35.8681 113.584 36.5742 114.067C37.2743 114.528 37.9321 115.145 38.5474 115.917C39.1627 116.689 39.6205 117.635 39.9208 118.756C40.4313 120.662 40.354 122.256 39.6888 123.539C39.0177 124.8 37.7855 125.671 35.9922 126.151C34.199 126.632 32.6965 126.494 31.4848 125.738C30.267 124.959 29.4029 123.617 28.8924 121.711L28.505 120.265L32.0018 119.329L32.4523 121.01C32.8607 122.534 33.7037 123.125 34.9814 122.783C36.2591 122.44 36.6937 121.507 36.2853 119.983C36.021 118.996 35.5208 118.205 34.7846 117.61C34.0709 117.008 33.0611 116.378 31.7553 115.719C30.6649 115.146 29.7696 114.629 29.0695 114.168C28.3633 113.685 27.7026 113.057 27.0873 112.285C26.472 111.513 26.0142 110.566 25.7139 109.445ZM52.8461 115.077C53.9392 119.156 52.6029 121.701 48.8371 122.71C47.0438 123.19 45.5414 123.052 44.3296 122.296C43.1119 121.517 42.2478 120.175 41.7372 118.27L41.5751 117.664L45.0719 116.727L45.2971 117.568C45.7115 119.115 46.5576 119.717 47.8353 119.374C48.5302 119.188 48.9887 118.837 49.2108 118.321C49.4329 117.805 49.3998 117.009 49.1115 115.933L47.9583 111.629C47.6806 113.193 46.7236 114.195 45.0873 114.633C43.7424 114.993 42.5994 114.807 41.6582 114.074C40.7171 113.341 40.0123 112.101 39.5438 110.352L38.3906 106.049C37.8861 104.166 37.9776 102.58 38.6652 101.29C39.3528 100.001 40.6044 99.113 42.42 98.6265C44.2357 98.14 45.7636 98.2831 47.0037 99.056C48.2439 99.8288 49.1162 101.157 49.6207 103.04L52.8461 115.077ZM45.6994 110.865C46.9771 110.523 47.4087 109.578 46.9943 108.032L45.8591 103.795C45.4507 102.271 44.6077 101.68 43.33 102.022C42.0523 102.365 41.6177 103.298 42.0261 104.822L43.1613 109.059C43.5757 110.605 44.4217 111.208 45.6994 110.865ZM59.4973 94.3389L65.8038 117.875L62.1053 118.866L55.7987 95.3299L59.4973 94.3389ZM71.8032 116.268L68.4745 117.16L62.168 93.6233L66.808 92.38L74.3823 105.45L70.6074 91.362L73.9025 90.4791L80.209 114.015L76.4096 115.033L67.2355 99.2207L71.8032 116.268ZM86.1522 112.423L82.8235 113.315L76.5169 89.7785L81.1569 88.5352L88.7313 101.605L84.9564 87.5172L88.2514 86.6343L94.558 110.171L90.7585 111.189L81.5844 95.3759L86.1522 112.423ZM102.333 93.4921L103.234 96.8544L98.1568 98.2149L100.004 105.108L106.392 103.396L107.293 106.758L97.206 109.461L90.8995 85.9247L100.986 83.2219L101.887 86.5842L95.499 88.296L97.2558 94.8525L102.333 93.4921ZM117.318 104.072C117.082 103.727 116.896 103.392 116.761 103.068C116.626 102.744 116.441 102.145 116.207 101.27L115.216 97.5719C114.928 96.4959 114.533 95.7848 114.032 95.4386C113.525 95.07 112.845 94.9998 111.993 95.2281L110.716 95.5704L113.283 105.153L109.585 106.144L103.278 82.6078L108.86 81.1123C110.765 80.6017 112.275 80.6777 113.389 81.34C114.498 81.98 115.298 83.219 115.791 85.0571L116.286 86.9064C116.935 89.3272 116.555 91.135 115.145 92.3295C116.115 92.4539 116.905 92.891 117.514 93.6407C118.118 94.3679 118.593 95.3816 118.942 96.6817L119.915 100.313C120.095 100.985 120.265 101.529 120.423 101.943C120.576 102.334 120.797 102.707 121.084 103.063L117.318 104.072ZM109.815 92.2081L111.261 91.8207C112 91.6225 112.498 91.2848 112.755 90.8076C113.034 90.3245 113.053 89.6346 112.813 88.738L112.191 86.418C111.963 85.5662 111.641 84.9918 111.225 84.6948C110.832 84.3918 110.31 84.3274 109.66 84.5016L107.878 84.9791L109.815 92.2081ZM125.147 76.7482L128.543 75.8383L131.218 100.348L125.704 101.825L115.766 79.2618L119.498 78.2618L127.467 96.7039L125.147 76.7482ZM133.967 74.3847L140.274 97.9209L136.575 98.912L130.269 75.3757L133.967 74.3847ZM148.105 81.2275L149.006 84.5898L143.929 85.9502L145.776 92.843L152.164 91.1312L153.065 94.4935L142.978 97.1963L136.672 73.6601L146.759 70.9573L147.66 74.3196L141.271 76.0314L143.028 82.5879L148.105 81.2275ZM162.216 92.0415L157.139 93.4019L148.21 70.5684L151.808 69.6044L158.765 87.6329L155.573 68.5954L159.138 67.6404L165.996 85.8394L162.903 66.6314L166.131 65.7665L169.815 90.0054L164.906 91.3208L160.201 79.1397L162.216 92.0415Z\"\n          fill=\"#436EB6\"\n        />\n        <path\n          d=\"M51.0894 57.3542L49.0756 56.0873L47.4451 57.984L48.9999 59.7849L47.78 61.2039L39.2978 51.1756L41.2424 48.9136L52.4301 55.7947L51.0894 57.3542ZM47.5597 55.1506L41.9459 51.6196L46.2794 56.6399L47.5597 55.1506ZM47.3313 42.4979L56.0634 52.0874L54.5565 53.4596L45.8244 43.8701L47.3313 42.4979ZM60.347 33.0923L61.9587 32.1414L66.8254 44.3287L64.2083 45.8726L55.8947 35.7189L57.6661 34.6739L64.3822 42.995L60.347 33.0923ZM72.9924 33.0346L73.6868 34.7524L71.093 35.8009L72.5165 39.3223L75.7802 38.003L76.4746 39.7207L71.3213 41.8039L66.4607 29.7796L71.6139 27.6965L72.3083 29.4142L69.0446 30.7335L70.3986 34.0831L72.9924 33.0346ZM83.8828 37.5586C83.7629 37.371 83.6699 37.1897 83.6038 37.0149C83.5376 36.8401 83.4498 36.5181 83.3403 36.049L82.8772 34.0642C82.7425 33.4869 82.5448 33.1017 82.2842 32.9089C82.0207 32.704 81.6605 32.6549 81.2034 32.7615L80.5177 32.9215L81.7176 38.0638L79.7329 38.5269L76.7858 25.8967L79.7809 25.1978C80.8034 24.9592 81.6053 25.0258 82.1867 25.3975C82.7653 25.7571 83.1697 26.4302 83.3998 27.4165L83.6314 28.4089C83.9345 29.708 83.7008 30.6631 82.9301 31.2742C83.4441 31.3572 83.8567 31.6033 84.1679 32.0127C84.4762 32.4101 84.7118 32.9576 84.8746 33.6552L85.3293 35.6039C85.4135 35.9648 85.4943 36.2567 85.5717 36.4796C85.6462 36.6905 85.7569 36.893 85.9037 37.0871L83.8828 37.5586ZM80.0967 31.1172L80.8726 30.9362C81.2695 30.8435 81.5404 30.6725 81.6851 30.4231C81.8419 30.1709 81.8641 29.8042 81.7519 29.3231L81.4614 28.0781C81.3547 27.621 81.1933 27.3099 80.9772 27.1447C80.7731 26.9767 80.4967 26.9334 80.1478 27.0148L79.1915 27.2379L80.0967 31.1172ZM89.7425 23.3006L91.1863 36.1895L89.1609 36.4164L87.7171 23.5275L89.7425 23.3006ZM99.6167 28.7035L99.599 30.5562L96.9682 30.5311L96.917 35.8854L94.8791 35.8659L95.003 22.897L100.394 22.9485L100.377 24.8012L97.0233 24.7692L96.9859 28.6784L99.6167 28.7035ZM107.046 23.4013L105.482 36.2761L103.459 36.0303L105.023 23.1554L107.046 23.4013ZM115.633 30.6744L115.192 32.4739L112.474 31.8076L111.57 35.4966L114.989 36.3349L114.548 38.1344L109.149 36.8107L112.238 24.2143L117.636 25.538L117.195 27.3375L113.776 26.4991L112.916 30.0081L115.633 30.6744ZM125.84 28.3285C126.776 28.7169 127.373 29.2592 127.632 29.9553C127.892 30.6515 127.82 31.4844 127.417 32.454L124.902 38.5114C124.499 39.4811 123.96 40.1197 123.284 40.4275C122.608 40.7352 121.802 40.6948 120.867 40.3064L117.889 39.0701L122.863 27.0922L125.84 28.3285ZM120.482 38.1405L121.543 38.5811C121.851 38.709 122.123 38.7148 122.358 38.5986C122.605 38.4871 122.814 38.2261 122.984 37.8154L125.571 31.5869C125.741 31.1762 125.779 30.8441 125.683 30.5905C125.599 30.3417 125.404 30.1533 125.096 30.0254L124.035 29.5849L120.482 38.1405ZM143.684 139.898L145.664 141.218L147.343 139.364L145.836 137.523L147.092 136.136L155.31 146.382L153.307 148.593L142.303 141.422L143.684 139.898ZM147.155 142.193L152.675 145.87L148.474 140.738L147.155 142.193ZM147.032 154.859L138.572 145.029L140.116 143.699L148.577 153.529L147.032 154.859ZM133.755 163.899L132.117 164.804L127.597 152.484L130.257 151.015L138.28 161.399L136.48 162.394L130.002 153.886L133.755 163.899ZM121.096 163.595L120.454 161.857L123.078 160.887L121.761 157.324L118.459 158.545L117.817 156.807L123.03 154.88L127.527 167.045L122.314 168.972L121.671 167.234L124.973 166.014L123.72 162.625L121.096 163.595ZM110.351 158.762C110.466 158.952 110.554 159.136 110.615 159.313C110.677 159.489 110.756 159.813 110.853 160.285L111.263 162.282C111.382 162.862 111.569 163.253 111.825 163.452C112.082 163.664 112.441 163.723 112.901 163.629L113.591 163.487L112.529 158.315L114.525 157.905L117.134 170.609L114.121 171.228C113.092 171.439 112.293 171.351 111.721 170.964C111.153 170.589 110.766 169.905 110.563 168.913L110.358 167.915C110.089 166.608 110.348 165.66 111.135 165.069C110.624 164.973 110.218 164.716 109.918 164.298C109.62 163.893 109.399 163.339 109.255 162.637L108.853 160.677C108.778 160.314 108.705 160.02 108.634 159.795C108.565 159.582 108.46 159.377 108.318 159.179L110.351 158.762ZM113.963 165.302L113.183 165.462C112.784 165.544 112.508 165.708 112.357 165.953C112.194 166.201 112.161 166.567 112.261 167.051L112.518 168.304C112.612 168.763 112.765 169.079 112.977 169.25C113.177 169.423 113.452 169.474 113.803 169.402L114.765 169.204L113.963 165.302ZM104.033 172.848L103.012 159.919L105.044 159.758L106.065 172.687L104.033 172.848ZM94.4045 167.175L94.4663 165.324L97.0958 165.411L97.2743 160.06L99.3112 160.128L98.8788 173.09L93.4902 172.91L93.552 171.058L96.9037 171.17L97.034 167.263L94.4045 167.175ZM86.7796 172.272L88.7238 159.449L90.7388 159.755L88.7947 172.578L86.7796 172.272ZM78.4133 164.752L78.9066 162.966L81.6034 163.711L82.6146 160.05L79.2213 159.113L79.7146 157.327L85.0724 158.807L81.6194 171.308L76.2617 169.828L76.7549 168.043L80.1482 168.98L81.1101 165.497L78.4133 164.752ZM68.3298 166.963C67.3986 166.564 66.807 166.016 66.5552 165.317C66.3034 164.618 66.384 163.786 66.797 162.821L69.3769 156.791C69.7899 155.825 70.3361 155.192 71.0155 154.892C71.6948 154.591 72.5001 154.64 73.4313 155.039L76.3953 156.307L71.2937 168.231L68.3298 166.963ZM73.7927 157.209L72.7366 156.757C72.43 156.626 72.1583 156.617 71.9216 156.731C71.6735 156.839 71.462 157.098 71.2871 157.507L68.6343 163.708C68.4594 164.116 68.4182 164.448 68.5108 164.703C68.5921 164.952 68.786 165.143 69.0926 165.274L70.1488 165.726L73.7927 157.209Z\"\n          fill=\"#436EB6\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_13085_4945\">\n          <rect width=\"195.93\" height=\"195.928\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default AIVerifiedIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAM,QAAO;QAAM,SAAQ;QAAc,MAAK;;0BAC1F,6LAAC;gBAAE,aAAU;;kCACX,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBAAK,GAAE;wBAAoF,MAAK;;;;;;kCACjG,6LAAC;wBAAK,GAAE;wBAA4F,MAAK;;;;;;kCACzG,6LAAC;wBAAK,GAAE;wBAA2F,MAAK;;;;;;kCACxG,6LAAC;wBAAK,GAAE;wBAA0F,MAAK;;;;;;kCACvG,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,6LAAC;0BACC,cAAA,6LAAC;oBAAS,IAAG;8BACX,cAAA,6LAAC;wBAAK,OAAM;wBAAS,QAAO;wBAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;AAKrD;KApCS;uCAsCM", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KATS;uCAWM", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/CheckSecondaryIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction CheckSecondaryIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"19\" height=\"19\" viewBox=\"0 0 24 24\" fill=\"none\">\n      <g clip-path=\"url(#clip0_13082_4925)\">\n        <path\n          d=\"M12 0C5.38346 0 0 5.38346 0 12C0 18.6165 5.38346 24 12 24C18.6165 24 24 18.6165 24 12C24 5.38346 18.6165 0 12 0ZM18.7068 8.84211L11.0376 16.4511C10.5865 16.9023 9.86466 16.9323 9.38346 16.4812L5.32331 12.782C4.84211 12.3308 4.81203 11.5789 5.23308 11.0977C5.68421 10.6165 6.43609 10.5865 6.91729 11.0376L10.1353 13.985L16.9925 7.12782C17.4737 6.64662 18.2256 6.64662 18.7068 7.12782C19.188 7.60902 19.188 8.3609 18.7068 8.84211Z\"\n          fill=\"#CB9932\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_13082_4925\">\n          <rect width=\"24\" height=\"24\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default CheckSecondaryIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,mBAAmB,EAAE,SAAS,EAA0B;IAC/D,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,6LAAC;gBAAE,aAAU;0BACX,cAAA,6LAAC;oBACC,GAAE;oBACF,MAAK;;;;;;;;;;;0BAGT,6LAAC;0BACC,cAAA,6LAAC;oBAAS,IAAG;8BACX,cAAA,6LAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK5C;KAhBS;uCAkBM", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/PreviewResumeIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction PreviewResumeIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"25\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <path\n        d=\"M19.4419 1.28516V1.28613C19.546 1.28464 19.6494 1.30297 19.7456 1.34277L19.8169 1.37695C19.8854 1.41401 19.948 1.46142 20.0024 1.51758L27.5933 9.10742V9.1084L27.645 9.16602C27.7603 9.30714 27.8241 9.48419 27.8247 9.66797V27.8438C27.8247 28.605 27.5222 29.3347 26.9839 29.873C26.4456 30.4114 25.7159 30.7138 24.9546 30.7139H7.04346C6.37733 30.7139 5.73491 30.483 5.22412 30.0645L5.01416 29.873C4.47584 29.3347 4.17334 28.6051 4.17334 27.8438V4.15527C4.17338 3.39401 4.47586 2.66428 5.01416 2.12598L5.22412 1.93457C5.7349 1.5161 6.37736 1.28517 7.04346 1.28516H19.4419ZM7.04541 2.87012C6.7472 2.87016 6.45962 2.97377 6.23096 3.16113L6.13721 3.24707C5.89621 3.48807 5.76029 3.81446 5.76025 4.15527V27.8438C5.76026 28.1846 5.89617 28.5119 6.13721 28.7529L6.23096 28.8379C6.45963 29.0253 6.74717 29.1289 7.04541 29.1289H24.9546L25.0806 29.123C25.2063 29.1109 25.3296 29.0796 25.4468 29.0312C25.6029 28.9668 25.7452 28.8723 25.8647 28.7529C25.9842 28.6336 26.0794 28.4919 26.144 28.3359L26.186 28.2168C26.2227 28.096 26.2417 27.9704 26.2417 27.8438V10.4609H21.1753C20.5059 10.4609 19.8635 10.195 19.3901 9.72168C18.9168 9.2483 18.6509 8.60595 18.6509 7.93652V2.87012H7.04541ZM20.2349 7.93652C20.2349 8.18554 20.3332 8.4245 20.5093 8.60059L20.5786 8.66309C20.7456 8.79983 20.9556 8.87597 21.1733 8.87598H25.1196L20.2349 3.99121V7.93652Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M12.3069 6.4873C12.7821 6.33047 13.2913 6.29936 13.7844 6.39746L13.9934 6.44727C14.4755 6.58088 14.9162 6.83691 15.2717 7.19238L15.4182 7.34961C15.7449 7.72854 15.9685 8.18664 16.0667 8.67969L16.1008 8.8916C16.162 9.38833 16.0941 9.89378 15.9016 10.3584C15.7093 10.8228 15.4003 11.2282 15.0061 11.5361L14.8313 11.6621C14.3537 11.9812 13.7924 12.1513 13.218 12.1514H13.217C12.5433 12.1504 11.8938 11.9165 11.3772 11.4932L11.1643 11.2998C10.6198 10.7553 10.3138 10.0171 10.3127 9.24707L10.3206 9.03223C10.3575 8.5332 10.5227 8.05091 10.802 7.63281L10.928 7.45801C11.2359 7.06383 11.6414 6.75485 12.1057 6.5625L12.3069 6.4873ZM13.0872 7.93359C12.7852 7.964 12.5009 8.097 12.2844 8.31348C12.037 8.56093 11.8982 8.89713 11.8977 9.24707L11.9124 9.44043C11.9409 9.63256 12.0116 9.81665 12.1204 9.97949L12.2395 10.1338C12.3699 10.2775 12.5314 10.3909 12.7122 10.4658L12.8977 10.5273C13.0859 10.5743 13.283 10.5791 13.4749 10.541L13.6624 10.4893C13.8453 10.4238 14.0121 10.3182 14.1506 10.1797C14.3352 9.99509 14.461 9.75994 14.512 9.50391L14.5364 9.31055C14.5426 9.18148 14.5296 9.05212 14.4983 8.92676L14.4368 8.74121C14.3618 8.56046 14.2485 8.39894 14.1047 8.26855L13.9504 8.14941C13.7335 8.00453 13.4788 7.92685 13.218 7.92676L13.0872 7.93359Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M14.0181 12.252L14.2183 12.2568C15.2193 12.3076 16.1692 12.7274 16.8813 13.4395L17.02 13.585C17.693 14.328 18.0686 15.2965 18.0698 16.3037V16.7266C18.0698 16.9104 18.0056 17.0875 17.8901 17.2285L17.8374 17.2871C17.6888 17.4356 17.487 17.5186 17.2769 17.5186C17.0931 17.5185 16.9158 17.4552 16.7749 17.3398L16.7163 17.2871C16.5677 17.1385 16.4849 16.9367 16.4849 16.7266V16.3037L16.4722 16.0605C16.4236 15.5765 16.2328 15.1174 15.9243 14.7412L15.7612 14.5605C15.3566 14.1559 14.8257 13.9064 14.2612 13.8496L14.0171 13.8369H12.4175C11.8453 13.8377 11.2937 14.0369 10.855 14.3965L10.6743 14.5605C10.2119 15.023 9.95162 15.6497 9.95068 16.3037V16.7266C9.95062 16.9104 9.8865 17.0875 9.771 17.2285L9.71826 17.2871C9.56964 17.4356 9.36781 17.5186 9.15771 17.5186C8.97399 17.5185 8.7967 17.4552 8.65576 17.3398L8.59717 17.2871C8.44859 17.1385 8.3658 16.9367 8.36572 16.7266V16.3037L8.37061 16.1025C8.42148 15.1015 8.84113 14.1515 9.55322 13.4395L9.69873 13.3018C10.4418 12.6286 11.4101 12.2532 12.4175 12.252H14.0181Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M17.1772 21.002L17.2554 21.0059C17.4369 21.0238 17.6077 21.1034 17.7378 21.2334C17.8864 21.382 17.9701 21.5838 17.9702 21.7939C17.9702 22.0041 17.8864 22.2058 17.7378 22.3545C17.6076 22.4847 17.437 22.5651 17.2554 22.583L17.1772 22.5869H8.77197C8.58813 22.5869 8.411 22.5227 8.27002 22.4072L8.21143 22.3545C8.06294 22.2059 7.97998 22.004 7.97998 21.7939C7.98005 21.5838 8.06284 21.382 8.21143 21.2334L8.27002 21.1807C8.41096 21.0653 8.58824 21.002 8.77197 21.002H17.1772Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M21.9321 24.6504C22.1422 24.6504 22.3441 24.7334 22.4927 24.8818L22.5454 24.9404C22.6609 25.0814 22.725 25.2585 22.7251 25.4424C22.7251 25.6526 22.6413 25.8543 22.4927 26.0029C22.344 26.1516 22.1423 26.2354 21.9321 26.2354H8.77197C8.58813 26.2353 8.411 26.1712 8.27002 26.0557L8.21143 26.0029C8.06294 25.8543 7.97998 25.6525 7.97998 25.4424C7.98005 25.2323 8.06284 25.0304 8.21143 24.8818L8.27002 24.8291C8.41096 24.7137 8.58824 24.6505 8.77197 24.6504H21.9321Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n    </svg>\n  );\n}\n\nexport default PreviewResumeIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB,EAAE,SAAS,EAA0B;IAC9D,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;;;;;;;AAIpB;KA9BS;uCAgCM", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/StarIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction StarIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"16\" height=\"16\" viewBox=\"0 0 20 19\" fill=\"none\">\n      <path\n        d=\"M15.9184 11.82C15.6594 12.071 15.5404 12.434 15.5994 12.79L16.4884 17.71C16.5634 18.127 16.3874 18.549 16.0384 18.79C15.6964 19.04 15.2414 19.07 14.8684 18.87L10.4394 16.56C10.2854 16.478 10.1144 16.434 9.93939 16.429H9.66839C9.57439 16.443 9.48239 16.473 9.39839 16.519L4.96839 18.84C4.74939 18.95 4.50139 18.989 4.25839 18.95C3.66639 18.838 3.27139 18.274 3.36839 17.679L4.25839 12.759C4.31739 12.4 4.19839 12.035 3.93939 11.78L0.328388 8.28C0.0263875 7.987 -0.0786125 7.547 0.0593875 7.15C0.193388 6.754 0.535388 6.465 0.948388 6.4L5.91839 5.679C6.29639 5.64 6.62839 5.41 6.79839 5.07L8.98839 0.58C9.04039 0.48 9.10739 0.388 9.18839 0.31L9.27839 0.24C9.32539 0.188 9.37939 0.145 9.43939 0.11L9.54839 0.07L9.71839 0H10.1394C10.5154 0.039 10.8464 0.264 11.0194 0.6L13.2384 5.07C13.3984 5.397 13.7094 5.624 14.0684 5.679L19.0384 6.4C19.4584 6.46 19.8094 6.75 19.9484 7.15C20.0794 7.551 19.9664 7.991 19.6584 8.28L15.9184 11.82Z\"\n        fill=\"#CB9932\"\n      />\n    </svg>\n  );\n}\n\nexport default StarIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAA0B;IACrD,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBAC5G,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KATS;uCAWM", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/ResumeModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef } from \"react\";\n\nimport Button from \"../formElements/Button\";\n// import { useTranslations } from \"next-intl\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport \"../../styles/eventModal.scss\";\n\ninterface ResumeModalProps {\n  isOpen: boolean; // Controls whether the modal is visible\n  onClose: () => void; // Function to close the modal\n  resumeLink?: string | null; // Optional link to the resume PDF\n}\n// 🔁 Static test link\n// const resumeLink = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\n\nconst ResumeModal: React.FC<ResumeModalProps> = ({ isOpen, onClose, resumeLink }) => {\n  // const t = useTranslations(\"common\");\n  // const [fileError, setFileError] = useState<string>(\"\");\n\n  // 🧠 Ref to detect clicks outside the modal\n  const modalRef = useRef<HTMLDivElement>(null);\n\n  // 🧩 Detect outside click and close modal\n  useEffect(() => {\n    const handleOutsideClick = (event: MouseEvent) => {\n      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener(\"mousedown\", handleOutsideClick);\n    }\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handleOutsideClick);\n    };\n  }, [isOpen, onClose]);\n\n  // ✅ Prevent rendering if modal not open\n  if (!isOpen) return null;\n  return (\n    <div className=\"modal theme-modal show-modal modal-lg\">\n      <div className=\"modal-dialog modal-dialog-centered\" ref={modalRef}>\n        <div className=\"modal-content\">\n          {/* ✅ Header */}\n          <div className=\"modal-header\">\n            <h4 className=\"m-0\">Resume Preview</h4>\n            <Button className=\"modal-close-btn\" onClick={onClose} type=\"button\">\n              <ModalCloseIcon />\n            </Button>\n          </div>\n\n          {/* ✅ Resume preview area */}\n          {resumeLink && (\n            <div className=\"modal-body\" style={{ height: \"80vh\" }}>\n              <iframe src={resumeLink} className=\"w-100 h-100\" title=\"Resume Preview\" style={{ border: \"none\" }} />\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResumeModal;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA,+CAA+C;AAC/C;;;AANA;;;;;AAcA,sBAAsB;AACtB,sHAAsH;AAEtH,MAAM,cAA0C,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE;;IAC9E,uCAAuC;IACvC,0DAA0D;IAE1D,4CAA4C;IAC5C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACxE;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,aAAa;YACzC;YAEA;yCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;gCAAG;QAAC;QAAQ;KAAQ;IAEpB,wCAAwC;IACxC,IAAI,CAAC,QAAQ,OAAO;IACpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;YAAqC,KAAK;sBACvD,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAM;;;;;;0CACpB,6LAAC,+IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;gCAAS,MAAK;0CACzD,cAAA,6LAAC,wJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;oBAKlB,4BACC,6LAAC;wBAAI,WAAU;wBAAa,OAAO;4BAAE,QAAQ;wBAAO;kCAClD,cAAA,6LAAC;4BAAO,KAAK;4BAAY,WAAU;4BAAc,OAAM;4BAAiB,OAAO;gCAAE,QAAQ;4BAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9G;GAhDM;KAAA;uCAkDS", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/ConfirmationModal.tsx"], "sourcesContent": ["import React from \"react\";\nimport Button from \"../formElements/Button\";\nimport \"../../styles/eventModal.scss\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\n\ninterface ConfirmationModalProps {\n  isOpen: boolean;\n  onClose?: () => void;\n  onConfirm?: () => void;\n  title: string;\n  message: string;\n  confirmButtonText?: string;\n  cancelButtonText?: string; // When not provided or empty, cancel button will be hidden\n  children?: React.ReactNode;\n  loading?: boolean;\n  loadingText?: string;\n}\n\nconst ConfirmationModal: React.FC<ConfirmationModalProps> = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmButtonText = \"Confirm\",\n  cancelButtonText, // No default value - when undefined/empty, cancel button won't show\n  children,\n  loading,\n  loadingText = \"Proceeding...\",\n}) => {\n  if (!isOpen) return null;\n\n  return (\n    <>\n      <div className=\"modal theme-modal show-modal\">\n        <div className=\"modal-dialog modal-dialog-centered\">\n          <div className=\"modal-content\">\n            <div className=\"modal-header justify-content-center pb-3\">\n              <h2 className=\"m-0\">{title}</h2>\n\n              <Button className={`modal-close-btn ${loading ? \"fade-close\" : \"\"}`} onClick={onClose} disabled={loading}>\n                <ModalCloseIcon />\n              </Button>\n            </div>\n            <div className=\"modal-body pt-0\">\n              {/* qualification-card */}\n              <p className=\"text-center pb-4 font16\">\n                {message}\n                {children}\n              </p>\n\n              <div className=\"button-align\">\n                {cancelButtonText && cancelButtonText.trim() !== \"\" && (\n                  <Button className={\"dark-outline-btn rounded-md w-100\"} onClick={onClose} disabled={loading}>\n                    {cancelButtonText}\n                  </Button>\n                )}\n                {confirmButtonText && (\n                  <Button className={`primary-btn rounded-md w-100 ${loading ? \"opacity-75\" : \"\"}`} onClick={onConfirm} disabled={loading}>\n                    {loading ? loadingText : confirmButtonText}\n                  </Button>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default ConfirmationModal;\n"], "names": [], "mappings": ";;;;AACA;AAEA;;;;;AAeA,MAAM,oBAAsD,CAAC,EAC3D,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,oBAAoB,SAAS,EAC7B,gBAAgB,EAChB,QAAQ,EACR,OAAO,EACP,cAAc,eAAe,EAC9B;IACC,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAO;;;;;;8CAErB,6LAAC,+IAAA,CAAA,UAAM;oCAAC,WAAW,CAAC,gBAAgB,EAAE,UAAU,eAAe,IAAI;oCAAE,SAAS;oCAAS,UAAU;8CAC/F,cAAA,6LAAC,wJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAE,WAAU;;wCACV;wCACA;;;;;;;8CAGH,6LAAC;oCAAI,WAAU;;wCACZ,oBAAoB,iBAAiB,IAAI,OAAO,oBAC/C,6LAAC,+IAAA,CAAA,UAAM;4CAAC,WAAW;4CAAqC,SAAS;4CAAS,UAAU;sDACjF;;;;;;wCAGJ,mCACC,6LAAC,+IAAA,CAAA,UAAM;4CAAC,WAAW,CAAC,6BAA6B,EAAE,UAAU,eAAe,IAAI;4CAAE,SAAS;4CAAW,UAAU;sDAC7G,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7C;KAnDM;uCAqDS", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/jobRequirementConstant.ts"], "sourcesContent": ["import { JobSelectOption } from \"@/interfaces/jobRequirementesInterfaces\";\n\n/**\n * Job category options\n */\nexport const CATEGORY_OPTION: JobSelectOption[] = [\n  { label: \"Full time\", value: \"full_time\" },\n  { label: \"Part time\", value: \"part_time\" },\n  { label: \"Contract\", value: \"contract\" },\n  { label: \"Internship\", value: \"internship\" },\n  { label: \"Freelance\", value: \"freelance\" },\n];\n\n/**\n * Salary cycle options\n */\nexport const SALARY_CYCLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Per Hour\", value: \"per hour\" },\n  { label: \"Per Month\", value: \"per month\" },\n  { label: \"Per Annum\", value: \"per annum\" },\n];\n\n/**\n * Location type options\n */\nexport const LOCATION_TYPE_OPTIONS: JobSelectOption[] = [\n  { label: \"Remote\", value: \"remote\" },\n  { label: \"Hybrid\", value: \"hybrid\" },\n  { label: \"On-site\", value: \"onsite\" },\n];\n\n/**\n * Tone style options\n */\nexport const TONE_STYLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Professional & Formal\", value: \"Professional_Formal\" },\n  { label: \"Conversational & Approachable\", value: \"Conversational_Approachable\" },\n  { label: \"Bold & Energetic\", value: \"Bold_Energetic\" },\n  { label: \"Inspirational & Mission-Driven\", value: \"Inspirational_Mission-Driven\" },\n  { label: \"Technical & Precise\", value: \"Technical_Precise\" },\n  { label: \"Creative & Fun\", value: \"Creative_Fun\" },\n  { label: \"Inclusive & Human-Centered\", value: \"Inclusive_Human-Centered\" },\n  { label: \"Minimalist & Straightforward\", value: \"Minimalist_Straightforward\" },\n];\n\n/**\n * Compliance options\n */\nexport const COMPLIANCE_OPTIONS: JobSelectOption[] = [\n  { label: \"Equal Employment Opportunity (EEO) Statement\", value: \"Equal Employment Opportunity (EEO) Statement\" },\n  {\n    label: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n    value: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n  },\n  { label: \"Disability Accommodation Statement\", value: \"Disability Accommodation Statement\" },\n  {\n    label: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n    value: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n  },\n  { label: \"Diversity & Inclusion Commitment\", value: \"Diversity & Inclusion Commitment\" },\n  {\n    label: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n    value: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n  },\n  {\n    label: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n    value: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n  },\n  { label: \"Work Authorization & Immigration Statement\", value: \"Work Authorization & Immigration Statement\" },\n];\n\nexport const EXPERIENCE_LEVEL_OPTIONS: JobSelectOption[] = [\n  { label: \"General\", value: \"General\" },\n  { label: \"No experience necessary\", value: \"No experience necessary\" },\n  { label: \"Entry-Level Position\", value: \"Entry-Level Position\" },\n  { label: \"Mid-Level Professional\", value: \"Mid-Level Professional\" },\n  { label: \"Senior/Experienced Professional\", value: \"Senior/Experienced Professional\" },\n  { label: \"Managerial/Executive Level\", value: \"Managerial/Executive Level\" },\n  { label: \"Specialized Expert\", value: \"Specialized Expert\" },\n];\n\nexport const DEPARTMENT_OPTION: JobSelectOption[] = [\n  { label: \"IT\", value: \"IT\" },\n  { label: \"HR\", value: \"HR\" },\n  { label: \"Marketing\", value: \"Marketing\" },\n  { label: \"Finance\", value: \"Finance\" },\n  { label: \"Sales\", value: \"Sales\" },\n];\n/**\n * Constants for file upload validation\n */\nexport const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\nexport const FILE_TYPE = \"application/pdf\";\nexport const FILE_NAME = \".pdf\";\n\n/**\n * Remove all $ and space symbols to clean the input\n */\nexport const SALARY_REMOVE_SYMBOL_REGEX = /[\\$\\s]/g;\n\n/**\n * Currency symbol\n */\nexport const CURRENCY_SYMBOL = \"$\";\n\n/**\n * Button list for SunEditor\n */\nexport const SUN_EDITOR_BUTTON_LIST = [\n  [\"font\", \"fontSize\", \"formatBlock\"],\n  [\"bold\", \"underline\", \"italic\"],\n  [\"fontColor\", \"hiliteColor\"],\n  [\"align\", \"list\", \"lineHeight\"],\n];\n\n/**\n * HiringType Select [Internal,External]\n */\nexport const HIRING_TYPE = {\n  INTERNAL: \"internal\",\n  EXTERNAL: \"external\",\n};\n\n/**\n * Skill categories\n */\nexport const SKILL_CATEGORY = {\n  Personal_Health: \"Personal Health\",\n  Social_Interaction: \"Social Interaction\",\n  Mastery_Of_Emotions: \"Mastery of Emotions\",\n  Mentality: \"Mentality\",\n  Cognitive_Abilities: \"Cognitive Abilities\",\n};\n\n/**\n * Application status values\n */\nexport const APPLICATION_STATUS = {\n  PENDING: \"Pending\",\n  APPROVED: \"Approved\",\n  REJECTED: \"Rejected\",\n  HIRED: \"Hired\",\n  ON_HOLD: \"On-Hold\",\n  FINAL_REJECT: \"Final-Reject\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport const SKILL_TYPE = {\n  ROLE: \"role\",\n  CULTURE: \"culture\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport type SkillType = (typeof SKILL_TYPE)[keyof typeof SKILL_TYPE];\n\n/**\n * HiringType key for searchParams\n */\nexport const HIRING_TYPE_KEY = \"hiringType\";\n\nexport const CURSOR_POINT = { cursor: \"pointer\" };\n\nexport const COMPLIANCE_LINK = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\n\n// Dynamic uploading messages for job generation\nexport const JOB_GENERATION_UPLOAD_MESSAGES = [\n  \"Analyzing your job description...\",\n  \"Extracting key requirements...\",\n  \"Processing document content...\",\n  \"Identifying skills and qualifications...\",\n  \"Parsing job details...\",\n  \"Almost ready...\",\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,kBAAqC;IAChD;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,uBAA0C;IACrD;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,wBAA2C;IACtD;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAS;CACrC;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAyB,OAAO;IAAsB;IAC/D;QAAE,OAAO;QAAiC,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAoB,OAAO;IAAiB;IACrD;QAAE,OAAO;QAAkC,OAAO;IAA+B;IACjF;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAkB,OAAO;IAAe;IACjD;QAAE,OAAO;QAA8B,OAAO;IAA2B;IACzE;QAAE,OAAO;QAAgC,OAAO;IAA6B;CAC9E;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAgD,OAAO;IAA+C;IAC/G;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAsC,OAAO;IAAqC;IAC3F;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAoC,OAAO;IAAmC;IACvF;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAA8C,OAAO;IAA6C;CAC5G;AAEM,MAAM,2BAA8C;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAA2B,OAAO;IAA0B;IACrE;QAAE,OAAO;QAAwB,OAAO;IAAuB;IAC/D;QAAE,OAAO;QAA0B,OAAO;IAAyB;IACnE;QAAE,OAAO;QAAmC,OAAO;IAAkC;IACrF;QAAE,OAAO;QAA8B,OAAO;IAA6B;IAC3E;QAAE,OAAO;QAAsB,OAAO;IAAqB;CAC5D;AAEM,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAIM,MAAM,kBAAkB,IAAI,OAAO,MAAM,MAAM;AAC/C,MAAM,gBAAgB,IAAI,OAAO,MAAM,MAAM;AAC7C,MAAM,YAAY;AAClB,MAAM,YAAY;AAKlB,MAAM,6BAA6B;AAKnC,MAAM,kBAAkB;AAKxB,MAAM,yBAAyB;IACpC;QAAC;QAAQ;QAAY;KAAc;IACnC;QAAC;QAAQ;QAAa;KAAS;IAC/B;QAAC;QAAa;KAAc;IAC5B;QAAC;QAAS;QAAQ;KAAa;CAChC;AAKM,MAAM,cAAc;IACzB,UAAU;IACV,UAAU;AACZ;AAKO,MAAM,iBAAiB;IAC5B,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;IACrB,WAAW;IACX,qBAAqB;AACvB;AAKO,MAAM,qBAAqB;IAChC,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,cAAc;AAChB;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;AACX;AAUO,MAAM,kBAAkB;AAExB,MAAM,eAAe;IAAE,QAAQ;AAAU;AAEzC,MAAM,kBAAkB;AAGxB,MAAM,iCAAiC;IAC5C;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/CandidatesServices/candidatesApplicationServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\n\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport {\n  AdditionalInfoPayload,\n  CandidateApplication,\n  CandidateProfileResponse,\n  FetchCandidatesParams,\n  ICandidateInterviewHistory,\n  IFinalAssessment,\n  ISkillSpecificAssessment,\n  PromoteDemotePayload,\n  topCandidateApplication,\n} from \"@/interfaces/candidatesInterface\";\n\n// ============================================================================\n// CANDIDATE APPLICATION MANAGEMENT\n// ============================================================================\n\n/**\n * Fetches candidates with their job applications using advanced filtering and pagination\n *\n * This function retrieves a paginated list of candidates along with their application details.\n * It supports comprehensive filtering options including job-specific filtering, name search,\n * and active/archived status filtering.\n *\n * @async\n * @function fetchCandidatesApplications\n * @param {FetchCandidatesParams} data - Query parameters for filtering and pagination\n * @param {number} [data.page] - Page offset for pagination (0-based)\n * @param {number} [data.limit] - Maximum number of candidates to return per page\n * @param {string} [data.searchStr] - Search string to filter candidates by name\n * @param {boolean} [data.isActive] - Filter by status: true for active, false for archived\n * @param {number} [data.jobId] - Optional job ID to filter candidates for specific position\n * @returns {Promise<ApiResponse<CandidateApplication[]>>} Promise resolving to candidate applications list\n *\n */\nexport const fetchCandidatesApplications = (data: FetchCandidatesParams): Promise<ApiResponse<CandidateApplication[]>> => {\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATES_WITH_APPLICATIONS, { ...data });\n};\n\n/**\n * Fetches top-ranked candidates with their applications for a specific job\n *\n * This function retrieves candidates who have been identified as top performers\n * based on AI scoring, ATS evaluation, and other ranking criteria. The results\n * are pre-filtered and sorted by the backend ranking algorithm.\n *\n * @async\n * @function fetchTopCandidatesApplications\n * @param {number} [jobId] - Optional job ID to filter top candidates for specific position\n * @returns {Promise<ApiResponse<topCandidateApplication[]>>} Promise resolving to top candidates list\n */\nexport const fetchTopCandidatesApplications = (jobId?: number): Promise<ApiResponse<topCandidateApplication[]>> => {\n  return http.get(endpoint.candidatesApplication.GET_TOP_CANDIDATES_WITH_APPLICATIONS, {\n    jobId,\n  });\n};\n\n/**\n * Promotes or demotes a candidate in the application ranking\n *\n * This function allows hiring managers to manually adjust candidate rankings\n * by promoting high-potential candidates or demoting those who don't meet\n * expectations. The action affects the candidate's visibility and priority.\n *\n * @async\n * @function promoteDemoteCandidate\n * @param {PromoteDemotePayload} payload - The promotion/demotion request data\n * @param {number} payload.candidateId - ID of the candidate to promote/demote\n * @param {number} payload.applicationId - ID of the specific application\n * @param {\"Promoted\" | \"Demoted\"} payload.action - Action to perform\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\n */\nexport const promoteDemoteCandidate = async (payload: PromoteDemotePayload): Promise<ApiResponse<null>> => {\n  return await http.put(endpoint.candidatesApplication.PROMOTE_DEMOTE_CANDIDATE, payload);\n};\n\n/**\n * Adds additional information to a candidate's application\n *\n * This function allows candidates or hiring managers to submit supplementary\n * information, documents, or clarifications to an existing application.\n * Commonly used for portfolio submissions, additional certifications, or\n * responses to specific questions.\n *\n * @async\n * @function addApplicantAdditionalInfo\n * @param {AdditionalInfoPayload} payload - The additional information data\n * @param {string} payload.applicationId - ID of the application to update\n * @param {string} payload.description - Description or text content\n * @param {string} payload.images - Image URLs or file references\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\n */\nexport const addApplicantAdditionalInfo = async (payload: AdditionalInfoPayload): Promise<ApiResponse<null>> => {\n  return await http.post(endpoint.candidatesApplication.ADDITIONAL_INFO, payload);\n};\n\n// ============================================================================\n// CANDIDATE PROFILE MANAGEMENT\n// ============================================================================\n\n/**\n * Fetches comprehensive candidate profile details by candidate ID\n *\n * This function retrieves detailed information about a specific candidate including\n * personal details, job application status, assigned interviewer information,\n * resume links, and current round information. Essential for candidate profile views.\n *\n * @async\n * @function fetchCandidateProfile\n * @param {number | string} candidateId - The unique identifier of the candidate\n * @returns {Promise<ApiResponse<CandidateProfileResponse>>} Promise resolving to candidate profile data\n */\nexport const fetchCandidateProfile = (jobApplicationId: number | string): Promise<ApiResponse<CandidateProfileResponse>> => {\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATE_DETAILS, { jobApplicationId });\n};\n\n/**\n * Updates the job application status for hire/reject decisions\n *\n * This function allows hiring managers to make final decisions on candidate applications\n * by updating the status to either \"Hired\" or \"Final-Reject\". This action typically\n * triggers workflow notifications and updates the candidate's status across the system.\n *\n * @async\n * @function updateJobApplicationStatus\n * @param {number} jobApplicationId - The unique identifier of the job application\n * @param {string} status - The new status (\"Hired\" or \"Final-Reject\")\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\n * ```\n */\nexport const updateJobApplicationStatus = async (jobApplicationId: number, status: string): Promise<ApiResponse<null>> => {\n  return await http.put(endpoint.candidatesApplication.UPDATE_JOB_APPLICATION_STATUS.replace(\":jobApplicationId\", jobApplicationId.toString()), {\n    status,\n  });\n};\n\n/**\n * Retrieves comprehensive interview history for a specific candidate\n *\n * This function fetches detailed interview records across all rounds including\n * interviewer information, skill scores, hard skill marks, interview summaries,\n * and AI-powered performance analysis. Essential for tracking candidate progress.\n *\n * @async\n * @function getCandidateInterviewHistory\n * @param {number} candidateId - The unique identifier of the candidate\n * @returns {Promise<ApiResponse<ICandidateInterviewHistory[]>>} Promise resolving to interview history array\n */\nexport const getCandidateInterviewHistory = async (applicationId: string): Promise<ApiResponse<ICandidateInterviewHistory[]>> => {\n  return await http.get(endpoint.candidatesApplication.GET_CANDIDATE_INTERVIEW_HISTORY.replace(\":applicationId\", applicationId));\n};\n\n/**\n * Retrieves comprehensive final assessment summary for a specific candidate application\n *\n * This function fetches the complete final assessment analysis generated after all\n * interview rounds are completed. It includes AI-powered insights, success probability\n * calculations, skill summaries, and personalized development recommendations.\n *\n * @async\n * @function getApplicationFinalSummary\n * @param {string} candidateId - The unique identifier of the candidate\n * @returns {Promise<ApiResponse<IFinalAssessment>>} Promise resolving to final assessment data\n *\n * Assessment Data Includes:\n * - Overall success probability percentage (0-100)\n * - Comprehensive skill summary with AI analysis\n * - Personalized development recommendations by category\n * - Job application reference details\n * ```\n */\nexport const getApplicationFinalSummary = async (jobApplicationId: string): Promise<ApiResponse> => {\n  return await http.get(endpoint.candidatesApplication.GET_APPLICATION_FINAL_SUMMARY.replace(\":jobApplicationId\", jobApplicationId));\n};\n\n/**\n * Retrieves detailed skill-specific assessment data for a candidate\n *\n * This function fetches comprehensive skill evaluation data aggregated from all\n * completed interview rounds. The data is flattened and optimized for frontend\n * consumption, providing detailed insights into each skill area evaluated.\n *\n * @async\n * @function getApplicationSkillScoreData\n * @param {string} candidateId - The unique identifier of the candidate\n * @returns {Promise<ApiResponse<ISkillSpecificAssessment>>} Promise resolving to skill assessment data\n *\n * Skill Data Includes:\n * - Individual skill scores and marks (0-10 scale)\n * - Skill-specific strengths and achievements\n * - Identified potential gaps and improvement areas\n * - Success probability for each skill area\n * - Overall career-based skills score\n * - Interviewer-specific evaluations and feedback\n */\n// export const getApplicationSkillScoreData = async (jobApplicationId: string): Promise<ApiResponse<ISkillSpecificAssessment>> => {\n//   return await http.get(endpoint.candidatesApplication.GET_APPLICATION_SKILL_SCORE_DATA.replace(\":jobApplicationId\", jobApplicationId));\n// };\n\n/**\n * Generates final summary for a candidate application\n *\n * This function triggers the generation of a comprehensive final summary for a candidate\n * based on their interview performance, skill assessments, and overall evaluation data.\n * The summary includes AI-powered insights and recommendations for hiring decisions.\n *\n * @async\n * @function generateFinalSummary\n * @param {number | string} candidateId - The unique identifier of the candidate\n * @param {number | string} jobApplicationId - The unique identifier of the job application\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\n *\n * Summary Generation Includes:\n * - Comprehensive analysis of interview performance across all rounds\n * - Skill-specific evaluation and scoring aggregation\n * - AI-powered insights and recommendations\n * - Overall success probability calculation\n * - Personalized development recommendations\n */\nexport const generateFinalSummary = async (jobApplicationId: number | string): Promise<ApiResponse<null>> => {\n  return await http.get(endpoint.candidatesApplication.GENERATE_FINAL_SUMMARY, {\n    jobApplicationId: jobApplicationId.toString(),\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAqCO,MAAM,8BAA8B,CAAC;IAC1C,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,gCAAgC,EAAE;QAAE,GAAG,IAAI;IAAC;AAC7F;AAcO,MAAM,iCAAiC,CAAC;IAC7C,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,oCAAoC,EAAE;QACnF;IACF;AACF;AAiBO,MAAM,yBAAyB,OAAO;IAC3C,OAAO,MAAM,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,wBAAwB,EAAE;AACjF;AAkBO,MAAM,6BAA6B,OAAO;IAC/C,OAAO,MAAM,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,eAAe,EAAE;AACzE;AAkBO,MAAM,wBAAwB,CAAC;IACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,qBAAqB,EAAE;QAAE;IAAiB;AAC3F;AAgBO,MAAM,6BAA6B,OAAO,kBAA0B;IACzE,OAAO,MAAM,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,qBAAqB,iBAAiB,QAAQ,KAAK;QAC5I;IACF;AACF;AAcO,MAAM,+BAA+B,OAAO;IACjD,OAAO,MAAM,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,+BAA+B,CAAC,OAAO,CAAC,kBAAkB;AACjH;AAqBO,MAAM,6BAA6B,OAAO;IAC/C,OAAO,MAAM,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,qBAAqB;AAClH;AA8CO,MAAM,uBAAuB,OAAO;IACzC,OAAO,MAAM,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,sBAAsB,EAAE;QAC3E,kBAAkB,iBAAiB,QAAQ;IAC7C;AACF", "debugId": null}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1035, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/noImageFound.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 464, height: 464, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/FinalAssessmentIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction FinalAssessmentIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"22\" height=\"22\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <path d=\"M25.4107 3.76469H22.4252C22.0367 2.66931 20.9905 1.88231 19.7636 1.88231H18.6612C18.2727 0.78675 17.226 0 15.9989 0C14.7719 0 13.7251 0.78675 13.3367 1.88238H12.2342C11.0072 1.88238 9.96111 2.66931 9.57261 3.76475H6.58717C5.0303 3.76475 3.76367 5.03137 3.76367 6.58825V29.1765C3.76367 30.7334 5.0303 32 6.58717 32H25.4107C26.9676 32 28.2342 30.7334 28.2342 29.1765V6.58825C28.2342 5.03131 26.9676 3.76469 25.4107 3.76469ZM11.293 4.70587C11.293 4.18694 11.7152 3.76469 12.2342 3.76469H14.1166C14.6364 3.76469 15.0578 3.34331 15.0578 2.8235C15.0578 2.30456 15.48 1.88231 15.999 1.88231C16.5179 1.88231 16.9402 2.3045 16.9402 2.8235C16.9402 3.34331 17.3615 3.76469 17.8814 3.76469H19.7637C20.2827 3.76469 20.7049 4.18687 20.7049 4.70587V5.64706H11.293V4.70587ZM26.3519 29.1765C26.3519 29.6954 25.9297 30.1177 25.4107 30.1177H6.58717C6.06823 30.1177 5.64598 29.6955 5.64598 29.1765V6.58825C5.64598 6.06931 6.06817 5.64706 6.58717 5.64706H9.41073V6.58825C9.41073 7.10806 9.83211 7.52944 10.3519 7.52944H21.646C22.1658 7.52944 22.5872 7.10806 22.5872 6.58825V5.64706H25.4107C25.9297 5.64706 26.3519 6.06925 26.3519 6.58825V29.1765H26.3519Z\" />\n      <path d=\"M13.1766 11.293H9.41189C8.89214 11.293 8.4707 11.7143 8.4707 12.2342C8.4707 12.754 8.89208 13.1753 9.41189 13.1753H13.1766C13.6963 13.1753 14.1178 12.754 14.1178 12.2342C14.1178 11.7143 13.6963 11.293 13.1766 11.293Z\" />\n      <path d=\"M13.1766 17.8809H9.41189C8.89214 17.8809 8.4707 18.3022 8.4707 18.822C8.4707 19.3418 8.89208 19.7632 9.41189 19.7632H13.1766C13.6963 19.7632 14.1178 19.3419 14.1178 18.822C14.1177 18.3022 13.6963 17.8809 13.1766 17.8809Z\" />\n      <path d=\"M13.1766 24.4707H9.41189C8.89214 24.4707 8.4707 24.8921 8.4707 25.4119C8.4707 25.9317 8.89208 26.3531 9.41189 26.3531H13.1766C13.6963 26.3531 14.1178 25.9317 14.1178 25.4119C14.1178 24.8921 13.6963 24.4707 13.1766 24.4707Z\" />\n      <path d=\"M23.254 10.6272C22.8865 10.2597 22.2905 10.2597 21.923 10.6273L19.765 12.7853L18.5481 11.5685C18.1806 11.2009 17.5846 11.2009 17.2171 11.5685C16.8495 11.936 16.8495 12.5319 17.2171 12.8995L19.0995 14.7819C19.2833 14.9656 19.5241 15.0575 19.765 15.0575C20.0059 15.0575 20.2468 14.9656 20.4305 14.7818L23.2541 11.9583C23.6215 11.5907 23.6215 10.9948 23.254 10.6272Z\" />\n      <path d=\"M23.254 16.2757C22.8865 15.9081 22.2905 15.9081 21.923 16.2757L19.765 18.4337L18.5481 17.2169C18.1806 16.8494 17.5846 16.8494 17.2171 17.2169C16.8495 17.5845 16.8495 18.1804 17.2171 18.5479L19.0995 20.4303C19.2833 20.614 19.5241 20.7059 19.765 20.7059C20.0059 20.7059 20.2468 20.614 20.4305 20.4302L23.2541 17.6067C23.6215 17.2391 23.6215 16.6432 23.254 16.2757Z\" />\n      <path d=\"M21.096 25.4134L22.3128 24.1965C22.6803 23.829 22.6803 23.2331 22.3128 22.8656C21.9453 22.498 21.3493 22.498 20.9818 22.8656L19.765 24.0824L18.5481 22.8655C18.1806 22.498 17.5846 22.498 17.2171 22.8655C16.8495 23.2331 16.8495 23.829 17.2171 24.1965L18.434 25.4134L17.2171 26.6303C16.8495 26.9978 16.8495 27.5937 17.2171 27.9613C17.4008 28.145 17.6418 28.2369 17.8826 28.2369C18.1235 28.2369 18.3644 28.145 18.5481 27.9612L19.765 26.7444L20.9819 27.9613C21.1656 28.145 21.4065 28.2369 21.6474 28.2369C21.8883 28.2369 22.1291 28.145 22.3129 27.9612C22.6805 27.5936 22.6805 26.9978 22.3129 26.6302L21.096 25.4134Z\" />\n    </svg>\n  );\n}\n\nexport default FinalAssessmentIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BACtF,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;KAZS;uCAcM", "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/assessmentService.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse, IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport { IAssessmentSubmission, ICreateAssessmentQuestionRequest, IQuestionData, IShareAssessmentRequest } from \"@/interfaces/finalAssessment\";\nimport { IAssessmentStatus } from \"@/components/views/conductInterview/InterviewSummary\";\nimport { IAssessmentData } from \"@/interfaces/candidateFinalAssessment\";\n\n/**\n * Interface for Final Assessment request\n */\nexport interface IFinalAssessmentRequest {\n  jobId: number;\n  jobApplicationId: number;\n}\nexport interface IFinalAssessmentResponse extends IFinalAssessmentRequest {\n  assessmentId: number;\n}\n\n/**\n * Get assessment status for a job application\n * @param jobApplicationId ID of the job application\n * @returns Promise with API response containing assessment status\n */\nexport const getAssessmentStatus = (jobApplicationId: string): Promise<IApiResponseCommonInterface<IAssessmentStatus>> => {\n  return http.post(endpoint.assessment.GET_ASSESSMENT_STATUS, { jobApplicationId });\n};\n\n/**\n * Create a final assessment for a job application\n * @param data Object containing jobId and jobApplicationId\n * @returns Promise with API response\n */\nexport const createFinalAssessment = (data: IFinalAssessmentRequest): Promise<IApiResponseCommonInterface<IFinalAssessmentResponse>> => {\n  return http.post(endpoint.assessment.CREATE_FINAL_ASSESSMENT, data);\n};\n\n/**\n * Get questions for a final assessment\n * @param finalAssessmentId ID of the final assessment\n * @param isShared Boolean indicating if assessment is shared\n * @param isSubmitted Boolean indicating if assessment is submitted\n * @returns Promise with API response containing assessment questions\n */\nexport const getFinalAssessmentQuestions = (finalAssessmentId: number, jobId: number, jobApplicationId: number): Promise<ApiResponse> => {\n  return http.get(endpoint.assessment.GET_FINAL_ASSESSMENT_QUESTIONS, { finalAssessmentId, jobId, jobApplicationId });\n};\n\n/**\n * Interface for creating assessment question\n */\n\n/**\n * Create a question for a final assessment\n * @param finalAssessmentId ID of the final assessment\n * @param data Question data\n * @returns Promise with API response\n */\nexport const createAssessmentQuestion = (data: ICreateAssessmentQuestionRequest): Promise<IApiResponseCommonInterface<IQuestionData>> => {\n  const url = endpoint.assessment.CREATE_ASSESSMENT_QUESTION;\n  return http.post(url, data);\n};\n\n/**\n * Interface for sharing assessment with candidate\n */\n\n/**\n * Share assessment with candidate via email\n * @param data Object containing finalAssessmentId and optional candidateEmail\n * @returns Promise with API response\n */\nexport const shareAssessmentToCandidate = (data: IShareAssessmentRequest): Promise<ApiResponse> => {\n  return http.post(endpoint.assessment.SHARE_ASSESSMENT, data);\n};\n\n/**\n * Get assessment data for a candidate using finalAssessmentId\n * @param finalAssessmentId ID of the final assessment\n * @returns Promise with API response containing assessment data\n */\nexport const getFinalAssessmentByCandidate = (finalAssessmentId: number): Promise<IApiResponseCommonInterface<IAssessmentData>> => {\n  return http.get(endpoint.assessment.GET_FINAL_ASSESSMENT_BY_CANDIDATE, { finalAssessmentId });\n};\n\n/**\n * Interface for assessment submission data\n */\n\n/**\n * Submit candidate's assessment answers\n * @param data Assessment submission data\n * @returns Promise with API response\n */\nexport const submitAssessment = (data: IAssessmentSubmission): Promise<ApiResponse> => {\n  return http.post(endpoint.assessment.SUBMIT_ASSESSMENT, data);\n};\n\n/**\n * Interface for email verification request\n */\nexport interface IVerifyEmailRequest {\n  email: string;\n  token: string;\n}\n\n/**\n * Response interface for email verification\n */\nexport interface IVerifyEmailResponse {\n  finalAssessmentId: number;\n}\n\n/**\n * Verify if candidate email exists in the system\n * @param data Object containing email and token\n * @returns Promise with API response containing finalAssessmentId\n */\nexport const verifyCandidateEmail = (data: IVerifyEmailRequest): Promise<IApiResponseCommonInterface<IVerifyEmailResponse>> => {\n  return http.post(endpoint.assessment.VERIFY_CANDIDATE_EMAIL, data);\n};\n\n/**\n * Interface for assessment token request\n */\nexport interface IAssessmentTokenRequest {\n  finalAssessmentId: number;\n}\n\n/**\n * Interface for assessment token response\n */\nexport interface IAssessmentTokenResponse {\n  token: string;\n}\n\n/**\n * Get assessment token for generating secure assessment URL\n * @param data Object containing finalAssessmentId\n * @returns Promise with API response containing assessment token\n */\nexport const getAssessmentToken = (data: IAssessmentTokenRequest): Promise<IApiResponseCommonInterface<IAssessmentTokenResponse>> => {\n  return http.post(endpoint.assessment.GENERATE_ASSESSMENT_TOKEN, data);\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAsBO,MAAM,sBAAsB,CAAC;IAClC,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,qBAAqB,EAAE;QAAE;IAAiB;AACjF;AAOO,MAAM,wBAAwB,CAAC;IACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,uBAAuB,EAAE;AAChE;AASO,MAAM,8BAA8B,CAAC,mBAA2B,OAAe;IACpF,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,8BAA8B,EAAE;QAAE;QAAmB;QAAO;IAAiB;AACnH;AAYO,MAAM,2BAA2B,CAAC;IACvC,MAAM,MAAM,+HAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,0BAA0B;IAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,KAAK;AACxB;AAWO,MAAM,6BAA6B,CAAC;IACzC,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE;AACzD;AAOO,MAAM,gCAAgC,CAAC;IAC5C,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,iCAAiC,EAAE;QAAE;IAAkB;AAC7F;AAWO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,iBAAiB,EAAE;AAC1D;AAsBO,MAAM,uBAAuB,CAAC;IACnC,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,sBAAsB,EAAE;AAC/D;AAqBO,MAAM,qBAAqB,CAAC;IACjC,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,yBAAyB,EAAE;AAClE", "debugId": null}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/loader/questionGeneratorLoader.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"loader\": \"questionGeneratorLoader-module__dUgNXW__loader\",\n  \"loaderAnim\": \"questionGeneratorLoader-module__dUgNXW__loaderAnim\",\n  \"loader_container\": \"questionGeneratorLoader-module__dUgNXW__loader_container\",\n  \"loader_text\": \"questionGeneratorLoader-module__dUgNXW__loader_text\",\n  \"loader_wrapper\": \"questionGeneratorLoader-module__dUgNXW__loader_wrapper\",\n  \"question_generator_loader_overlay\": \"questionGeneratorLoader-module__dUgNXW__question_generator_loader_overlay\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/loader/QuestionGeneratorLoader.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport styles from \"./questionGeneratorLoader.module.css\";\n\ninterface Props {\n  show: boolean;\n}\n\nconst QuestionGeneratorLoader: React.FC<Props> = ({ show }) => {\n  if (!show) return null;\n\n  return (\n    <div className={styles.question_generator_loader_overlay}>\n      <div className={styles.loader_wrapper}>\n        <div className={styles.loader_container}>\n          <span className={styles.loader}></span>\n        </div>\n        <div className={styles.loader_text}>Generating Questions...</div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuestionGeneratorLoader;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AASA,MAAM,0BAA2C,CAAC,EAAE,IAAI,EAAE;IACxD,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC;QAAI,WAAW,qKAAA,CAAA,UAAM,CAAC,iCAAiC;kBACtD,cAAA,6LAAC;YAAI,WAAW,qKAAA,CAAA,UAAM,CAAC,cAAc;;8BACnC,6LAAC;oBAAI,WAAW,qKAAA,CAAA,UAAM,CAAC,gBAAgB;8BACrC,cAAA,6LAAC;wBAAK,WAAW,qKAAA,CAAA,UAAM,CAAC,MAAM;;;;;;;;;;;8BAEhC,6LAAC;oBAAI,WAAW,qKAAA,CAAA,UAAM,CAAC,WAAW;8BAAE;;;;;;;;;;;;;;;;;AAI5C;KAbM;uCAeS", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1274, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/FinalAssessmentConfirmModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { FC } from \"react\";\nimport <PERSON>ton from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport { useTranslations } from \"next-intl\";\n\ninterface IProps {\n  onClickCancel: () => void;\n  onClickGenerate: () => void;\n  disabled?: boolean;\n}\n\nconst FinalAssessmentConfirmModal: FC<IProps> = ({ onClickCancel, onClickGenerate, disabled }) => {\n  const t = useTranslations();\n\n  return (\n    <div className=\"modal theme-modal show-modal\">\n      <div className=\"modal-dialog modal-dialog-centered modal-xl\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header text-center pb-0\">\n            <h4>{t(\"generate_final_candidate_assessment\")}</h4>\n            <p className=\"m-0 textMd w100 text-center\">{t(\"before_proceeding_please_review_the_candidates_overall_performance\")}</p>\n            <Button className=\"modal-close-btn\" onClick={onClickCancel}>\n              <ModalCloseIcon />\n            </Button>\n          </div>\n          <div className=\"modal-body position-relative\">\n            <div className=\"alert alert-warning mb-4\">\n              <p className=\"m-0\">{t(\"final_assessment_warning_message\")}</p>\n            </div>\n\n            <div className=\"action-btn justify-content-end\">\n              <Button className=\"primary-btn rounded-md\" onClick={onClickGenerate} disabled={disabled}>\n                {t(\"generate_final_assessment\")}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\nexport default FinalAssessmentConfirmModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAYA,MAAM,8BAA0C,CAAC,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE;;IAC3F,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAI,EAAE;;;;;;0CACP,6LAAC;gCAAE,WAAU;0CAA+B,EAAE;;;;;;0CAC9C,6LAAC,+IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;0CAC3C,cAAA,6LAAC,wJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAGnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAO,EAAE;;;;;;;;;;;0CAGxB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+IAAA,CAAA,UAAM;oCAAC,WAAU;oCAAyB,SAAS;oCAAiB,UAAU;8CAC5E,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GA7BM;;QACM,yMAAA,CAAA,kBAAe;;;KADrB;uCA8BS", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/conductInterview.module.scss.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"additional\": \"conductInterview-module-scss-module__yztraq__additional\",\n  \"completed\": \"conductInterview-module-scss-module__yztraq__completed\",\n  \"conduct_interview\": \"conductInterview-module-scss-module__yztraq__conduct_interview\",\n  \"conduct_interview_page\": \"conductInterview-module-scss-module__yztraq__conduct_interview_page\",\n  \"current\": \"conductInterview-module-scss-module__yztraq__current\",\n  \"question_info_box\": \"conductInterview-module-scss-module__yztraq__question_info_box\",\n  \"summary_card_height\": \"conductInterview-module-scss-module__yztraq__summary_card_height\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1424, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/conductInterview/CandidateProfile.tsx"], "sourcesContent": ["import React, { use, useEffect, useMemo, useState } from \"react\";\r\n\r\nimport { Bar, Radar } from \"react-chartjs-2\";\r\nimport {\r\n  Chart as ChartJS,\r\n  RadialLinearScale,\r\n  CategoryScale,\r\n  PointElement,\r\n  LinearScale,\r\n  BarElement,\r\n  LineElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n} from \"chart.js\";\r\nimport { buildStyles, CircularProgressbar } from \"react-circular-progressbar\";\r\nimport Image from \"next/image\";\r\nimport Avatar from \"react-avatar\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { useTranslations } from \"use-intl\";\r\n\r\nimport AiMarkIcon from \"@/components/svgComponents/AiMarkIcon\";\r\nimport AIVerifiedIcon from \"@/components/svgComponents/AIVerifiedIcon\";\r\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\r\nimport CheckSecondaryIcon from \"@/components/svgComponents/CheckSecondaryIcon\";\r\nimport PreviewResumeIcon from \"@/components/svgComponents/PreviewResumeIcon\";\r\nimport StarIcon from \"@/components/svgComponents/StarIcon\";\r\nimport Button from \"@/components/formElements/Button\";\r\nimport Loader from \"@/components/loader/Loader\";\r\nimport ResumeModal from \"@/components/commonModals/ResumeModal\";\r\nimport ConfirmationModal from \"@/components/commonModals/ConfirmationModal\";\r\n\r\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\r\nimport type {\r\n  BarChartData,\r\n  CandidateProfileProps,\r\n  CandidateProfileResponse,\r\n  ICandidateInterviewHistory,\r\n  IFinalAssessment,\r\n  ISkillSpecificAssessment,\r\n  RadarChartData,\r\n} from \"@/interfaces/candidatesInterface\";\r\nimport { AuthState } from \"@/redux/slices/authSlice\";\r\nimport {\r\n  fetchCandidateProfile,\r\n  getApplicationFinalSummary,\r\n\r\n  getCandidateInterviewHistory,\r\n  updateJobApplicationStatus,\r\n  generateFinalSummary,\r\n} from \"@/services/CandidatesServices/candidatesApplicationServices\";\r\nimport { toastMessageError, toastMessageSuccess, toTitleCase } from \"@/utils/helper\";\r\n\r\nimport noImageFound from \"../../../../public/assets/images/noImageFound.svg\";\r\n\r\nimport \"react-circular-progressbar/dist/styles.css\";\r\nimport FinalAssessmentIcon from \"@/components/svgComponents/FinalAssessmentIcon\";\r\nimport { createFinalAssessment, getAssessmentStatus } from \"@/services/assessmentService\";\r\nimport ROUTES from \"@/constants/routes\";\r\nimport QuestionGeneratorLoader from \"@/components/loader/QuestionGeneratorLoader\";\r\nimport FinalAssessmentConfirmModal from \"@/components/commonModals/FinalAssessmentConfirmModal\";\r\nimport Skeleton from \"react-loading-skeleton\";\r\nimport style from \"../../../styles/conductInterview.module.scss\";\r\nimport \"react-loading-skeleton/dist/skeleton.css\";\r\n// ============================================================================\r\n// CANDIDATE PROFILE COMPONENT\r\n// ============================================================================\r\n\r\n/**\r\n * @fileoverview CandidateProfile Component\r\n *\r\n * A comprehensive React component for displaying detailed candidate information\r\n * during the interview process. This component provides a complete view of\r\n * candidate data including profile information, skill assessments, interview\r\n * history, and hiring/rejection functionality.\r\n *\r\n *\r\n * Key Features:\r\n * - Tabbed interface for skill assessment and interview history\r\n * - Interactive charts for skill visualization (Bar and Radar charts)\r\n * - Real-time data loading with proper loading states\r\n * - Hire/Reject candidate functionality with status updates\r\n * - AI-powered analysis and recommendations display\r\n * - Responsive design with Bootstrap grid system\r\n * - Comprehensive error handling and user feedback\r\n *\r\n * Data Sources:\r\n * - Candidate profile information\r\n * - Skill-specific assessment data\r\n * - Final assessment summary\r\n * - Interview history across all rounds\r\n * - AI interviewer performance analysis\r\n *\r\n * Dependencies:\r\n * - Chart.js for data visualization\r\n * - Next.js for routing and image optimization\r\n * - Redux for state management\r\n * - React Intl for internationalization\r\n *\r\n * Performance Considerations:\r\n * - Memoized computed values to prevent unnecessary re-renders\r\n * - Lazy loading of tab-specific data\r\n * - Optimized API calls with loading state management\r\n * - Efficient chart data generation\r\n */\r\n\r\n// ============================================================================\r\n// MAIN COMPONENT\r\n// ============================================================================\r\n\r\nexport interface IAssessmentStatus {\r\n  exists: boolean;\r\n  isAssessmentShared: boolean;\r\n  isAssessmentSubmitted: boolean;\r\n  assessmentId: number | null;\r\n}\r\nconst BUTTON_STATES = {\r\n  IDLE: \"idle\",\r\n  LOADING: \"loading\",\r\n  GENERATING: \"generating\",\r\n};\r\n\r\n/**\r\n * CandidateProfile Component\r\n *\r\n * A comprehensive candidate profile view that displays candidate information,\r\n * skill assessments, interview history, and provides hiring/rejection functionality.\r\n *\r\n * Features:\r\n * - Tabbed interface for skill assessment and interview history\r\n * - Interactive charts for skill visualization\r\n * - Real-time data loading with loading states\r\n * - Hire/Reject candidate functionality\r\n * - AI-powered analysis and recommendations\r\n *\r\n * @param props - Component props containing candidate ID parameter\r\n * @returns JSX.Element - The rendered candidate profile component\r\n */\r\nconst CandidateProfile: React.FC<CandidateProfileProps> = ({ params }) => {\r\n  // ============================================================================\r\n  // HOOKS AND EXTERNAL DATA\r\n  // ============================================================================\r\n\r\n  const router = useRouter();\r\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\r\n  const t = useTranslations();\r\n\r\n  // Extract candidate ID from params\r\n  const paramsPromise = use(params);\r\n  const jobApplicationId = paramsPromise.jobApplicationId;\r\n\r\n  // ============================================================================\r\n  // STATE DECLARATIONS\r\n  // ============================================================================\r\n\r\n  // UI State - Controls tab selection and active skill display\r\n  /** Controls which main tab is active (true = Skill Assessment, false = Interview History) */\r\n  const [selectedTab, setSelectedTab] = useState<boolean>(false);\r\n  /** Currently selected skill tab for detailed view */\r\n  const [activeSkillTab, setActiveSkillTab] = useState<string>(\"\");\r\n  /** Animation value for circular progress bar (0-100) */\r\n  const [animationValue, setAnimationValue] = useState<number>(0);\r\n\r\n  // Data State - Stores fetched candidate information\r\n  /** Main candidate profile data from API */\r\n  const [candidateProfileData, setCandidateProfileData] = useState<CandidateProfileResponse | null>(null);\r\n  /** Candidate's interview history across all rounds */\r\n  const [candidateInterviewHistory, setCandidateInterviewHistory] = useState<ICandidateInterviewHistory[]>([]);\r\n  /** Detailed skill-specific assessment data */\r\n  const [skillSpecificAssessment, setSkillSpecificAssessment] = useState<ISkillSpecificAssessment | null>(null);\r\n  /** Final assessment summary and recommendations */\r\n  const [finalAssessment, setFinalAssessment] = useState<IFinalAssessment | null>(null);\r\n\r\n  // Loading State - Tracks API call progress\r\n  /** Loading state for skill assessment API call */\r\n  const [isLoadingSkillAssessment, setIsLoadingSkillAssessment] = useState<boolean>(false);\r\n  /** Loading state for final assessment API call */\r\n  const [isLoadingFinalAssessment, setIsLoadingFinalAssessment] = useState<boolean>(false);\r\n  /** Loading state for interview history API call */\r\n  const [isLoadingInterviewHistory, setIsLoadingInterviewHistory] = useState<boolean>(false);\r\n  /** Processing state for hire/reject actions */\r\n  const [isProcessing, setIsProcessing] = useState<boolean>(false);\r\n  /** Loading state for generate final summary action */\r\n  const [isGeneratingFinalSummary, setIsGeneratingFinalSummary] = useState<boolean>(false);\r\n  /** Flag to track if user has generated final summary */\r\n  const [hasFinalSummaryBeenGenerated, setHasFinalSummaryBeenGenerated] = useState<boolean>(false);\r\n\r\n  // Data Loaded Flags - Prevents unnecessary API calls\r\n  /** Flag indicating if skill assessment data has been loaded */\r\n  const [skillAssessmentLoaded, setSkillAssessmentLoaded] = useState<boolean>(false);\r\n  /** Flag indicating if final assessment data has been loaded */\r\n  const [finalAssessmentLoaded, setFinalAssessmentLoaded] = useState<boolean>(false);\r\n  /** Flag indicating if interview history data has been loaded */\r\n  const [interviewHistoryLoaded, setInterviewHistoryLoaded] = useState<boolean>(false);\r\n  /** Flag incdicating if candidate profile data has been loaded */\r\n  const [candidateProfileLoaded, setCandidateProfileLoaded] = useState<boolean>(false);\r\n\r\n  type ButtonState = (typeof BUTTON_STATES)[keyof typeof BUTTON_STATES];\r\n  // const searchParams = useSearchParams() || new URLSearchParams();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [buttonState, setButtonState] = useState<ButtonState>(BUTTON_STATES.IDLE);\r\n  const [assessmentStatus, setAssessmentStatus] = useState<IAssessmentStatus>();\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n\r\n  // Confirmation modal states for different actions\r\n  const [confirmationModal, setConfirmationModal] = useState({\r\n    isOpen: false,\r\n    action: \"\",\r\n    title: \"\",\r\n    message: \"\",\r\n    confirmButtonText: \"\",\r\n    onConfirm: () => {},\r\n    loading: false,\r\n  });\r\n\r\n  console.log(skillAssessmentLoaded, finalAssessmentLoaded, interviewHistoryLoaded);\r\n\r\n  const [resumeModal, setResumeModal] = useState<{ isOpen: boolean; resumeLink: string | null }>({ isOpen: false, resumeLink: null });\r\n\r\n  // const showFinalAssessmentConfirmation = () => {\r\n  //   if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {\r\n  //     toastMessageError(t(\"job_id_and_job_application_id_are_required\"));\r\n  //     return;\r\n  //   }\r\n  //   setShowConfirmModal(true);\r\n  // };\r\n\r\n  // Helper functions to show confirmation modals for different actions\r\n  const showCreateFinalAssessmentConfirmation = () => {\r\n    setConfirmationModal({\r\n      isOpen: true,\r\n      action: \"createFinalAssessment\",\r\n      title: t(\"create_final_assessment\"),\r\n      message: t(\"are_you_sure_you_want_to_create_final_assessment\"),\r\n      confirmButtonText: t(\"create\"),\r\n      onConfirm: handleCreateFinalAssessmentConfirmed,\r\n      loading: false,\r\n    });\r\n  };\r\n\r\n  const showGenerateFinalSummaryConfirmation = () => {\r\n    setConfirmationModal({\r\n      isOpen: true,\r\n      action: \"generateFinalSummary\",\r\n      title: t(\"generate_final_summary\"),\r\n      message:\r\n        \"Once the final summary is generated for this candidate, they will no longer be eligible to participate in any additional interview rounds. If any interview round remains incomplete, please ensure that it is conducted before proceeding with the final summary for this candidate.\",\r\n      confirmButtonText: t(\"generate\"),\r\n      onConfirm: handleGenerateFinalSummaryConfirmed,\r\n      loading: false,\r\n    });\r\n  };\r\n\r\n  const showHireConfirmation = () => {\r\n    setConfirmationModal({\r\n      isOpen: true,\r\n      action: \"hire\",\r\n      title: t(\"hire_candidate\"),\r\n      message: t(\"are_you_sure_you_want_to_hire_this_candidate\"),\r\n      confirmButtonText: t(\"hire\"),\r\n      onConfirm: () => handleHireRejectCandidateConfirmed(APPLICATION_STATUS.HIRED),\r\n      loading: false,\r\n    });\r\n  };\r\n\r\n  const showRejectConfirmation = () => {\r\n    setConfirmationModal({\r\n      isOpen: true,\r\n      action: \"reject\",\r\n      title: t(\"reject_candidate\"),\r\n      message: t(\"are_you_sure_you_want_to_reject_this_candidate\"),\r\n      confirmButtonText: t(\"reject\"),\r\n      onConfirm: () => handleHireRejectCandidateConfirmed(APPLICATION_STATUS.FINAL_REJECT),\r\n      loading: false,\r\n    });\r\n  };\r\n\r\n  const closeConfirmationModal = () => {\r\n    setConfirmationModal({\r\n      isOpen: false,\r\n      action: \"\",\r\n      title: \"\",\r\n      message: \"\",\r\n      confirmButtonText: \"\",\r\n      onConfirm: () => {},\r\n      loading: false,\r\n    });\r\n  };\r\n\r\n  /**\r\n   * Handle creating final assessment (confirmed action)\r\n   */\r\n  const handleCreateFinalAssessmentConfirmed = async () => {\r\n    if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {\r\n      toastMessageError(t(\"job_id_and_job_application_id_are_required\"));\r\n      closeConfirmationModal();\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Update modal loading state\r\n      setConfirmationModal((prev) => ({ ...prev, loading: true }));\r\n      setIsLoading(true);\r\n      setButtonState(BUTTON_STATES.GENERATING);\r\n\r\n      const response = await createFinalAssessment({ jobId: candidateProfileData?.jobId, jobApplicationId: candidateProfileData?.jobApplicationId });\r\n      if (response && response.data && response.data.success) {\r\n        toastMessageSuccess(t(response?.data?.message || \"final_assessment_created_successfully\"));\r\n\r\n        // Get the finalAssessmentId from the response data\r\n        const finalAssessmentId = response.data.data?.assessmentId;\r\n\r\n        if (finalAssessmentId) {\r\n          // Redirect to the final assessment page with the finalAssessmentId and default status values\r\n          // For a newly created assessment, both isShared and isSubmitted will be false\r\n          router.push(\r\n            `${ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${finalAssessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`\r\n          );\r\n        }\r\n      } else {\r\n        toastMessageError(t(response?.data?.message || \"failed_to_create_final_assessment\"));\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error creating final assessment:\", error);\r\n      toastMessageError(t(\"an_error_occurred_while_creating_the_final_assessment\"));\r\n    } finally {\r\n      setIsLoading(false);\r\n      setButtonState(BUTTON_STATES.IDLE);\r\n      closeConfirmationModal();\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle creating final assessment (original function for backward compatibility)\r\n   */\r\n  const handleCreateFinalAssessment = async () => {\r\n    if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {\r\n      toastMessageError(t(\"job_id_and_job_application_id_are_required\"));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      setButtonState(BUTTON_STATES.GENERATING);\r\n      setShowConfirmModal(false);\r\n      const response = await createFinalAssessment({ jobId: candidateProfileData?.jobId, jobApplicationId: candidateProfileData?.jobApplicationId });\r\n      if (response && response.data && response.data.success) {\r\n        toastMessageSuccess(t(response?.data?.message || \"final_assessment_created_successfully\"));\r\n\r\n        // Get the finalAssessmentId from the response data\r\n        const finalAssessmentId = response.data.data?.assessmentId;\r\n\r\n        if (finalAssessmentId) {\r\n          // Redirect to the final assessment page with the finalAssessmentId and default status values\r\n          // For a newly created assessment, both isShared and isSubmitted will be false\r\n          router.push(\r\n            `${ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${finalAssessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`\r\n          );\r\n        }\r\n      } else {\r\n        toastMessageError(t(response?.data?.message || \"failed_to_create_final_assessment\"));\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error creating final assessment:\", error);\r\n      toastMessageError(t(\"an_error_occurred_while_creating_the_final_assessment\"));\r\n    } finally {\r\n      setIsLoading(false);\r\n      setButtonState(BUTTON_STATES.IDLE);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Close the confirmation modal\r\n   */\r\n  const handleCancelConfirmModal = () => {\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  // Effect to check assessment status when component mounts\r\n  useEffect(() => {\r\n    // Automatically call getAssessmentStatus when the component mounts\r\n    const checkStatus = async () => {\r\n      if (!jobApplicationId) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        const response = await getAssessmentStatus(jobApplicationId);\r\n\r\n        if (response?.data) {\r\n          const assessmentData = response?.data?.data;\r\n\r\n          // Update assessment status state\r\n          setAssessmentStatus({\r\n            exists: !!assessmentData.assessmentId, //exists: assessmentData.id ? true : false\r\n            isAssessmentShared: assessmentData.isAssessmentShared || false,\r\n            isAssessmentSubmitted: assessmentData.isAssessmentSubmitted || false,\r\n            assessmentId: assessmentData.assessmentId || null,\r\n          });\r\n        } else {\r\n          toastMessageError(t(response?.data?.message || \"failed_to_get_assessment_status\"));\r\n        }\r\n      } catch (error) {\r\n        console.error(error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    checkStatus();\r\n  }, [t]);\r\n  // ============================================================================\r\n  // CHART CONFIGURATION\r\n  // ============================================================================\r\n\r\n  // Register Chart.js components\r\n  ChartJS.register(RadialLinearScale, CategoryScale, LinearScale, BarElement, PointElement, LineElement, Title, Tooltip, Legend);\r\n\r\n  // ============================================================================\r\n  // UTILITY FUNCTIONS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Generates bar chart data for skill visualization\r\n   * @returns BarChartData object configured for skill scores display\r\n   */\r\n  const generateBarChartData = (): BarChartData => {\r\n    const labels = skillSpecificAssessment?.skillsScores.map((item) => item.skill_name) || [];\r\n    const values = skillSpecificAssessment?.skillsScores.map((item) => item.skill_marks * 10) || [];\r\n\r\n    return {\r\n      labels,\r\n      datasets: [\r\n        {\r\n          data: values,\r\n          backgroundColor: labels.map((label, index) => {\r\n            const skillScore = values[index];\r\n            // Color changes only for perfect scores (100/100)\r\n            return skillScore === 100 ? \"#ffc107\" : \"rgba(119,167,255,0.8)\";\r\n          }),\r\n          borderRadius: 8,\r\n          borderSkipped: false,\r\n          barPercentage: 0.5,\r\n        },\r\n      ],\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Generates radar chart data for behavioral assessment visualization\r\n   * @returns RadarChartData object configured for behavioral metrics\r\n   */\r\n  const generateRadarChartData = (): RadarChartData => {\r\n    return {\r\n      labels: [\"Body Language\", \"Affability\", \"Posture\", \"Eye-Contact\", \"Confidence\"], // Dynamic portion\r\n      datasets: [\r\n        {\r\n          label: \"Behavioral Assessment\",\r\n          data: [28, 48, 40, 19, 96],\r\n          fill: true,\r\n          backgroundColor: \"rgba(49, 65, 75, 0.2)\",\r\n          borderColor: \"rgb(54, 162, 235)\",\r\n          pointBackgroundColor: \"rgb(54, 162, 235)\",\r\n          pointBorderColor: \"#fff\",\r\n          pointHoverBackgroundColor: \"#fff\",\r\n          pointHoverBorderColor: \"rgb(54, 162, 235)\",\r\n        },\r\n      ],\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Calculates the number of filled bars for skill success probability visualization\r\n   * @param probability - Success probability percentage (0-100)\r\n   * @returns Number of bars to fill (0-10)\r\n   */\r\n  const calculateFilledBars = (probability: number): number => {\r\n    return Math.round(probability / 10);\r\n  };\r\n\r\n  // ============================================================================\r\n  // COMPUTED VALUES\r\n  // ============================================================================\r\n\r\n  /** Chart data for bar chart visualization */\r\n  const barChartData = generateBarChartData();\r\n\r\n  /** Chart data for radar chart visualization */\r\n  const radarChartData = generateRadarChartData();\r\n\r\n  // ============================================================================\r\n  // EVENT HANDLERS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Handles switching to Interview History view\r\n   * Loads interview history data if not already loaded\r\n   *\r\n   * @async\r\n   * @function handleInterviewHistoryClick\r\n   * @returns {Promise<void>}\r\n   */\r\n  const handleInterviewHistoryClick = async (): Promise<void> => {\r\n    setSelectedTab(false);\r\n\r\n    // Load interview history data if not already loaded\r\n\r\n    await loadCandidateInterviewHistory();\r\n  };\r\n\r\n  /**\r\n   * Handles switching to Skill Specific Assessment view\r\n   * Only works if final summary has been generated\r\n   *\r\n   * @async\r\n   * @function handleSkillAssessmentClick\r\n   * @returns {Promise<void>}\r\n   */\r\n  const handleSkillAssessmentClick = async (): Promise<void> => {\r\n    if (!hasFinalSummaryBeenGenerated) return;\r\n\r\n    setSelectedTab(true);\r\n\r\n    // Load skill assessment data if not already loaded\r\n    if (!skillAssessmentLoaded || !finalAssessmentLoaded) {\r\n      await Promise.all([loadFinalAssessment(), loadSkillSpecificAssessment()]);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handles hire or reject candidate action (confirmed action)\r\n   * Updates the job application status and refreshes all data\r\n   *\r\n   * @async\r\n   * @function handleHireRejectCandidateConfirmed\r\n   * @param {string} status - The new application status (HIRED or FINAL_REJECT)\r\n   * @returns {Promise<void>}\r\n   */\r\n  const handleHireRejectCandidateConfirmed = async (status: string): Promise<void> => {\r\n    if (!candidateProfileData) {\r\n      closeConfirmationModal();\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Update modal loading state\r\n      setConfirmationModal((prev) => ({ ...prev, loading: true }));\r\n      setIsProcessing(true);\r\n\r\n      const response = await updateJobApplicationStatus(Number(candidateProfileData.jobApplicationId), status);\r\n\r\n      if (response && response.data && response.data.success) {\r\n        toastMessageSuccess(t(response.data.message));\r\n        await refreshAllData();\r\n      } else {\r\n        toastMessageError(t(response?.data?.message));\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating job application status:\", error);\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n    } finally {\r\n      setIsProcessing(false);\r\n      closeConfirmationModal();\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handles hire or reject candidate action (original function for backward compatibility)\r\n   * Updates the job application status and refreshes all data\r\n   *\r\n   * @async\r\n   * @function handleHireRejectCandidate\r\n   * @param {string} status - The new application status (HIRED or FINAL_REJECT)\r\n   * @returns {Promise<void>}\r\n   *\r\n   * Side effects:\r\n   * - Updates isProcessing state during operation\r\n   * - Shows success/error toast messages\r\n   * - Refreshes all candidate data on success\r\n   *\r\n   * @example\r\n   * // Hire the candidate\r\n   * handleHireRejectCandidate(APPLICATION_STATUS.HIRED);\r\n   *\r\n   * // Reject the candidate\r\n   * handleHireRejectCandidate(APPLICATION_STATUS.FINAL_REJECT);\r\n   */\r\n  // const handleHireRejectCandidate = async (status: string): Promise<void> => {\r\n  //   if (!candidateProfileData) return;\r\n\r\n  //   setIsProcessing(true);\r\n  //   try {\r\n  //     const response = await updateJobApplicationStatus(Number(candidateProfileData.jobApplicationId), status);\r\n\r\n  //     if (response && response.data && response.data.success) {\r\n  //       toastMessageSuccess(t(response.data.message));\r\n  //       await refreshAllData();\r\n  //     } else {\r\n  //       toastMessageError(t(response?.data?.message));\r\n  //     }\r\n  //   } catch (error) {\r\n  //     console.error(\"Error updating job application status:\", error);\r\n  //     toastMessageError(t(\"something_went_wrong\"));\r\n  //   } finally {\r\n  //     setIsProcessing(false);\r\n  //   }\r\n  // };\r\n\r\n  // ============================================================================\r\n  // API FUNCTIONS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Loads candidate interview history from the API\r\n   * Prevents multiple simultaneous calls using loading state\r\n   *\r\n   * @async\r\n   * @function loadCandidateInterviewHistory\r\n   * @returns {Promise<void>}\r\n   *\r\n   * Side effects:\r\n   * - Updates isLoadingInterviewHistory state\r\n   * - Updates candidateInterviewHistory state on success\r\n   * - Updates interviewHistoryLoaded flag\r\n   * - Shows error toast on failure\r\n   */\r\n  const loadCandidateInterviewHistory = async (): Promise<void> => {\r\n    if (isLoadingInterviewHistory) return;\r\n\r\n    setIsLoadingInterviewHistory(true);\r\n    try {\r\n      const response = await getCandidateInterviewHistory(jobApplicationId);\r\n      if (response?.data?.success) {\r\n        setCandidateInterviewHistory(response.data.data);\r\n        setInterviewHistoryLoaded(true);\r\n      } else {\r\n        toastMessageError(t(response?.data?.message));\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading interview history:\", error);\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n    } finally {\r\n      setIsLoadingInterviewHistory(false);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Loads final assessment data from the API\r\n   * Prevents multiple simultaneous calls using loading state\r\n   *\r\n   * @async\r\n   * @function loadFinalAssessment\r\n   * @returns {Promise<void>}\r\n   *\r\n   * Side effects:\r\n   * - Updates isLoadingFinalAssessment state\r\n   * - Updates finalAssessment state on success\r\n   * - Updates finalAssessmentLoaded flag\r\n   * - Shows error toast on failure\r\n   */\r\n  const loadFinalAssessment = async (): Promise<void> => {\r\n    if (isLoadingFinalAssessment) return;\r\n\r\n    setIsLoadingFinalAssessment(true);\r\n    try {\r\n      const response = await getApplicationFinalSummary(jobApplicationId);\r\n      if (response?.data?.success) {\r\n        setFinalAssessment(response.data.data);\r\n        setFinalAssessmentLoaded(true);\r\n      } else {\r\n        toastMessageError(t(response?.data?.message));\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading final assessment:\", error);\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n    } finally {\r\n      setIsLoadingFinalAssessment(false);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Loads skill-specific assessment data from the API\r\n   * Prevents multiple simultaneous calls using loading state\r\n   *\r\n   * @async\r\n   * @function loadSkillSpecificAssessment\r\n   * @returns {Promise<void>}\r\n   *\r\n   * Side effects:\r\n   * - Updates isLoadingSkillAssessment state\r\n   * - Updates skillSpecificAssessment state on success\r\n   * - Updates skillAssessmentLoaded flag\r\n   * - Shows error toast on failure\r\n   */\r\n  const loadSkillSpecificAssessment = async (): Promise<void> => {\r\n    if (isLoadingSkillAssessment) return;\r\n    setSkillAssessmentLoaded(true);\r\n\r\n    setIsLoadingSkillAssessment(true);\r\n    try {\r\n      const response = await getApplicationSkillScoreData(jobApplicationId);\r\n      if (response?.data?.success) {\r\n        setSkillSpecificAssessment(response.data.data);\r\n      } else {\r\n        toastMessageError(t(response?.data?.message));\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading skill assessment:\", error);\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n    } finally {\r\n      setIsLoadingSkillAssessment(false);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Loads candidate profile data from the API\r\n   *\r\n   * @async\r\n   * @function loadCandidateProfile\r\n   * @returns {Promise<void>}\r\n   *\r\n   * Side effects:\r\n   * - Updates candidateProfileData state on success\r\n   * - Shows error toast on failure\r\n   */\r\n  const loadCandidateProfile = async (): Promise<void> => {\r\n    setCandidateProfileLoaded(true);\r\n\r\n    try {\r\n      const response = await fetchCandidateProfile(jobApplicationId);\r\n      if (response?.data?.success) {\r\n        setCandidateProfileData(response.data.data);\r\n        setCandidateProfileLoaded(false);\r\n      } else {\r\n        toastMessageError(t(response?.data?.message));\r\n        setCandidateProfileLoaded(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading candidate profile:\", error);\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n      setCandidateProfileLoaded(false);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Refreshes all candidate data by resetting loaded flags and reloading data\r\n   * Used after status updates to ensure data consistency\r\n   *\r\n   * @async\r\n   * @function refreshAllData\r\n   * @returns {Promise<void>}\r\n   *\r\n   * Side effects:\r\n   * - Resets all data loaded flags\r\n   * - Reloads candidate profile\r\n   * - Reloads tab-specific data based on current tab\r\n   */\r\n  const refreshAllData = async (): Promise<void> => {\r\n    // Reset loaded flags to force refresh\r\n    setSkillAssessmentLoaded(false);\r\n    setFinalAssessmentLoaded(false);\r\n    setInterviewHistoryLoaded(false);\r\n\r\n    // Refresh candidate profile\r\n    await loadCandidateProfile();\r\n\r\n    // Refresh assessment data based on current tab\r\n    if (selectedTab) {\r\n      await Promise.all([loadSkillSpecificAssessment(), loadFinalAssessment()]);\r\n    } else {\r\n      await loadCandidateInterviewHistory();\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handles generating final summary for the candidate (confirmed action)\r\n   * Triggers API call to generate comprehensive final summary based on interview data\r\n   *\r\n   * @async\r\n   * @function handleGenerateFinalSummaryConfirmed\r\n   * @returns {Promise<void>}\r\n   */\r\n  const handleGenerateFinalSummaryConfirmed = async (): Promise<void> => {\r\n    if (!candidateProfileData?.jobApplicationId || isGeneratingFinalSummary) {\r\n      closeConfirmationModal();\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Update modal loading state\r\n      setConfirmationModal((prev) => ({ ...prev, loading: true }));\r\n      setIsGeneratingFinalSummary(true);\r\n\r\n      const response = await generateFinalSummary(candidateProfileData.jobApplicationId);\r\n      if (response?.data?.success) {\r\n        toastMessageSuccess(t(response?.data?.message || \"final_summary_generated_successfully\"));\r\n        // Set flag to indicate final summary has been generated by user\r\n        setHasFinalSummaryBeenGenerated(true);\r\n        // Switch to Skill Specific Assessment tab automatically\r\n        setSelectedTab(true);\r\n        // Load skill-specific assessment data\r\n        await Promise.all([loadFinalAssessment(), loadSkillSpecificAssessment()]);\r\n      } else {\r\n        toastMessageError(t(response?.data?.message || \"failed_to_generate_final_summary\"));\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error generating final summary:\", error);\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n    } finally {\r\n      setIsGeneratingFinalSummary(false);\r\n      closeConfirmationModal();\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handles generating final summary for the candidate (original function for backward compatibility)\r\n   * Triggers API call to generate comprehensive final summary based on interview data\r\n   *\r\n   * @async\r\n   * @function handleGenerateFinalSummary\r\n   * @returns {Promise<void>}\r\n   *\r\n   * Side effects:\r\n   * - Updates isGeneratingFinalSummary state\r\n   * - Shows success/error toast messages\r\n   * - Refreshes final assessment data on success\r\n   */\r\n  // const handleGenerateFinalSummary = async (): Promise<void> => {\r\n  //   if (!candidateProfileData?.jobApplicationId || isGeneratingFinalSummary) return;\r\n\r\n  //   setIsGeneratingFinalSummary(true);\r\n  //   try {\r\n  //     const response = await generateFinalSummary(candidateId, candidateProfileData.jobApplicationId);\r\n  //     if (response?.data?.success) {\r\n  //       toastMessageSuccess(t(response?.data?.message || \"final_summary_generated_successfully\"));\r\n  //       // Set flag to indicate final summary has been generated by user\r\n  //       setHasFinalSummaryBeenGenerated(true);\r\n  //       // Switch to Skill Specific Assessment tab automatically\r\n  //       setSelectedTab(true);\r\n  //       // Load skill-specific assessment data\r\n  //       await Promise.all([loadFinalAssessment(), loadSkillSpecificAssessment()]);\r\n  //     } else {\r\n  //       toastMessageError(t(response?.data?.message || \"failed_to_generate_final_summary\"));\r\n  //     }\r\n  //   } catch (error) {\r\n  //     console.error(\"Error generating final summary:\", error);\r\n  //     toastMessageError(t(\"something_went_wrong\"));\r\n  //   } finally {\r\n  //     setIsGeneratingFinalSummary(false);\r\n  //   }\r\n  // };\r\n\r\n  // ============================================================================\r\n  // COMPUTED VALUES AND MEMOIZED DATA\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Memoized AI interviewer analysis for the current user\r\n   * Finds analysis data where the interviewer ID matches the current user\r\n   */\r\n  const aiInterviewerAnalysis = useMemo(\r\n    () =>\r\n      candidateInterviewHistory?.find((record) => record.interviewerId === authData?.id && record.interviewerPerformanceAiAnalysis)\r\n        ?.interviewerPerformanceAiAnalysis,\r\n    [candidateInterviewHistory, authData?.id]\r\n  );\r\n\r\n  /**\r\n   * Memoized all interviewer analysis data for admin users\r\n   * Returns all interviewer performance AI analysis data for admin users\r\n   */\r\n  const allInterviewerAnalysis = useMemo(() => {\r\n    if (!candidateInterviewHistory || !authData || authData.account_type !== \"admin\") return [];\r\n\r\n    return candidateInterviewHistory\r\n      .filter((record) => record.interviewerPerformanceAiAnalysis)\r\n      .map((record) => ({\r\n        interviewerId: record.interviewerId,\r\n        interviewerName: record.interviewerName,\r\n        interviewerImage: record.interviewerImage,\r\n        roundNumber: record.roundNumber,\r\n        analysis: record.interviewerPerformanceAiAnalysis,\r\n      }));\r\n  }, [candidateInterviewHistory, authData]);\r\n\r\n  // ============================================================================\r\n  // EFFECTS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Initial data loading effect\r\n   * Loads candidate profile and interview history data in parallel when component mounts or candidateId changes\r\n   */\r\n  useEffect(() => {\r\n    const initializeData = async () => {\r\n      // Set loading states immediately to prevent \"No data\" messages\r\n      setCandidateProfileLoaded(true);\r\n      setIsLoadingInterviewHistory(true);\r\n\r\n      // Load candidate profile and interview history data in parallel\r\n      await Promise.all([loadCandidateProfile(), loadCandidateInterviewHistory()]);\r\n    };\r\n\r\n    initializeData();\r\n  }, [jobApplicationId]);\r\n\r\n  /**\r\n   * Effect to set the first skill as active when skill assessment data is loaded\r\n   * Ensures there's always an active skill tab when data is available\r\n   */\r\n  useEffect(() => {\r\n    if (skillSpecificAssessment?.skillsScores && skillSpecificAssessment.skillsScores.length > 0 && !activeSkillTab) {\r\n      setActiveSkillTab(skillSpecificAssessment.skillsScores[0].skill_name);\r\n    }\r\n  }, [skillSpecificAssessment, activeSkillTab]);\r\n\r\n  /**\r\n   * Effect to animate the circular progress bar\r\n   * Creates a smooth animation from 0 to the final success probability value\r\n   */\r\n  useEffect(() => {\r\n    const timer = requestAnimationFrame(() => setAnimationValue(finalAssessment?.overallSuccessProbability || 0));\r\n    return () => cancelAnimationFrame(timer);\r\n  }, [finalAssessment?.overallSuccessProbability]);\r\n\r\n  // ============================================================================\r\n  // RENDER LOGIC\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Renders the skill details section for the selected skill\r\n   * Shows strengths, potential gaps, and success probability\r\n   */\r\n  const renderSkillDetails = () => {\r\n    if (!skillSpecificAssessment?.skillsScores || !activeSkillTab) return null;\r\n\r\n    const selectedSkill = skillSpecificAssessment.skillsScores.find((skill) => skill.skill_name === activeSkillTab);\r\n\r\n    if (!selectedSkill) return null;\r\n\r\n    const probability = selectedSkill.probability_of_success_in_this_skill?.probabilityOfSuccessInSkill || 0;\r\n    const filledBars = calculateFilledBars(probability);\r\n\r\n    return (\r\n      <div className=\"row\">\r\n        <div className=\"col-md-7\">\r\n          <h4 className=\"skill-sub-title\">Strengths</h4>\r\n          <ul className=\"strengths\">\r\n            {selectedSkill.strengths?.strengths?.map((strength, index) => (\r\n              <li key={index} className=\"strength-item\">\r\n                {strength}\r\n              </li>\r\n            ))}\r\n          </ul>\r\n          <h4 className=\"skill-sub-title\">Potential Gaps</h4>\r\n          <ul className=\"strengths\">\r\n            {selectedSkill.potentials_gaps?.potentialGaps?.map((gap, index) => (\r\n              <li key={index} className=\"strength-item\">\r\n                {gap}\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n        <div className=\"col-md-5\">\r\n          <div className=\"probability-card\">\r\n            <h4 className=\"skill-sub-title\">Skilssssl Success Probability</h4>\r\n            <div className=\"progress-container\">\r\n              <h3 className=\"ms-2 fw-bold\">{probability}%</h3>\r\n              <div className=\"probability-bar\">\r\n                {Array.from({ length: 10 }, (_, index) => (\r\n                  <div key={index} className={`bar ${index < filledBars ? \"filled\" : \"\"}`} />\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <QuestionGeneratorLoader show={buttonState === BUTTON_STATES.GENERATING} />\r\n      {showConfirmModal && (\r\n        <FinalAssessmentConfirmModal onClickCancel={handleCancelConfirmModal} onClickGenerate={handleCreateFinalAssessment} disabled={isLoading} />\r\n      )}\r\n\r\n      {/* Confirmation Modal for various actions */}\r\n      <ConfirmationModal\r\n        isOpen={confirmationModal.isOpen}\r\n        onClose={closeConfirmationModal}\r\n        onConfirm={confirmationModal.onConfirm}\r\n        title={confirmationModal.title}\r\n        message={confirmationModal.message}\r\n        confirmButtonText={confirmationModal.confirmButtonText}\r\n        loading={confirmationModal.loading}\r\n        loadingText={t(\"processing\")}\r\n      />\r\n\r\n      <div className={style.conduct_interview_page}>\r\n        <div className=\"container\">\r\n          <div className=\"common-page-header\">\r\n            <div className=\"common-page-head-section\">\r\n              <div className=\"main-heading\">\r\n                <h2>\r\n                  <Button className=\"clear-btn p-0 m-0\">\r\n                    <BackArrowIcon\r\n                      onClick={() => {\r\n                        router.back();\r\n                      }}\r\n                    />\r\n                  </Button>{\" \"}\r\n                  {t(\"candidate\")} <span>{t(\"profile\")} </span>\r\n                </h2>\r\n\r\n                <Button\r\n                  className=\"theme-btn clear-btn text-btn primary p-0 m-0\"\r\n                  onClick={() => {\r\n                    if (!isLoading) {\r\n                      if (!assessmentStatus?.exists) {\r\n                        showCreateFinalAssessmentConfirmation();\r\n                      } else if (assessmentStatus?.assessmentId) {\r\n                        // Redirect to the final assessment page with the existing assessment ID\r\n                        router.push(\r\n                          `${ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${assessmentStatus.assessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`\r\n                        );\r\n                      }\r\n                    }\r\n                  }}\r\n                  disabled={isLoading}\r\n                >\r\n                  {buttonState === \"loading\" || (isLoading && buttonState !== \"generating\") ? \"\" : <FinalAssessmentIcon />}\r\n                  {buttonState === \"loading\" || (isLoading && buttonState !== \"generating\") ? (\r\n                    <Skeleton width={100} height={30} borderRadius={12} />\r\n                  ) : buttonState === \"generating\" ? (\r\n                    t(\"generating_questions\")\r\n                  ) : !assessmentStatus?.exists ? (\r\n                    t(\"create_final_assessment\")\r\n                  ) : assessmentStatus.isAssessmentSubmitted ? (\r\n                    t(\"view_final_assessment_result\")\r\n                  ) : (\r\n                    t(\"view_final_assessment\")\r\n                  )}\r\n                </Button>\r\n\r\n                {/* Conditionally render resume preview button if resumeLink exists */}\r\n                {candidateProfileData && candidateProfileData.resumeLink && (\r\n                  <>\r\n                    <Button\r\n                      onClick={() => setResumeModal({ isOpen: true, resumeLink: candidateProfileData?.resumeLink })}\r\n                      className=\"theme-btn clear-btn text-btn primary p-0 m-0\"\r\n                    >\r\n                      <PreviewResumeIcon className=\"me-2\" />\r\n                      {t(\"preview_candidate_resume\")}\r\n                    </Button>\r\n                    <ResumeModal\r\n                      isOpen={resumeModal.isOpen}\r\n                      onClose={() => setResumeModal({ ...resumeModal, isOpen: false })}\r\n                      resumeLink={resumeModal.resumeLink}\r\n                    />\r\n                  </>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"inner-section profile-section\">\r\n            <div className=\"candidate-profile\">\r\n              {candidateProfileLoaded ? (\r\n                <Skeleton height={100} width={100} borderRadius={12} />\r\n              ) : (\r\n                <Avatar\r\n                  src={candidateProfileData?.imageUrl || undefined}\r\n                  name={candidateProfileData?.candidateName || \"\"}\r\n                  size=\"100\"\r\n                  round={true}\r\n                  className=\"candidate-image\"\r\n                />\r\n              )}\r\n              <div className=\"candidate-info\">\r\n                {/*  Use candidateName from API */}\r\n                <h3 className=\"candidate-name\">\r\n                  {candidateProfileLoaded ? <Skeleton width={150} height={25} /> : toTitleCase(candidateProfileData?.candidateName || \"-\")}\r\n                </h3>\r\n\r\n                <div className=\"info-container\">\r\n                  <div className=\"info-item\">\r\n                    <p className=\"info-title\">{t(\"post_applied_for\")}</p>\r\n                    {/* Use jobTitle from API */}\r\n                    <p className=\"info-value\">\r\n                      {candidateProfileLoaded ? <Skeleton width={100} height={25} /> : candidateProfileData?.jobTitle || \"-\"}\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"info-item\">\r\n                    <p className=\"info-title\">{t(\"department\")}</p>\r\n                    <p className=\"info-value\">\r\n                      {candidateProfileLoaded ? <Skeleton width={100} height={25} /> : candidateProfileData?.department || \"-\"}\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"info-item\">\r\n                    <p className=\"info-title\">{t(\"current_round\")}</p>\r\n                    <p className=\"info-value\">\r\n                      {candidateProfileLoaded ? <Skeleton width={20} height={18} /> : candidateProfileData?.roundNumber || 0}\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"info-item\">\r\n                    <p className=\"info-title\">{t(\"resume_approved_by\")}</p>\r\n                    <p className=\"info-value with-img\">\r\n                      {candidateProfileLoaded ? (\r\n                        <Skeleton width={20} height={20} />\r\n                      ) : (\r\n                        <Image src={candidateProfileData?.interviewerImage || noImageFound} alt=\"Interviewer avatar\" height={20} width={20} />\r\n                      )}\r\n                      {/*  Use interviewerName from API */}\r\n                      {candidateProfileLoaded ? <Skeleton width={100} height={25} /> : candidateProfileData?.interviewerName || \"-\"}\r\n                    </p>\r\n                  </div>\r\n                  {candidateProfileLoaded ? (\r\n                    <Skeleton height={40} width={100} borderRadius={12} />\r\n                  ) : candidateProfileData ? (\r\n                    <div className=\"button-align\">\r\n                      {/* Show hire/reject buttons only when user has generated final summary */}\r\n                      {hasFinalSummaryBeenGenerated && !isGeneratingFinalSummary && assessmentStatus?.exists ? (\r\n                        <>\r\n                          <Button\r\n                            className=\"primary-btn rounded-md minWidth\"\r\n                            onClick={showHireConfirmation}\r\n                            disabled={isProcessing || !candidateProfileData}\r\n                          >\r\n                            {isProcessing ? <Loader /> : t(\"hire\")}\r\n                          </Button>\r\n                          <Button\r\n                            className=\"dark-outline-btn rounded-md minWidth\"\r\n                            onClick={showRejectConfirmation}\r\n                            disabled={isProcessing || !candidateProfileData}\r\n                          >\r\n                            {isProcessing ? <Loader /> : t(\"reject\")}\r\n                          </Button>\r\n                        </>\r\n                      ) : (\r\n                        /* Show generate final summary button when user hasn't generated final summary yet */\r\n                        <Button\r\n                          className=\"primary-btn rounded-md minWidth\"\r\n                          onClick={showGenerateFinalSummaryConfirmation}\r\n                          disabled={isGeneratingFinalSummary || !candidateProfileData?.jobApplicationId}\r\n                        >\r\n                          {isGeneratingFinalSummary ? <Loader /> : t(\"generate_final_summary\")}\r\n                        </Button>\r\n                      )}\r\n                    </div>\r\n                  ) : (\r\n                    \"\"\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            {/* Interview History Button - Always visible */}\r\n            {!hasFinalSummaryBeenGenerated && (\r\n              <div className=\"mb-4\" onClick={handleInterviewHistoryClick}>\r\n                <h2>{t(\"interview_history\")}</h2>\r\n              </div>\r\n            )}\r\n\r\n            {/* Skill Specific Assessment Tab - Only visible after final summary generation */}\r\n            {hasFinalSummaryBeenGenerated && (\r\n              <div className=\"common-tab mb-5\">\r\n                <li className={selectedTab ? \"active\" : \"\"} onClick={handleSkillAssessmentClick}>\r\n                  {t(\"skill_specific_assessment\")}\r\n                </li>\r\n                <li className={!selectedTab ? \"active\" : \"\"} onClick={handleInterviewHistoryClick}>\r\n                  {t(\"interview_history\")}\r\n                </li>\r\n              </div>\r\n            )}\r\n            {selectedTab && (\r\n              <div className=\"assessment-content\">\r\n                {!finalAssessmentLoaded ? (\r\n                  // Loading state - show skeleton loaders\r\n                  <div className=\"row g-4\">\r\n                    <div className=\"col-md-4\">\r\n                      <div className=\"improvement-areas-card\">\r\n                        <Skeleton height={200} width={\"100%\"} borderRadius={20} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"col-md-8\">\r\n                      <div className=\"improvement-areas-card\">\r\n                        <Skeleton height={200} width={\"100%\"} borderRadius={20} />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ) : finalAssessmentLoaded && finalAssessment ? (\r\n                  // Data available - show actual content\r\n                  <div className=\"row g-4\">\r\n                    {finalAssessment && finalAssessment.overallSuccessProbability && finalAssessment.overallSuccessProbability && (\r\n                      <div className=\"col-md-4\">\r\n                        <div className=\"circular-progress-card\">\r\n                          <CircularProgressbar\r\n                            className=\"circular-progress-bar\"\r\n                            value={animationValue}\r\n                            text={`${animationValue}%`} // ⇐ plain string\r\n                            circleRatio={0.5}\r\n                            strokeWidth={16}\r\n                            styles={buildStyles({\r\n                              rotation: 0.749,\r\n                              strokeLinecap: \"butt\",\r\n                              trailColor: \"#eee\",\r\n                              textColor: \"#333\",\r\n                              textSize: \"1.6rem\",\r\n                              pathColor: \"#9ebff7\",\r\n                              pathTransitionDuration: 0.5,\r\n                            })}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                    <div className=\"col-md-8\">\r\n                      <div className=\"summary-text-card row\">\r\n                        {finalAssessment &&\r\n                          finalAssessment.skillSummary &&\r\n                          finalAssessment.skillSummary &&\r\n                          finalAssessment.skillSummary.finalSummary.length > 0 && (\r\n                            <div className=\"col-md-9\">\r\n                              <h3 className=\"sub-tittle mt-0\">\r\n                                <AiMarkIcon className=\"me-2\" /> {t(\"ai_summary\")}\r\n                              </h3>\r\n                              <ul className=\"check-list\">\r\n                                {finalAssessment?.skillSummary?.finalSummary?.map((item: string, index) => {\r\n                                  return (\r\n                                    <li key={index}>\r\n                                      <CheckSecondaryIcon className=\"me-2\" /> {item}\r\n                                    </li>\r\n                                  );\r\n                                })}\r\n                              </ul>\r\n                            </div>\r\n                          )}\r\n                        <div className=\"col-md-3\">\r\n                          <AIVerifiedIcon />\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  // No data available - show message\r\n                  <div className=\"no-final-assessment\">\r\n                    <p>{t(\"no_final_assessment_data_found\")}</p>\r\n                  </div>\r\n                )}\r\n                <div className=\"row g-4\">\r\n                  <div className=\"col-12 col-md-6 d-flex\">\r\n                    <div className=\"skills-score-card skills-graph-card flex-grow-1\">\r\n                      {!skillAssessmentLoaded ? (\r\n                        <Skeleton height={300} width={\"100%\"} borderRadius={20} />\r\n                      ) : (\r\n                        <>\r\n                          {/* <h3 className=\"sub-tittle mt-0\">\r\n                            <AiMarkIcon className=\"me-2\" /> AI Summary\r\n                          </h3> */}\r\n                          <Radar data={radarChartData} options={{ maintainAspectRatio: false }} />\r\n                        </>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"col-12 col-md-6 d-flex\">\r\n                    <div className=\"skills-score-card skills-graph-card flex-grow-1\">\r\n                      {!skillAssessmentLoaded ? (\r\n                        <Skeleton height={300} width={\"100%\"} borderRadius={20} />\r\n                      ) : (\r\n                        <Bar\r\n                          data={barChartData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: { legend: { display: false }, title: { display: false } },\r\n                            scales: { x: { grid: { display: false } } },\r\n                          }}\r\n                        />\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                {/* next design */}\r\n                <div className=\"row g-4\">\r\n                  {!skillAssessmentLoaded ? (\r\n                    <div className=\"col-md-4\">\r\n                      <div className=\"improvement-areas-card\">\r\n                        <Skeleton height={200} width={\"100%\"} borderRadius={20} />\r\n                      </div>\r\n                    </div>\r\n                  ) : skillSpecificAssessment && skillSpecificAssessment.skillsScores && skillSpecificAssessment.skillsScores.length > 0 ? (\r\n                    <div className=\"col-lg-4\">\r\n                      <div className=\"summary-text-card skills-score-card\">\r\n                        <h3 className=\"sub-tittle mt-0\">{t(\"skills_score\")}</h3>\r\n                        <ul className=\"skills-list\">\r\n                          <li className=\"skills-item\">\r\n                            <span className=\"skill-name\">{t(\"career_based_skills\")}</span>\r\n                            <span className=\"skill-rating\">\r\n                              <StarIcon /> {skillSpecificAssessment.careerBasedSkillsScore}/10\r\n                            </span>\r\n                          </li>\r\n                          {skillSpecificAssessment &&\r\n                            skillSpecificAssessment.skillsScores &&\r\n                            skillSpecificAssessment.skillsScores.length > 0 &&\r\n                            skillSpecificAssessment.skillsScores.map((item, index) => {\r\n                              return (\r\n                                <li key={index} className=\"skills-item\">\r\n                                  <span className=\"skill-name\">{item.skill_name}</span>\r\n                                  <span className=\"skill-rating\">\r\n                                    {item.skill_marks === 10 ? (\r\n                                      <span className=\"skill-badge\">{t(\"extreme\")}</span>\r\n                                    ) : (\r\n                                      <>\r\n                                        <StarIcon /> {item.skill_marks}/10\r\n                                      </>\r\n                                    )}\r\n                                  </span>\r\n                                </li>\r\n                              );\r\n                            })}\r\n                        </ul>\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    \"\"\r\n                  )}\r\n                  {!skillAssessmentLoaded ? (\r\n                    <div className=\"col-lg-8\">\r\n                      <div className=\"improvement-areas-card\">\r\n                        <Skeleton height={200} width={\"100%\"} borderRadius={20} />\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    skillSpecificAssessment &&\r\n                    skillSpecificAssessment.skillsScores && (\r\n                      <div className=\"col-lg-8\">\r\n                        <div className=\"summary-text-card skills-summary-card\">\r\n                          <h3 className=\"sub-tittle mt-0\">{t(\"skills_summary\")}</h3>\r\n                          <div className=\"skills-tags\">\r\n                            {skillSpecificAssessment &&\r\n                              skillSpecificAssessment.skillsScores &&\r\n                              skillSpecificAssessment.skillsScores.map((skill, index) => (\r\n                                <span\r\n                                  key={index}\r\n                                  className={`skill-tag ${activeSkillTab === skill.skill_name ? \"active\" : \"\"}`}\r\n                                  onClick={() => setActiveSkillTab(skill.skill_name)}\r\n                                >\r\n                                  {skill.skill_name}\r\n                                </span>\r\n                              ))}\r\n                          </div>\r\n\r\n                          <div className=\"strengths-gaps\">{renderSkillDetails()}</div>\r\n                        </div>\r\n                      </div>\r\n                    )\r\n                  )}\r\n                  <div className=\"col-12\">\r\n                    {!finalAssessmentLoaded ? (\r\n                      // Loading state - show skeleton loaders\r\n                      <div className=\"improvement-areas-card\">\r\n                        <Skeleton height={18} width={\"20%\"} borderRadius={6} />\r\n                        <div className=\"row g-3 mt-3\">\r\n                          <div className=\"col-md-4\">\r\n                            <Skeleton height={177} width={\"100%\"} borderRadius={24} />\r\n                          </div>\r\n                          <div className=\"col-md-4\">\r\n                            <Skeleton height={177} width={\"100%\"} borderRadius={24} />\r\n                          </div>\r\n                          <div className=\"col-md-4\">\r\n                            <Skeleton height={177} width={\"100%\"} borderRadius={24} />\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ) : finalAssessmentLoaded &&\r\n                      finalAssessment &&\r\n                      finalAssessment.developmentRecommendations &&\r\n                      finalAssessment.developmentRecommendations.recommendations &&\r\n                      finalAssessment.developmentRecommendations.recommendations.length > 0 ? (\r\n                      // Data available - show actual content\r\n                      <div className=\"summary-text-card improvement-areas-card\">\r\n                        <h3 className=\"sub-tittle mt-0\">{t(\"improvement_areas\")}</h3>\r\n                        <div className=\"row g-4\">\r\n                          {finalAssessment.developmentRecommendations.recommendations.map((recommendation, index) => (\r\n                            <div key={index} className=\"col-md-4\">\r\n                              <div className=\"improvement-card h-100\">\r\n                                <h4 className=\"title\">{recommendation.title}</h4>\r\n                                <p className=\"description\">{recommendation.description}</p>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      // No data available - show message (this covers both no finalAssessment and no developmentRecommendations)\r\n                      <div className=\"no-improvement-areas\">\r\n                        <p>{t(\"no_improvement_areas_data_found\")}</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* end next design */}\r\n              </div>\r\n            )}\r\n            {!selectedTab &&\r\n              (isLoadingInterviewHistory ? (\r\n                <div className=\"history-content\">\r\n                  <div className=\"interview-summary\">\r\n                    <div className=\"summary-header\">\r\n                      <Skeleton height={21} width={\"20%\"} borderRadius={6} />\r\n                    </div>\r\n                    <div className=\"interviewer\">\r\n                      <Skeleton height={21} width={\"20%\"} className=\"mb-4\" borderRadius={6} />\r\n\r\n                      <div className=\"interviewer-info\">\r\n                        <Skeleton height={45} width={45} borderRadius={100} />\r\n                        <Skeleton height={18} width={100} borderRadius={6} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"summary-scores\">\r\n                      <Skeleton height={18} width={100} className=\"mb-4\" borderRadius={6} />\r\n                      <div className=\"score-btns mt-2\">\r\n                        <Skeleton height={45} width={170} className=\"mb-4\" borderRadius={12} />\r\n                        <Skeleton height={45} width={170} className=\"mb-4\" borderRadius={12} />\r\n                        <Skeleton height={45} width={170} className=\"mb-4\" borderRadius={12} />\r\n                        <Skeleton height={45} width={170} className=\"mb-4\" borderRadius={12} />\r\n                        <Skeleton height={45} width={170} className=\"mb-4\" borderRadius={12} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"summary-highlights\">\r\n                      <Skeleton height={18} width={100} className=\"mb-4\" borderRadius={6} />\r\n                      <ul className=\"highlight-list p-0\">\r\n                        <Skeleton height={16} width={\"80%\"} className=\"mb-3\" borderRadius={6} count={4} />\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"interview-summary\">\r\n                    <div className=\"summary-header\">\r\n                      <Skeleton height={21} width={\"20%\"} borderRadius={6} />\r\n                    </div>\r\n                    <div className=\"interviewer\">\r\n                      <div className=\"interviewer-info large\">\r\n                        <Skeleton height={45} width={45} borderRadius={100} />\r\n                        <Skeleton height={18} width={100} borderRadius={6} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"summary-highlights\">\r\n                      <Skeleton height={18} width={100} borderRadius={6} className=\"mb-4\" />\r\n                      <ul className=\"highlight-list p-0\">\r\n                        <Skeleton height={16} width={\"80%\"} className=\"mb-3\" borderRadius={6} count={4} />\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"history-content\">\r\n                  {candidateInterviewHistory && candidateInterviewHistory.length > 0 ? (\r\n                    <>\r\n                      {candidateInterviewHistory.map((historyItem, index) => (\r\n                        <div key={index} className=\"interview-summary\">\r\n                          <div className=\"summary-header\">\r\n                            <h1 className=\"summary-heading\">\r\n                              {t(\"round\")} {historyItem.roundNumber} {t(\"summary\")}\r\n                            </h1>\r\n                          </div>\r\n                          <div className=\"interviewer\">\r\n                            <h2 className=\"summary-title\">{t(\"interview_by\")}</h2>\r\n                            <div className=\"interviewer-info\">\r\n                              <Image\r\n                                src={historyItem.interviewerImage || noImageFound}\r\n                                alt={t(\"interviewer_avatar\")}\r\n                                className=\"interviewer-avatar\"\r\n                                width={50}\r\n                                height={50}\r\n                              />\r\n                              <span className=\"interviewer-name\">{historyItem.interviewerName}</span>\r\n                              {historyItem.endTime ? (\r\n                                <div className=\"text-muted small\">\r\n                                  <p>\r\n                                    {\" \"}\r\n                                    {new Date(historyItem.endTime).toLocaleString(undefined, {\r\n                                      year: \"numeric\",\r\n                                      month: \"short\",\r\n                                      day: \"numeric\",\r\n                                      hour: \"2-digit\",\r\n                                      minute: \"2-digit\",\r\n                                    })}\r\n                                  </p>\r\n                                </div>\r\n                              ) : null}\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"summary-scores\">\r\n                            <h2 className=\"summary-title\">{t(\"scores\")}</h2>\r\n                            <div className=\"score-btns\">\r\n                              <Button className=\"secondary-btn rounded-md px-3 py-3\">\r\n                                {t(\"hard_skills\")} : {historyItem.hardSkillMarks}\r\n                              </Button>\r\n                              {Object.entries(historyItem.skillScores).map(([skillName, score]) => (\r\n                                <Button key={skillName} className=\"secondary-btn rounded-md px-3 py-3\">\r\n                                  {skillName} : {score === 10 ? t(\"extreme\") : score}\r\n                                </Button>\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"summary-highlights\">\r\n                            <h2 className=\"summary-title\">{t(\"highlights\")}</h2>\r\n                            <ul className=\"highlight-list\">\r\n                              {historyItem.interviewSummary?.highlight.map((item: string, index) => {\r\n                                return (\r\n                                  <li key={index} className=\"highlight-item\">\r\n                                    {item}\r\n                                  </li>\r\n                                );\r\n                              })}\r\n                            </ul>\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                      {/* Show current user's performance feedback */}\r\n                      {aiInterviewerAnalysis && (\r\n                        <div className=\"interview-summary\">\r\n                          <div className=\"summary-header\">\r\n                            <h1 className=\"summary-heading\">{t(\"your_performance_feedback\")}</h1>\r\n                          </div>\r\n                          <div className=\"interviewer\">\r\n                            <div className=\"interviewer-info large\">\r\n                              <Image\r\n                                src={authData?.image || noImageFound}\r\n                                alt={t(\"interviewer_avatar\")}\r\n                                className=\"interviewer-avatar\"\r\n                                width={50}\r\n                                height={50}\r\n                              />\r\n                              <span className=\"interviewer-name\">\r\n                                {authData?.first_name} {authData?.last_name}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"summary-highlights\">\r\n                            <h2 className=\"summary-title\">{t(\"highlights\")}</h2>\r\n                            <ul className=\"highlight-list\">\r\n                              {aiInterviewerAnalysis.highlights?.map((item: string, index: number) => {\r\n                                return (\r\n                                  <li key={index} className=\"highlight-item\">\r\n                                    {item}\r\n                                  </li>\r\n                                );\r\n                              })}\r\n                            </ul>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Show all interviewer performance feedback for admin users */}\r\n                      {authData?.account_type === \"admin\" && allInterviewerAnalysis.length > 0 && (\r\n                        <div className=\"admin-interviewer-analysis\">\r\n                          <div className=\"summary-header\">\r\n                            <h1 className=\"summary-heading\">{t(\"all_interviewer_performance_feedback\")}</h1>\r\n                          </div>\r\n                          {allInterviewerAnalysis.map((analysisData) => (\r\n                            <div key={`${analysisData.interviewerId}-${analysisData.roundNumber}`} className=\"interview-summary\">\r\n                              <div className=\"interviewer\">\r\n                                <div className=\"interviewer-info large\">\r\n                                  <Image\r\n                                    src={analysisData.interviewerImage || noImageFound}\r\n                                    alt={t(\"interviewer_avatar\")}\r\n                                    className=\"interviewer-avatar\"\r\n                                    width={50}\r\n                                    height={50}\r\n                                  />\r\n                                  <span className=\"interviewer-name\">\r\n                                    {analysisData.interviewerName} - Round {analysisData.roundNumber}\r\n                                  </span>\r\n                                </div>\r\n                              </div>\r\n                              {analysisData.analysis?.highlights && (\r\n                                <div className=\"summary-highlights\">\r\n                                  <h2 className=\"summary-title\">{t(\"highlights\")}</h2>\r\n                                  <ul className=\"highlight-list\">\r\n                                    {analysisData.analysis.highlights.map((item: string, highlightIndex: number) => (\r\n                                      <li key={highlightIndex} className=\"highlight-item\">\r\n                                        {item}\r\n                                      </li>\r\n                                    ))}\r\n                                  </ul>\r\n                                </div>\r\n                              )}\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      )}\r\n                    </>\r\n                  ) : (\r\n                    <div className=\"no-interview-history\">\r\n                      <p>{t(\"no_interview_history_found\")}</p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\nexport default CandidateProfile;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAYA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAWA;AAQA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA,MAAM,gBAAgB;IACpB,MAAM;IACN,SAAS;IACT,YAAY;AACd;AAEA;;;;;;;;;;;;;;;CAeC,GACD,MAAM,mBAAoD,CAAC,EAAE,MAAM,EAAE;;IACnE,+EAA+E;IAC/E,0BAA0B;IAC1B,+EAA+E;IAE/E,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;;IAChF,MAAM,IAAI,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;IAExB,mCAAmC;IACnC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,MAAG,AAAD,EAAE;IAC1B,MAAM,mBAAmB,cAAc,gBAAgB;IAEvD,+EAA+E;IAC/E,qBAAqB;IACrB,+EAA+E;IAE/E,6DAA6D;IAC7D,2FAA2F,GAC3F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,mDAAmD,GACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,sDAAsD,GACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,oDAAoD;IACpD,yCAAyC,GACzC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAClG,oDAAoD,GACpD,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC,EAAE;IAC3G,4CAA4C,GAC5C,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACxG,iDAAiD,GACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAEhF,2CAA2C;IAC3C,gDAAgD,GAChD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClF,gDAAgD,GAChD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClF,iDAAiD,GACjD,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpF,6CAA6C,GAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,oDAAoD,GACpD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClF,sDAAsD,GACtD,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAE1F,qDAAqD;IACrD,6DAA6D,GAC7D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC5E,6DAA6D,GAC7D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC5E,8DAA8D,GAC9D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC9E,+DAA+D,GAC/D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAG9E,mEAAmE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,cAAc,IAAI;IAC9E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,kDAAkD;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzD,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,SAAS;QACT,mBAAmB;QACnB,SAAS;yCAAE,KAAO;;QAClB,SAAS;IACX;IAEA,QAAQ,GAAG,CAAC,uBAAuB,uBAAuB;IAE1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkD;QAAE,QAAQ;QAAO,YAAY;IAAK;IAEjI,kDAAkD;IAClD,mFAAmF;IACnF,0EAA0E;IAC1E,cAAc;IACd,MAAM;IACN,+BAA+B;IAC/B,KAAK;IAEL,qEAAqE;IACrE,MAAM,wCAAwC;QAC5C,qBAAqB;YACnB,QAAQ;YACR,QAAQ;YACR,OAAO,EAAE;YACT,SAAS,EAAE;YACX,mBAAmB,EAAE;YACrB,WAAW;YACX,SAAS;QACX;IACF;IAEA,MAAM,uCAAuC;QAC3C,qBAAqB;YACnB,QAAQ;YACR,QAAQ;YACR,OAAO,EAAE;YACT,SACE;YACF,mBAAmB,EAAE;YACrB,WAAW;YACX,SAAS;QACX;IACF;IAEA,MAAM,uBAAuB;QAC3B,qBAAqB;YACnB,QAAQ;YACR,QAAQ;YACR,OAAO,EAAE;YACT,SAAS,EAAE;YACX,mBAAmB,EAAE;YACrB,WAAW,IAAM,mCAAmC,6IAAA,CAAA,qBAAkB,CAAC,KAAK;YAC5E,SAAS;QACX;IACF;IAEA,MAAM,yBAAyB;QAC7B,qBAAqB;YACnB,QAAQ;YACR,QAAQ;YACR,OAAO,EAAE;YACT,SAAS,EAAE;YACX,mBAAmB,EAAE;YACrB,WAAW,IAAM,mCAAmC,6IAAA,CAAA,qBAAkB,CAAC,YAAY;YACnF,SAAS;QACX;IACF;IAEA,MAAM,yBAAyB;QAC7B,qBAAqB;YACnB,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAO;YAClB,SAAS;QACX;IACF;IAEA;;GAEC,GACD,MAAM,uCAAuC;QAC3C,IAAI,CAAC,sBAAsB,SAAS,CAAC,sBAAsB,kBAAkB;YAC3E,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB;YACA;QACF;QAEA,IAAI;YACF,6BAA6B;YAC7B,qBAAqB,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAK,CAAC;YAC1D,aAAa;YACb,eAAe,cAAc,UAAU;YAEvC,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD,EAAE;gBAAE,OAAO,sBAAsB;gBAAO,kBAAkB,sBAAsB;YAAiB;YAC5I,IAAI,YAAY,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACtD,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,UAAU,MAAM,WAAW;gBAEjD,mDAAmD;gBACnD,MAAM,oBAAoB,SAAS,IAAI,CAAC,IAAI,EAAE;gBAE9C,IAAI,mBAAmB;oBACrB,6FAA6F;oBAC7F,8EAA8E;oBAC9E,OAAO,IAAI,CACT,GAAG,6HAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,kBAAkB,OAAO,EAAE,sBAAsB,MAAM,kBAAkB,EAAE,sBAAsB,kBAAkB;gBAExL;YACF,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM,WAAW;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,aAAa;YACb,eAAe,cAAc,IAAI;YACjC;QACF;IACF;IAEA;;GAEC,GACD,MAAM,8BAA8B;QAClC,IAAI,CAAC,sBAAsB,SAAS,CAAC,sBAAsB,kBAAkB;YAC3E,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB;QACF;QAEA,IAAI;YACF,aAAa;YACb,eAAe,cAAc,UAAU;YACvC,oBAAoB;YACpB,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD,EAAE;gBAAE,OAAO,sBAAsB;gBAAO,kBAAkB,sBAAsB;YAAiB;YAC5I,IAAI,YAAY,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACtD,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,UAAU,MAAM,WAAW;gBAEjD,mDAAmD;gBACnD,MAAM,oBAAoB,SAAS,IAAI,CAAC,IAAI,EAAE;gBAE9C,IAAI,mBAAmB;oBACrB,6FAA6F;oBAC7F,8EAA8E;oBAC9E,OAAO,IAAI,CACT,GAAG,6HAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,kBAAkB,OAAO,EAAE,sBAAsB,MAAM,kBAAkB,EAAE,sBAAsB,kBAAkB;gBAExL;YACF,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM,WAAW;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,aAAa;YACb,eAAe,cAAc,IAAI;QACnC;IACF;IAEA;;GAEC,GACD,MAAM,2BAA2B;QAC/B,oBAAoB;IACtB;IAEA,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,mEAAmE;YACnE,MAAM;0DAAc;oBAClB,IAAI,CAAC,kBAAkB;wBACrB;oBACF;oBAEA,IAAI;wBACF,aAAa;wBACb,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE;wBAE3C,IAAI,UAAU,MAAM;4BAClB,MAAM,iBAAiB,UAAU,MAAM;4BAEvC,iCAAiC;4BACjC,oBAAoB;gCAClB,QAAQ,CAAC,CAAC,eAAe,YAAY;gCACrC,oBAAoB,eAAe,kBAAkB,IAAI;gCACzD,uBAAuB,eAAe,qBAAqB,IAAI;gCAC/D,cAAc,eAAe,YAAY,IAAI;4BAC/C;wBACF,OAAO;4BACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM,WAAW;wBACjD;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC;oBAChB,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;qCAAG;QAAC;KAAE;IACN,+EAA+E;IAC/E,sBAAsB;IACtB,+EAA+E;IAE/E,+BAA+B;IAC/B,+JAAA,CAAA,QAAO,CAAC,QAAQ,CAAC,+JAAA,CAAA,oBAAiB,EAAE,+JAAA,CAAA,gBAAa,EAAE,+JAAA,CAAA,cAAW,EAAE,+JAAA,CAAA,aAAU,EAAE,+JAAA,CAAA,eAAY,EAAE,+JAAA,CAAA,cAAW,EAAE,+JAAA,CAAA,QAAK,EAAE,+JAAA,CAAA,UAAO,EAAE,+JAAA,CAAA,SAAM;IAE7H,+EAA+E;IAC/E,oBAAoB;IACpB,+EAA+E;IAE/E;;;GAGC,GACD,MAAM,uBAAuB;QAC3B,MAAM,SAAS,yBAAyB,aAAa,IAAI,CAAC,OAAS,KAAK,UAAU,KAAK,EAAE;QACzF,MAAM,SAAS,yBAAyB,aAAa,IAAI,CAAC,OAAS,KAAK,WAAW,GAAG,OAAO,EAAE;QAE/F,OAAO;YACL;YACA,UAAU;gBACR;oBACE,MAAM;oBACN,iBAAiB,OAAO,GAAG,CAAC,CAAC,OAAO;wBAClC,MAAM,aAAa,MAAM,CAAC,MAAM;wBAChC,kDAAkD;wBAClD,OAAO,eAAe,MAAM,YAAY;oBAC1C;oBACA,cAAc;oBACd,eAAe;oBACf,eAAe;gBACjB;aACD;QACH;IACF;IAEA;;;GAGC,GACD,MAAM,yBAAyB;QAC7B,OAAO;YACL,QAAQ;gBAAC;gBAAiB;gBAAc;gBAAW;gBAAe;aAAa;YAC/E,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM;wBAAC;wBAAI;wBAAI;wBAAI;wBAAI;qBAAG;oBAC1B,MAAM;oBACN,iBAAiB;oBACjB,aAAa;oBACb,sBAAsB;oBACtB,kBAAkB;oBAClB,2BAA2B;oBAC3B,uBAAuB;gBACzB;aACD;QACH;IACF;IAEA;;;;GAIC,GACD,MAAM,sBAAsB,CAAC;QAC3B,OAAO,KAAK,KAAK,CAAC,cAAc;IAClC;IAEA,+EAA+E;IAC/E,kBAAkB;IAClB,+EAA+E;IAE/E,2CAA2C,GAC3C,MAAM,eAAe;IAErB,6CAA6C,GAC7C,MAAM,iBAAiB;IAEvB,+EAA+E;IAC/E,iBAAiB;IACjB,+EAA+E;IAE/E;;;;;;;GAOC,GACD,MAAM,8BAA8B;QAClC,eAAe;QAEf,oDAAoD;QAEpD,MAAM;IACR;IAEA;;;;;;;GAOC,GACD,MAAM,6BAA6B;QACjC,IAAI,CAAC,8BAA8B;QAEnC,eAAe;QAEf,mDAAmD;QACnD,IAAI,CAAC,yBAAyB,CAAC,uBAAuB;YACpD,MAAM,QAAQ,GAAG,CAAC;gBAAC;gBAAuB;aAA8B;QAC1E;IACF;IAEA;;;;;;;;GAQC,GACD,MAAM,qCAAqC,OAAO;QAChD,IAAI,CAAC,sBAAsB;YACzB;YACA;QACF;QAEA,IAAI;YACF,6BAA6B;YAC7B,qBAAqB,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAK,CAAC;YAC1D,gBAAgB;YAEhB,MAAM,WAAW,MAAM,CAAA,GAAA,yKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,qBAAqB,gBAAgB,GAAG;YAEjG,IAAI,YAAY,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACtD,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,SAAS,IAAI,CAAC,OAAO;gBAC3C,MAAM;YACR,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,gBAAgB;YAChB;QACF;IACF;IAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,+EAA+E;IAC/E,uCAAuC;IAEvC,2BAA2B;IAC3B,UAAU;IACV,gHAAgH;IAEhH,gEAAgE;IAChE,uDAAuD;IACvD,gCAAgC;IAChC,eAAe;IACf,uDAAuD;IACvD,QAAQ;IACR,sBAAsB;IACtB,sEAAsE;IACtE,oDAAoD;IACpD,gBAAgB;IAChB,8BAA8B;IAC9B,MAAM;IACN,KAAK;IAEL,+EAA+E;IAC/E,gBAAgB;IAChB,+EAA+E;IAE/E;;;;;;;;;;;;;GAaC,GACD,MAAM,gCAAgC;QACpC,IAAI,2BAA2B;QAE/B,6BAA6B;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,yKAAA,CAAA,+BAA4B,AAAD,EAAE;YACpD,IAAI,UAAU,MAAM,SAAS;gBAC3B,6BAA6B,SAAS,IAAI,CAAC,IAAI;gBAC/C,0BAA0B;YAC5B,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,6BAA6B;QAC/B;IACF;IAEA;;;;;;;;;;;;;GAaC,GACD,MAAM,sBAAsB;QAC1B,IAAI,0BAA0B;QAE9B,4BAA4B;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,yKAAA,CAAA,6BAA0B,AAAD,EAAE;YAClD,IAAI,UAAU,MAAM,SAAS;gBAC3B,mBAAmB,SAAS,IAAI,CAAC,IAAI;gBACrC,yBAAyB;YAC3B,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,4BAA4B;QAC9B;IACF;IAEA;;;;;;;;;;;;;GAaC,GACD,MAAM,8BAA8B;QAClC,IAAI,0BAA0B;QAC9B,yBAAyB;QAEzB,4BAA4B;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,6BAA6B;YACpD,IAAI,UAAU,MAAM,SAAS;gBAC3B,2BAA2B,SAAS,IAAI,CAAC,IAAI;YAC/C,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,4BAA4B;QAC9B;IACF;IAEA;;;;;;;;;;GAUC,GACD,MAAM,uBAAuB;QAC3B,0BAA0B;QAE1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,yKAAA,CAAA,wBAAqB,AAAD,EAAE;YAC7C,IAAI,UAAU,MAAM,SAAS;gBAC3B,wBAAwB,SAAS,IAAI,CAAC,IAAI;gBAC1C,0BAA0B;YAC5B,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;gBACpC,0BAA0B;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,0BAA0B;QAC5B;IACF;IAEA;;;;;;;;;;;;GAYC,GACD,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,yBAAyB;QACzB,yBAAyB;QACzB,0BAA0B;QAE1B,4BAA4B;QAC5B,MAAM;QAEN,+CAA+C;QAC/C,IAAI,aAAa;YACf,MAAM,QAAQ,GAAG,CAAC;gBAAC;gBAA+B;aAAsB;QAC1E,OAAO;YACL,MAAM;QACR;IACF;IAEA;;;;;;;GAOC,GACD,MAAM,sCAAsC;QAC1C,IAAI,CAAC,sBAAsB,oBAAoB,0BAA0B;YACvE;YACA;QACF;QAEA,IAAI;YACF,6BAA6B;YAC7B,qBAAqB,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAK,CAAC;YAC1D,4BAA4B;YAE5B,MAAM,WAAW,MAAM,CAAA,GAAA,yKAAA,CAAA,uBAAoB,AAAD,EAAE,qBAAqB,gBAAgB;YACjF,IAAI,UAAU,MAAM,SAAS;gBAC3B,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,UAAU,MAAM,WAAW;gBACjD,gEAAgE;gBAChE,gCAAgC;gBAChC,wDAAwD;gBACxD,eAAe;gBACf,sCAAsC;gBACtC,MAAM,QAAQ,GAAG,CAAC;oBAAC;oBAAuB;iBAA8B;YAC1E,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM,WAAW;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,4BAA4B;YAC5B;QACF;IACF;IAEA;;;;;;;;;;;;GAYC,GACD,kEAAkE;IAClE,qFAAqF;IAErF,uCAAuC;IACvC,UAAU;IACV,uGAAuG;IACvG,qCAAqC;IACrC,mGAAmG;IACnG,yEAAyE;IACzE,+CAA+C;IAC/C,iEAAiE;IACjE,8BAA8B;IAC9B,+CAA+C;IAC/C,mFAAmF;IACnF,eAAe;IACf,6FAA6F;IAC7F,QAAQ;IACR,sBAAsB;IACtB,+DAA+D;IAC/D,oDAAoD;IACpD,gBAAgB;IAChB,0CAA0C;IAC1C,MAAM;IACN,KAAK;IAEL,+EAA+E;IAC/E,oCAAoC;IACpC,+EAA+E;IAE/E;;;GAGC,GACD,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2DAClC,IACE,2BAA2B;mEAAK,CAAC,SAAW,OAAO,aAAa,KAAK,UAAU,MAAM,OAAO,gCAAgC;mEACxH;0DACN;QAAC;QAA2B,UAAU;KAAG;IAG3C;;;GAGC,GACD,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4DAAE;YACrC,IAAI,CAAC,6BAA6B,CAAC,YAAY,SAAS,YAAY,KAAK,SAAS,OAAO,EAAE;YAE3F,OAAO,0BACJ,MAAM;oEAAC,CAAC,SAAW,OAAO,gCAAgC;mEAC1D,GAAG;oEAAC,CAAC,SAAW,CAAC;wBAChB,eAAe,OAAO,aAAa;wBACnC,iBAAiB,OAAO,eAAe;wBACvC,kBAAkB,OAAO,gBAAgB;wBACzC,aAAa,OAAO,WAAW;wBAC/B,UAAU,OAAO,gCAAgC;oBACnD,CAAC;;QACL;2DAAG;QAAC;QAA2B;KAAS;IAExC,+EAA+E;IAC/E,UAAU;IACV,+EAA+E;IAE/E;;;GAGC,GACD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;6DAAiB;oBACrB,+DAA+D;oBAC/D,0BAA0B;oBAC1B,6BAA6B;oBAE7B,gEAAgE;oBAChE,MAAM,QAAQ,GAAG,CAAC;wBAAC;wBAAwB;qBAAgC;gBAC7E;;YAEA;QACF;qCAAG;QAAC;KAAiB;IAErB;;;GAGC,GACD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,yBAAyB,gBAAgB,wBAAwB,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,gBAAgB;gBAC/G,kBAAkB,wBAAwB,YAAY,CAAC,EAAE,CAAC,UAAU;YACtE;QACF;qCAAG;QAAC;QAAyB;KAAe;IAE5C;;;GAGC,GACD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,QAAQ;oDAAsB,IAAM,kBAAkB,iBAAiB,6BAA6B;;YAC1G;8CAAO,IAAM,qBAAqB;;QACpC;qCAAG;QAAC,iBAAiB;KAA0B;IAE/C,+EAA+E;IAC/E,eAAe;IACf,+EAA+E;IAE/E;;;GAGC,GACD,MAAM,qBAAqB;QACzB,IAAI,CAAC,yBAAyB,gBAAgB,CAAC,gBAAgB,OAAO;QAEtE,MAAM,gBAAgB,wBAAwB,YAAY,CAAC,IAAI,CAAC,CAAC,QAAU,MAAM,UAAU,KAAK;QAEhG,IAAI,CAAC,eAAe,OAAO;QAE3B,MAAM,cAAc,cAAc,oCAAoC,EAAE,+BAA+B;QACvG,MAAM,aAAa,oBAAoB;QAEvC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkB;;;;;;sCAChC,6LAAC;4BAAG,WAAU;sCACX,cAAc,SAAS,EAAE,WAAW,IAAI,CAAC,UAAU,sBAClD,6LAAC;oCAAe,WAAU;8CACvB;mCADM;;;;;;;;;;sCAKb,6LAAC;4BAAG,WAAU;sCAAkB;;;;;;sCAChC,6LAAC;4BAAG,WAAU;sCACX,cAAc,eAAe,EAAE,eAAe,IAAI,CAAC,KAAK,sBACvD,6LAAC;oCAAe,WAAU;8CACvB;mCADM;;;;;;;;;;;;;;;;8BAMf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkB;;;;;;0CAChC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAAgB;4CAAY;;;;;;;kDAC1C,6LAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAG,GAAG,CAAC,GAAG,sBAC9B,6LAAC;gDAAgB,WAAW,CAAC,IAAI,EAAE,QAAQ,aAAa,WAAW,IAAI;+CAA7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ1B;IAEA,qBACE;;0BACE,6LAAC,0JAAA,CAAA,UAAuB;gBAAC,MAAM,gBAAgB,cAAc,UAAU;;;;;;YACtE,kCACC,6LAAC,oKAAA,CAAA,UAA2B;gBAAC,eAAe;gBAA0B,iBAAiB;gBAA6B,UAAU;;;;;;0BAIhI,6LAAC,0JAAA,CAAA,UAAiB;gBAChB,QAAQ,kBAAkB,MAAM;gBAChC,SAAS;gBACT,WAAW,kBAAkB,SAAS;gBACtC,OAAO,kBAAkB,KAAK;gBAC9B,SAAS,kBAAkB,OAAO;gBAClC,mBAAmB,kBAAkB,iBAAiB;gBACtD,SAAS,kBAAkB,OAAO;gBAClC,aAAa,EAAE;;;;;;0BAGjB,6LAAC;gBAAI,WAAW,kKAAA,CAAA,UAAK,CAAC,sBAAsB;0BAC1C,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,+IAAA,CAAA,UAAM;oDAAC,WAAU;8DAChB,cAAA,6LAAC,uJAAA,CAAA,UAAa;wDACZ,SAAS;4DACP,OAAO,IAAI;wDACb;;;;;;;;;;;gDAEM;gDACT,EAAE;gDAAa;8DAAC,6LAAC;;wDAAM,EAAE;wDAAW;;;;;;;;;;;;;sDAGvC,6LAAC,+IAAA,CAAA,UAAM;4CACL,WAAU;4CACV,SAAS;gDACP,IAAI,CAAC,WAAW;oDACd,IAAI,CAAC,kBAAkB,QAAQ;wDAC7B;oDACF,OAAO,IAAI,kBAAkB,cAAc;wDACzC,wEAAwE;wDACxE,OAAO,IAAI,CACT,GAAG,6HAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,iBAAiB,YAAY,CAAC,OAAO,EAAE,sBAAsB,MAAM,kBAAkB,EAAE,sBAAsB,kBAAkB;oDAEpM;gDACF;4CACF;4CACA,UAAU;;gDAET,gBAAgB,aAAc,aAAa,gBAAgB,eAAgB,mBAAK,6LAAC,6JAAA,CAAA,UAAmB;;;;;gDACpG,gBAAgB,aAAc,aAAa,gBAAgB,6BAC1D,6LAAC,gKAAA,CAAA,UAAQ;oDAAC,OAAO;oDAAK,QAAQ;oDAAI,cAAc;;;;;2DAC9C,gBAAgB,eAClB,EAAE,0BACA,CAAC,kBAAkB,SACrB,EAAE,6BACA,iBAAiB,qBAAqB,GACxC,EAAE,kCAEF,EAAE;;;;;;;wCAKL,wBAAwB,qBAAqB,UAAU,kBACtD;;8DACE,6LAAC,+IAAA,CAAA,UAAM;oDACL,SAAS,IAAM,eAAe;4DAAE,QAAQ;4DAAM,YAAY,sBAAsB;wDAAW;oDAC3F,WAAU;;sEAEV,6LAAC,2JAAA,CAAA,UAAiB;4DAAC,WAAU;;;;;;wDAC5B,EAAE;;;;;;;8DAEL,6LAAC,oJAAA,CAAA,UAAW;oDACV,QAAQ,YAAY,MAAM;oDAC1B,SAAS,IAAM,eAAe;4DAAE,GAAG,WAAW;4DAAE,QAAQ;wDAAM;oDAC9D,YAAY,YAAY,UAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAO9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,uCACC,6LAAC,gKAAA,CAAA,UAAQ;4CAAC,QAAQ;4CAAK,OAAO;4CAAK,cAAc;;;;;iEAEjD,6LAAC,iKAAA,CAAA,UAAM;4CACL,KAAK,sBAAsB,YAAY;4CACvC,MAAM,sBAAsB,iBAAiB;4CAC7C,MAAK;4CACL,OAAO;4CACP,WAAU;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAG,WAAU;8DACX,uCAAyB,6LAAC,gKAAA,CAAA,UAAQ;wDAAC,OAAO;wDAAK,QAAQ;;;;;+DAAS,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE,sBAAsB,iBAAiB;;;;;;8DAGtH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAc,EAAE;;;;;;8EAE7B,6LAAC;oEAAE,WAAU;8EACV,uCAAyB,6LAAC,gKAAA,CAAA,UAAQ;wEAAC,OAAO;wEAAK,QAAQ;;;;;+EAAS,sBAAsB,YAAY;;;;;;;;;;;;sEAGvG,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAc,EAAE;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EACV,uCAAyB,6LAAC,gKAAA,CAAA,UAAQ;wEAAC,OAAO;wEAAK,QAAQ;;;;;+EAAS,sBAAsB,cAAc;;;;;;;;;;;;sEAGzG,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAc,EAAE;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EACV,uCAAyB,6LAAC,gKAAA,CAAA,UAAQ;wEAAC,OAAO;wEAAI,QAAQ;;;;;+EAAS,sBAAsB,eAAe;;;;;;;;;;;;sEAGzG,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAc,EAAE;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;;wEACV,uCACC,6LAAC,gKAAA,CAAA,UAAQ;4EAAC,OAAO;4EAAI,QAAQ;;;;;iGAE7B,6LAAC,gIAAA,CAAA,UAAK;4EAAC,KAAK,sBAAsB,oBAAoB,uTAAA,CAAA,UAAY;4EAAE,KAAI;4EAAqB,QAAQ;4EAAI,OAAO;;;;;;wEAGjH,uCAAyB,6LAAC,gKAAA,CAAA,UAAQ;4EAAC,OAAO;4EAAK,QAAQ;;;;;mFAAS,sBAAsB,mBAAmB;;;;;;;;;;;;;wDAG7G,uCACC,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAI,OAAO;4DAAK,cAAc;;;;;mEAC9C,qCACF,6LAAC;4DAAI,WAAU;sEAEZ,gCAAgC,CAAC,4BAA4B,kBAAkB,uBAC9E;;kFACE,6LAAC,+IAAA,CAAA,UAAM;wEACL,WAAU;wEACV,SAAS;wEACT,UAAU,gBAAgB,CAAC;kFAE1B,6BAAe,6LAAC,yIAAA,CAAA,UAAM;;;;mFAAM,EAAE;;;;;;kFAEjC,6LAAC,+IAAA,CAAA,UAAM;wEACL,WAAU;wEACV,SAAS;wEACT,UAAU,gBAAgB,CAAC;kFAE1B,6BAAe,6LAAC,yIAAA,CAAA,UAAM;;;;mFAAM,EAAE;;;;;;;+EAInC,mFAAmF,iBACnF,6LAAC,+IAAA,CAAA,UAAM;gEACL,WAAU;gEACV,SAAS;gEACT,UAAU,4BAA4B,CAAC,sBAAsB;0EAE5D,yCAA2B,6LAAC,yIAAA,CAAA,UAAM;;;;2EAAM,EAAE;;;;;;;;;;mEAKjD;;;;;;;;;;;;;;;;;;;gCAMP,CAAC,8CACA,6LAAC;oCAAI,WAAU;oCAAO,SAAS;8CAC7B,cAAA,6LAAC;kDAAI,EAAE;;;;;;;;;;;gCAKV,8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,cAAc,WAAW;4CAAI,SAAS;sDAClD,EAAE;;;;;;sDAEL,6LAAC;4CAAG,WAAW,CAAC,cAAc,WAAW;4CAAI,SAAS;sDACnD,EAAE;;;;;;;;;;;;gCAIR,6BACC,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,wBACA,wCAAwC;sDACxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAK,OAAO;4DAAQ,cAAc;;;;;;;;;;;;;;;;8DAGxD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAK,OAAO;4DAAQ,cAAc;;;;;;;;;;;;;;;;;;;;;mDAIxD,yBAAyB,kBAC3B,uCAAuC;sDACvC,6LAAC;4CAAI,WAAU;;gDACZ,mBAAmB,gBAAgB,yBAAyB,IAAI,gBAAgB,yBAAyB,kBACxG,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2KAAA,CAAA,sBAAmB;4DAClB,WAAU;4DACV,OAAO;4DACP,MAAM,GAAG,eAAe,CAAC,CAAC;4DAC1B,aAAa;4DACb,aAAa;4DACb,QAAQ,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;gEAClB,UAAU;gEACV,eAAe;gEACf,YAAY;gEACZ,WAAW;gEACX,UAAU;gEACV,WAAW;gEACX,wBAAwB;4DAC1B;;;;;;;;;;;;;;;;8DAKR,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;4DACZ,mBACC,gBAAgB,YAAY,IAC5B,gBAAgB,YAAY,IAC5B,gBAAgB,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,mBACjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC,oJAAA,CAAA,UAAU;gFAAC,WAAU;;;;;;4EAAS;4EAAE,EAAE;;;;;;;kFAErC,6LAAC;wEAAG,WAAU;kFACX,iBAAiB,cAAc,cAAc,IAAI,CAAC,MAAc;4EAC/D,qBACE,6LAAC;;kGACC,6LAAC,4JAAA,CAAA,UAAkB;wFAAC,WAAU;;;;;;oFAAS;oFAAE;;+EADlC;;;;;wEAIb;;;;;;;;;;;;0EAIR,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,wJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;;;;;mDAMvB,mCAAmC;sDACnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAG,EAAE;;;;;;;;;;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,CAAC,sCACA,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAK,OAAO;4DAAQ,cAAc;;;;;iFAEpD;sEAIE,cAAA,6LAAC,yJAAA,CAAA,QAAK;gEAAC,MAAM;gEAAgB,SAAS;oEAAE,qBAAqB;gEAAM;;;;;;;;;;;;;;;;;8DAM3E,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,CAAC,sCACA,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAK,OAAO;4DAAQ,cAAc;;;;;iFAEpD,6LAAC,yJAAA,CAAA,MAAG;4DACF,MAAM;4DACN,SAAS;gEACP,qBAAqB;gEACrB,SAAS;oEAAE,QAAQ;wEAAE,SAAS;oEAAM;oEAAG,OAAO;wEAAE,SAAS;oEAAM;gEAAE;gEACjE,QAAQ;oEAAE,GAAG;wEAAE,MAAM;4EAAE,SAAS;wEAAM;oEAAE;gEAAE;4DAC5C;;;;;;;;;;;;;;;;;;;;;;sDAOV,6LAAC;4CAAI,WAAU;;gDACZ,CAAC,sCACA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAK,OAAO;4DAAQ,cAAc;;;;;;;;;;;;;;;2DAGtD,2BAA2B,wBAAwB,YAAY,IAAI,wBAAwB,YAAY,CAAC,MAAM,GAAG,kBACnH,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAmB,EAAE;;;;;;0EACnC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;0FAAc,EAAE;;;;;;0FAChC,6LAAC;gFAAK,WAAU;;kGACd,6LAAC,kJAAA,CAAA,UAAQ;;;;;oFAAG;oFAAE,wBAAwB,sBAAsB;oFAAC;;;;;;;;;;;;;oEAGhE,2BACC,wBAAwB,YAAY,IACpC,wBAAwB,YAAY,CAAC,MAAM,GAAG,KAC9C,wBAAwB,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM;wEAC9C,qBACE,6LAAC;4EAAe,WAAU;;8FACxB,6LAAC;oFAAK,WAAU;8FAAc,KAAK,UAAU;;;;;;8FAC7C,6LAAC;oFAAK,WAAU;8FACb,KAAK,WAAW,KAAK,mBACpB,6LAAC;wFAAK,WAAU;kGAAe,EAAE;;;;;6GAEjC;;0GACE,6LAAC,kJAAA,CAAA,UAAQ;;;;;4FAAG;4FAAE,KAAK,WAAW;4FAAC;;;;;;;;;2EAP9B;;;;;oEAab;;;;;;;;;;;;;;;;;2DAKR;gDAED,CAAC,sCACA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAK,OAAO;4DAAQ,cAAc;;;;;;;;;;;;;;;2DAIxD,2BACA,wBAAwB,YAAY,kBAClC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAmB,EAAE;;;;;;0EACnC,6LAAC;gEAAI,WAAU;0EACZ,2BACC,wBAAwB,YAAY,IACpC,wBAAwB,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC/C,6LAAC;wEAEC,WAAW,CAAC,UAAU,EAAE,mBAAmB,MAAM,UAAU,GAAG,WAAW,IAAI;wEAC7E,SAAS,IAAM,kBAAkB,MAAM,UAAU;kFAEhD,MAAM,UAAU;uEAJZ;;;;;;;;;;0EASb,6LAAC;gEAAI,WAAU;0EAAkB;;;;;;;;;;;;;;;;;8DAKzC,6LAAC;oDAAI,WAAU;8DACZ,CAAC,wBACA,wCAAwC;kEACxC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,gKAAA,CAAA,UAAQ;gEAAC,QAAQ;gEAAI,OAAO;gEAAO,cAAc;;;;;;0EAClD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4EAAC,QAAQ;4EAAK,OAAO;4EAAQ,cAAc;;;;;;;;;;;kFAEtD,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4EAAC,QAAQ;4EAAK,OAAO;4EAAQ,cAAc;;;;;;;;;;;kFAEtD,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4EAAC,QAAQ;4EAAK,OAAO;4EAAQ,cAAc;;;;;;;;;;;;;;;;;;;;;;+DAIxD,yBACF,mBACA,gBAAgB,0BAA0B,IAC1C,gBAAgB,0BAA0B,CAAC,eAAe,IAC1D,gBAAgB,0BAA0B,CAAC,eAAe,CAAC,MAAM,GAAG,IACpE,uCAAuC;kEACvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAmB,EAAE;;;;;;0EACnC,6LAAC;gEAAI,WAAU;0EACZ,gBAAgB,0BAA0B,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,gBAAgB,sBAC/E,6LAAC;wEAAgB,WAAU;kFACzB,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAAS,eAAe,KAAK;;;;;;8FAC3C,6LAAC;oFAAE,WAAU;8FAAe,eAAe,WAAW;;;;;;;;;;;;uEAHhD;;;;;;;;;;;;;;;+DAUhB,2GAA2G;kEAC3G,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;sEAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCASjB,CAAC,eACA,CAAC,0CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;wDAAC,QAAQ;wDAAI,OAAO;wDAAO,cAAc;;;;;;;;;;;8DAEpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAI,OAAO;4DAAO,WAAU;4DAAO,cAAc;;;;;;sEAEnE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gKAAA,CAAA,UAAQ;oEAAC,QAAQ;oEAAI,OAAO;oEAAI,cAAc;;;;;;8EAC/C,6LAAC,gKAAA,CAAA,UAAQ;oEAAC,QAAQ;oEAAI,OAAO;oEAAK,cAAc;;;;;;;;;;;;;;;;;;8DAGpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAI,OAAO;4DAAK,WAAU;4DAAO,cAAc;;;;;;sEACjE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gKAAA,CAAA,UAAQ;oEAAC,QAAQ;oEAAI,OAAO;oEAAK,WAAU;oEAAO,cAAc;;;;;;8EACjE,6LAAC,gKAAA,CAAA,UAAQ;oEAAC,QAAQ;oEAAI,OAAO;oEAAK,WAAU;oEAAO,cAAc;;;;;;8EACjE,6LAAC,gKAAA,CAAA,UAAQ;oEAAC,QAAQ;oEAAI,OAAO;oEAAK,WAAU;oEAAO,cAAc;;;;;;8EACjE,6LAAC,gKAAA,CAAA,UAAQ;oEAAC,QAAQ;oEAAI,OAAO;oEAAK,WAAU;oEAAO,cAAc;;;;;;8EACjE,6LAAC,gKAAA,CAAA,UAAQ;oEAAC,QAAQ;oEAAI,OAAO;oEAAK,WAAU;oEAAO,cAAc;;;;;;;;;;;;;;;;;;8DAGrE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAI,OAAO;4DAAK,WAAU;4DAAO,cAAc;;;;;;sEACjE,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC,gKAAA,CAAA,UAAQ;gEAAC,QAAQ;gEAAI,OAAO;gEAAO,WAAU;gEAAO,cAAc;gEAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;sDAInF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;wDAAC,QAAQ;wDAAI,OAAO;wDAAO,cAAc;;;;;;;;;;;8DAEpD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,gKAAA,CAAA,UAAQ;gEAAC,QAAQ;gEAAI,OAAO;gEAAI,cAAc;;;;;;0EAC/C,6LAAC,gKAAA,CAAA,UAAQ;gEAAC,QAAQ;gEAAI,OAAO;gEAAK,cAAc;;;;;;;;;;;;;;;;;8DAGpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAI,OAAO;4DAAK,cAAc;4DAAG,WAAU;;;;;;sEAC7D,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC,gKAAA,CAAA,UAAQ;gEAAC,QAAQ;gEAAI,OAAO;gEAAO,WAAU;gEAAO,cAAc;gEAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAMrF,6LAAC;oCAAI,WAAU;8CACZ,6BAA6B,0BAA0B,MAAM,GAAG,kBAC/D;;4CACG,0BAA0B,GAAG,CAAC,CAAC,aAAa,sBAC3C,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAG,WAAU;;oEACX,EAAE;oEAAS;oEAAE,YAAY,WAAW;oEAAC;oEAAE,EAAE;;;;;;;;;;;;sEAG9C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAiB,EAAE;;;;;;8EACjC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAK,YAAY,gBAAgB,IAAI,uTAAA,CAAA,UAAY;4EACjD,KAAK,EAAE;4EACP,WAAU;4EACV,OAAO;4EACP,QAAQ;;;;;;sFAEV,6LAAC;4EAAK,WAAU;sFAAoB,YAAY,eAAe;;;;;;wEAC9D,YAAY,OAAO,iBAClB,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;;oFACE;oFACA,IAAI,KAAK,YAAY,OAAO,EAAE,cAAc,CAAC,WAAW;wFACvD,MAAM;wFACN,OAAO;wFACP,KAAK;wFACL,MAAM;wFACN,QAAQ;oFACV;;;;;;;;;;;mFAGF;;;;;;;;;;;;;sEAGR,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAiB,EAAE;;;;;;8EACjC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,+IAAA,CAAA,UAAM;4EAAC,WAAU;;gFACf,EAAE;gFAAe;gFAAI,YAAY,cAAc;;;;;;;wEAEjD,OAAO,OAAO,CAAC,YAAY,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,WAAW,MAAM,iBAC9D,6LAAC,+IAAA,CAAA,UAAM;gFAAiB,WAAU;;oFAC/B;oFAAU;oFAAI,UAAU,KAAK,EAAE,aAAa;;+EADlC;;;;;;;;;;;;;;;;;sEAMnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAiB,EAAE;;;;;;8EACjC,6LAAC;oEAAG,WAAU;8EACX,YAAY,gBAAgB,EAAE,UAAU,IAAI,CAAC,MAAc;wEAC1D,qBACE,6LAAC;4EAAe,WAAU;sFACvB;2EADM;;;;;oEAIb;;;;;;;;;;;;;mDAvDI;;;;;4CA6DX,uCACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAG,WAAU;sEAAmB,EAAE;;;;;;;;;;;kEAErC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KAAK,UAAU,SAAS,uTAAA,CAAA,UAAY;oEACpC,KAAK,EAAE;oEACP,WAAU;oEACV,OAAO;oEACP,QAAQ;;;;;;8EAEV,6LAAC;oEAAK,WAAU;;wEACb,UAAU;wEAAW;wEAAE,UAAU;;;;;;;;;;;;;;;;;;kEAIxC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAiB,EAAE;;;;;;0EACjC,6LAAC;gEAAG,WAAU;0EACX,sBAAsB,UAAU,EAAE,IAAI,CAAC,MAAc;oEACpD,qBACE,6LAAC;wEAAe,WAAU;kFACvB;uEADM;;;;;gEAIb;;;;;;;;;;;;;;;;;;4CAOP,UAAU,iBAAiB,WAAW,uBAAuB,MAAM,GAAG,mBACrE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAG,WAAU;sEAAmB,EAAE;;;;;;;;;;;oDAEpC,uBAAuB,GAAG,CAAC,CAAC,6BAC3B,6LAAC;4DAAsE,WAAU;;8EAC/E,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,gIAAA,CAAA,UAAK;gFACJ,KAAK,aAAa,gBAAgB,IAAI,uTAAA,CAAA,UAAY;gFAClD,KAAK,EAAE;gFACP,WAAU;gFACV,OAAO;gFACP,QAAQ;;;;;;0FAEV,6LAAC;gFAAK,WAAU;;oFACb,aAAa,eAAe;oFAAC;oFAAU,aAAa,WAAW;;;;;;;;;;;;;;;;;;gEAIrE,aAAa,QAAQ,EAAE,4BACtB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAiB,EAAE;;;;;;sFACjC,6LAAC;4EAAG,WAAU;sFACX,aAAa,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAc,+BACnD,6LAAC;oFAAwB,WAAU;8FAChC;mFADM;;;;;;;;;;;;;;;;;2DApBT,GAAG,aAAa,aAAa,CAAC,CAAC,EAAE,aAAa,WAAW,EAAE;;;;;;;;;;;;qEAiC7E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;sDAAG,EAAE;;;;;;;;;;;;;;;wCAIb;;;;;;;;;;;;;;;;;;;;AAMf;GA37CM;;QAKW,qIAAA,CAAA,YAAS;QACP,4JAAA,CAAA,cAAW;QAClB,qKAAA,CAAA,kBAAe;;;KAPrB;uCA47CS", "debugId": null}}, {"offset": {"line": 4259, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4265, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/candidate-profile/%5BjobApplicationId%5D/page.tsx"], "sourcesContent": ["\"use client\";\nimport CandidateProfile from \"@/components/views/conductInterview/CandidateProfile\";\nimport React from \"react\";\n\nconst page = ({ params }: { params: Promise<{ jobApplicationId: string }> }) => {\n  return (\n    <div>\n      <CandidateProfile params={params} />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO,CAAC,EAAE,MAAM,EAAqD;IACzE,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,UAAgB;YAAC,QAAQ;;;;;;;;;;;AAGhC;uCAEe", "debugId": null}}, {"offset": {"line": 4292, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}