{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/LinkedinIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype LinkedinIconProps = {\n  className?: string;\n};\n\nfunction LinkedinIcon({ className }: LinkedinIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"85\" height=\"24\" viewBox=\"0 0 85 24\" fill=\"none\" className={className}>\n      <g clipPath=\"url(#clip0_9859_6933)\">\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M3.18711 4.8521H0.000976562V18.98H8.80458V16.297H3.18711V4.8521ZM11.5497 4.28654C10.5227 4.28654 9.85178 4.97815 9.85178 5.87952C9.85178 6.75964 10.5016 7.47232 11.5078 7.47232C12.5766 7.47232 13.2264 6.75946 13.2264 5.87952C13.2057 4.97815 12.5766 4.28654 11.5497 4.28654ZM9.93569 18.98H13.122V8.73021H9.93569V18.98ZM21.0862 8.49955C19.3675 8.49955 18.3405 9.48465 17.9213 10.1556H17.8583L17.7113 8.73021H14.9445C14.9865 9.65265 15.0283 10.7215 15.0283 12.0001V18.9802H18.2146V13.0693C18.2146 12.7756 18.2355 12.4823 18.3192 12.2725C18.5499 11.6859 19.074 11.0778 19.9545 11.0778C21.1072 11.0778 21.5684 11.9792 21.5684 13.2996V18.98H24.7547V12.9223C24.7545 9.90401 23.1824 8.49955 21.0862 8.49955ZM36.5337 8.73021H32.698L30.6228 11.8114C30.3712 12.1888 30.1199 12.6079 29.8892 13.048H29.8473V4.09766H26.6611V18.9799H29.8474V15.7726L30.6438 14.7667L33.1384 18.9799H37.0581L32.866 12.8802L36.5337 8.73021ZM41.6688 8.49955C38.1058 8.49955 36.4916 11.3713 36.4916 13.9703C36.4916 17.1775 38.4831 19.1894 41.9627 19.1894C43.3461 19.1894 44.6245 18.9799 45.6726 18.5396L45.2538 16.3807C44.394 16.6742 43.5137 16.821 42.4238 16.821C40.9355 16.821 39.6149 16.1918 39.5312 14.8715H46.1337C46.1757 14.6408 46.2385 14.1586 46.2385 13.6139C46.2383 11.0778 44.981 8.49955 41.6688 8.49955ZM39.5099 12.6917C39.5936 11.8535 40.1389 10.6165 41.4803 10.6165C42.9476 10.6165 43.2827 11.9162 43.2827 12.6917H39.5099ZM57.431 15.9198V4.09784H54.2446V9.63158H54.2027C53.7414 8.9398 52.7773 8.47867 51.4987 8.47867C49.0463 8.47867 46.8874 10.4908 46.9083 13.9494C46.9083 17.1357 48.8578 19.2109 51.2892 19.2109C52.6095 19.2109 53.8672 18.6237 54.4964 17.4917H54.559L54.6847 18.98H57.5147C57.4727 18.2883 57.431 17.0725 57.431 15.9198ZM54.2446 14.4107C54.2446 14.6621 54.2239 14.9136 54.1818 15.1443C53.9931 16.0454 53.2387 16.6742 52.3162 16.6742C50.9958 16.6742 50.1361 15.5841 50.1361 13.8448C50.1361 12.2305 50.8698 10.9312 52.3375 10.9312C53.3227 10.9312 54.0142 11.6441 54.2029 12.4823C54.2448 12.6708 54.2448 12.9013 54.2448 13.0693V14.4107H54.2446Z\"\n          fill=\"#181818\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M81.8876 0.0175781H62.9813C61.6624 0.0175781 60.5938 1.09007 60.5938 2.413V21.5839C60.5938 22.907 61.6624 23.9793 62.9813 23.9793H81.8874C83.2063 23.9793 84.275 22.9068 84.275 21.5839V2.41318C84.2752 1.09025 83.2065 0.0175781 81.8876 0.0175781ZM68.1926 18.9772H65.0065V8.72738H68.1926V18.9772ZM66.5785 7.46949C65.5726 7.46949 64.9227 6.75663 64.9227 5.87669C64.9227 4.97532 65.5933 4.28371 66.6205 4.28371C67.6474 4.28371 68.2765 4.97532 68.2972 5.87669C68.2972 6.75682 67.6474 7.46949 66.5785 7.46949ZM79.8253 18.9772H76.6393V13.2967C76.6393 11.9764 76.1778 11.075 75.0249 11.075C74.1446 11.075 73.6208 11.6829 73.3901 12.2697C73.3064 12.4794 73.2852 12.7728 73.2852 13.0664V18.9774H70.0992V11.9972C70.0992 10.7186 70.0573 9.64982 70.0155 8.72738H72.7823L72.9292 10.1527H72.992C73.4112 9.48182 74.4384 8.49672 76.1571 8.49672C78.2532 8.49672 79.8251 9.90118 79.8251 12.9195V18.9772H79.8253Z\"\n          fill=\"#006699\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_9859_6933\">\n          <rect width=\"84.2748\" height=\"24\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default LinkedinIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,aAAa,EAAE,SAAS,EAAqB;IACpD,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,6LAAC;gBAAE,UAAS;;kCACV,6LAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,6LAAC;0BACC,cAAA,6LAAC;oBAAS,IAAG;8BACX,cAAA,6LAAC;wBAAK,OAAM;wBAAU,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAKjD;KAxBS;uCA0BM", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/ApplicationsSourcesModal.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport Button from \"../formElements/Button\";\nimport LinkedinIcon from \"../svgComponents/LinkedinIcon\";\n\ninterface IProps {\n  onCancel?: () => void;\n}\n\nconst ApplicationsSourcesModal: React.FC<IProps> = ({ onCancel }) => {\n  return (\n    <div className=\"modal theme-modal show-modal applications-sources-modal\">\n      <div className=\"modal-dialog modal-dialog-centered\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-center\">\n            <h2 className=\"text-left\">Applications Sources</h2>\n          </div>\n          <div className=\"modal-body\">\n            <div className=\"applications-list\">\n              <div className=\"item\">\n                <div className=\"left-item\">\n                  <LinkedinIcon />\n                </div>\n                <div className=\"item-right\">42 Applicants</div>\n              </div>\n              <div className=\"item\">\n                <div className=\"left-item\">\n                  <LinkedinIcon />\n                </div>\n                <div className=\"item-right\">42 Applicants</div>\n              </div>\n              <div className=\"item\">\n                <div className=\"left-item\">\n                  <LinkedinIcon />\n                </div>\n                <div className=\"item-right\">42 Applicants</div>\n              </div>\n              <div className=\"item\">\n                <div className=\"left-item\">Other</div>\n                <div className=\"item-right\">42 Applicants</div>\n              </div>\n            </div>\n\n            <div className=\"action-btn justify-content-center\">\n              <Button className=\"primary-btn rounded-md w-100\" onClick={onCancel}>\n                Ok\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\nexport default ApplicationsSourcesModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASA,MAAM,2BAA6C,CAAC,EAAE,QAAQ,EAAE;IAC9D,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAY;;;;;;;;;;;kCAE5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,sJAAA,CAAA,UAAY;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,sJAAA,CAAA,UAAY;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,sJAAA,CAAA,UAAY;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+IAAA,CAAA,UAAM;oCAAC,WAAU;oCAA+B,SAAS;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlF;KA5CM;uCA6CS", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,6LAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;KAD9C;AAIN;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,6LAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,6LAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,6LAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,6LAAC,+IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textbox.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\n\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface CommonInputProps extends InputHTMLAttributes<HTMLInputElement> {\n  iconClass?: string;\n  align?: \"left\" | \"right\";\n  children?: React.ReactNode;\n}\n\ninterface TextboxProps<T extends FieldValues> extends CommonInputProps {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textbox<T extends FieldValues>({ children, control, name, iconClass, align, ...props }: TextboxProps<T>) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <Controller\n        control={control}\n        name={name}\n        render={({ field }) => (\n          <input\n            {...props}\n            value={field.value}\n            onChange={(e) => {\n              field.onChange(e);\n              props.onChange?.(e);\n            }}\n            aria-label=\"\"\n          />\n        )}\n        defaultValue={\"\" as T[typeof name]}\n      />\n      {children}\n    </div>\n  );\n}\n\nexport function CommonInput({ iconClass, children, align, onChange, ...props }: CommonInputProps) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <input {...props} onChange={onChange} />\n\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAae,SAAS,QAA+B,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAwB;IAC7H,qBACE,6LAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,6LAAC,iKAAA,CAAA,aAAU;gBACT,SAAS;gBACT,MAAM;gBACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC;wBACE,GAAG,KAAK;wBACT,OAAO,MAAM,KAAK;wBAClB,UAAU,CAAC;4BACT,MAAM,QAAQ,CAAC;4BACf,MAAM,QAAQ,GAAG;wBACnB;wBACA,cAAW;;;;;;gBAGf,cAAc;;;;;;YAEf;;;;;;;AAGP;KAtBwB;AAwBjB,SAAS,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAyB;IAC9F,qBACE,6LAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,6LAAC;gBAAO,GAAG,KAAK;gBAAE,UAAU;;;;;;YAE3B;;;;;;;AAGP;MARgB", "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/SearchIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction SearchIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\">\n      <g opacity=\"0.7\">\n        <path\n          d=\"M28.2109 18.8274C28.2109 20.6833 27.6605 22.4976 26.6295 24.0407C25.5984 25.5839 24.1329 26.7867 22.4182 27.497C20.7036 28.2072 18.8168 28.3931 16.9965 28.0311C15.1762 27.6691 13.5042 26.7755 12.1917 25.4632C10.8793 24.1509 9.9855 22.479 9.62331 20.6587C9.26111 18.8384 9.4468 16.9517 10.1569 15.237C10.867 13.5222 12.0696 12.0566 13.6127 11.0253C15.1557 9.99409 16.9699 9.44356 18.8259 9.44336C20.0583 9.44323 21.2786 9.68586 22.4173 10.1574C23.5559 10.6289 24.5905 11.3201 25.462 12.1915C26.3335 13.0629 27.0248 14.0974 27.4965 15.236C27.9681 16.3746 28.2109 17.595 28.2109 18.8274Z\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path d=\"M30.557 30.559L25.457 25.459\" stroke=\"#333333\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n      </g>\n    </svg>\n  );\n}\n\nexport default SearchIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,6LAAC;YAAE,SAAQ;;8BACT,6LAAC;oBACC,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEjB,6LAAC;oBAAK,GAAE;oBAA+B,QAAO;oBAAU,aAAY;oBAAM,eAAc;oBAAQ,gBAAe;;;;;;;;;;;;;;;;;AAIvH;KAfS;uCAiBM", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ThreeDotsIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction ThreeDotsIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M12.0001 19.2C13.3256 19.2 14.4001 20.2745 14.4001 21.6C14.4001 22.9255 13.3256 24 12.0001 24C10.6746 24 9.6001 22.9255 9.6001 21.6C9.6001 20.2745 10.6746 19.2 12.0001 19.2ZM12.0001 9.60005C13.3256 9.60005 14.4001 10.6746 14.4001 12C14.4001 13.3255 13.3256 14.4 12.0001 14.4C10.6746 14.4 9.6001 13.3255 9.6001 12C9.6001 10.6746 10.6746 9.60005 12.0001 9.60005ZM12.0001 0C13.3256 0 14.4001 1.07452 14.4001 2.39999C14.4001 3.72546 13.3256 4.79998 12.0001 4.79998C10.6746 4.79998 9.6001 3.72546 9.6001 2.39999C9.6001 1.07452 10.6746 0 12.0001 0Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default ThreeDotsIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,6LAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KAXS;uCAaM", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KATS;uCAWM", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/jobRequirements/jobServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse, IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport { GenerateJobSchema } from \"@/validations/jobRequirementsValidations\";\n\n/**\n * Saves job details to the backend\n * @param formData Job requirement form data\n * @returns Job details response\n */\nexport const saveJobDetails = (formData: GenerateJobSchema): Promise<ApiResponse> => {\n  return http.post(endpoint.jobRequirements.SAVE_JOB_DETAILS, formData);\n};\n\nexport interface Job {\n  id: number;\n  title: string;\n  jobId: string;\n  postedDate: string;\n  updatedDate: string;\n  isActive: boolean;\n  applicationCount?: number;\n  finalJobDescriptionHtml: string;\n}\n\nexport type JobsApiResponse = ApiResponse<Job[]>;\n\n/**\n * Fetch paginated jobs metadata from the API\n * @param data Object with pagination and filter params\n * @returns Promise resolving to JobsApiResponse\n */\nexport const fetchJobsMeta = (data: {\n  page?: number;\n  limit?: number;\n  searchStr?: string;\n  isActive?: boolean;\n  applicationCount?: number;\n}): Promise<IApiResponseCommonInterface<Job[]>> => {\n  // Use 'params' to send query parameters in axios GET\n  return http.get(endpoint.jobRequirements.GET_JOBS_META, { ...data });\n};\n\n/**\n * Fetches the HTML description of a job\n * @param id Job ID\n * @returns Promise resolving to ApiResponse<Job>\n */\nexport const getJobHtmlDescription = (id: string): Promise<ApiResponse<Job>> => {\n  return http.get(endpoint.jobRequirements.GET_JOB_HTML_DESCRIPTION, { id });\n};\n\n/**\n * Updates the HTML description of a job\n * @param htmlData Object containing job ID and HTML description\n * @returns Promise resolving to ApiResponse<Job>\n */\nexport const updateJobDescription = (htmlData: { jobId: number; finalJobDescriptionHtml: string }): Promise<ApiResponse<Job>> => {\n  return http.put(endpoint.jobRequirements.UPDATE_JOB_DESCRIPTION, htmlData);\n};\n\n/**\n * Generates PDF from job title and editor content\n * @param data Object containing job title and editor content\n * @returns Promise resolving to ApiResponse\n */\nexport const generatePDF = (data: { jobTitle: string; editorContent: string }): Promise<ApiResponse> => {\n  return http.post(endpoint.jobRequirements.GENERATE_PDF, data);\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AASO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,gBAAgB,EAAE;AAC9D;AAoBO,MAAM,gBAAgB,CAAC;IAO5B,qDAAqD;IACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,aAAa,EAAE;QAAE,GAAG,IAAI;IAAC;AACpE;AAOO,MAAM,wBAAwB,CAAC;IACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE;QAAE;IAAG;AAC1E;AAOO,MAAM,uBAAuB,CAAC;IACnC,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE;AACnE;AAOO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,YAAY,EAAE;AAC1D", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/jobRequirements/updateJobServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\n\n/**\n * Updates the job's active status on the backend\n * @param jobId ID of the job to update\n * @param status Boolean indicating if job is active or not\n * @returns Promise resolving to ApiResponse\n */\nexport const updateJobStatus = (jobId: number, status: boolean): Promise<ApiResponse> => {\n  // Assuming endpoint.jobRequirements.UPDATE_JOB_STATUS is something like '/jobs/updateJob'\n  return http.put(`${endpoint.jobRequirements.UPDATE_JOB_STATUS}/${jobId}`, { status });\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AASO,MAAM,kBAAkB,CAAC,OAAe;IAC7C,0FAA0F;IAC1F,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,GAAG,+HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,EAAE,OAAO,EAAE;QAAE;IAAO;AACrF", "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard__stats_header\": \"commonPage-module-scss-module__em0r7a__dashboard__stats_header\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/skeletons/TableSkeleton.tsx"], "sourcesContent": ["import React from \"react\";\nimport Skeleton from \"react-loading-skeleton\";\nimport \"react-loading-skeleton/dist/skeleton.css\";\n\nconst TableSkeleton = ({ rows = 3, cols = 3, colWidths = \"120,80,100\" }) => {\n  const columnWidths = colWidths.split(\",\").map((w) => w.trim());\n\n  return (\n    <tbody>\n      {[...Array(rows)].map((_, rowIndex) => (\n        <tr key={`loader-row-${rowIndex}`}>\n          {[...Array(cols)].map((_, colIndex) => (\n            <td key={`loader-col-${colIndex}`} className=\"text-center\">\n              <Skeleton width={columnWidths[colIndex] || 80} height={20} circle={false} />\n            </td>\n          ))}\n        </tr>\n      ))}\n    </tbody>\n  );\n};\n\nexport default TableSkeleton;\n"], "names": [], "mappings": ";;;;AACA;;;;AAGA,MAAM,gBAAgB,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,YAAY,YAAY,EAAE;IACrE,MAAM,eAAe,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;IAE3D,qBACE,6LAAC;kBACE;eAAI,MAAM;SAAM,CAAC,GAAG,CAAC,CAAC,GAAG,yBACxB,6LAAC;0BACE;uBAAI,MAAM;iBAAM,CAAC,GAAG,CAAC,CAAC,GAAG,yBACxB,6LAAC;wBAAkC,WAAU;kCAC3C,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4BAAC,OAAO,YAAY,CAAC,SAAS,IAAI;4BAAI,QAAQ;4BAAI,QAAQ;;;;;;uBAD5D,CAAC,WAAW,EAAE,UAAU;;;;;eAF5B,CAAC,WAAW,EAAE,UAAU;;;;;;;;;;AAUzC;KAhBM;uCAkBS", "debugId": null}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/resume/ActiveJobs.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n\"use client\";\r\n\r\n// Internal libraries\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { AuthState } from \"@/redux/slices/authSlice\";\r\n\r\n// External libraries\r\nimport { useRouter } from \"next/navigation\";\r\nimport InfiniteScroll from \"react-infinite-scroll-component\";\r\nimport { debounce } from \"lodash\";\r\n\r\n// Components\r\nimport ApplicationsSourcesModal from \"@/components/commonModals/ApplicationsSourcesModal\";\r\nimport Button from \"@/components/formElements/Button\";\r\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\r\nimport Textbox from \"@/components/formElements/Textbox\";\r\nimport SearchIcon from \"@/components/svgComponents/SearchIcon\";\r\nimport ThreeDotsIcon from \"@/components/svgComponents/ThreeDotsIcon\";\r\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\r\n\r\n// Services\r\nimport { fetchJobsMeta, Job } from \"@/services/jobRequirements/jobServices\";\r\nimport { updateJobStatus } from \"@/services/jobRequirements/updateJobServices\";\r\n\r\n// Constants\r\nimport { DEFAULT_LIMIT, PERMISSION } from \"@/constants/commonConstants\";\r\n\r\n// CSS\r\nimport style from \"@/styles/commonPage.module.scss\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport \"react-loading-skeleton/dist/skeleton.css\";\r\nimport TableSkeleton from \"../skeletons/TableSkeleton\";\r\n// import Loader from \"@/components/loader/Loader\";\r\nimport ROUTES from \"@/constants/routes\";\r\n\r\nconst ActiveJobs = () => {\r\n  const router = useRouter();\r\n  const { control } = useForm({ mode: \"onChange\" });\r\n  const [showApplicationsSourcesModal, setShowApplicationsSourcesModal] = useState(false);\r\n  const [jobs, setJobs] = useState<Job[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [offset, setOffset] = useState(0);\r\n  const [hasMore, setHasMore] = useState(true);\r\n  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);\r\n  const [searchStr, setSearchStr] = useState(\"\");\r\n  const t = useTranslations();\r\n  const dropdownRefs = React.useRef<{ [key: string]: HTMLUListElement | null }>({});\r\n\r\n  console.log(\"offset==========\", offset);\r\n\r\n  // Get user permissions from Redux store\r\n  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];\r\n\r\n  // Check if user has permissions for various actions\r\n  const hasManualResumeScreeningPermission = userPermissions.includes(PERMISSION.MANUAL_RESUME_SCREENING);\r\n  const hasArchiveRestoreJobPermission = userPermissions.includes(PERMISSION.ARCHIVE_RESTORE_JOB_POSTS);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (\r\n        openDropdownId &&\r\n        dropdownRefs.current[openDropdownId] &&\r\n        !dropdownRefs.current[openDropdownId]?.contains(event.target as Node) &&\r\n        !(event.target as Element).closest(\".applications-sources-modal\")\r\n      ) {\r\n        setOpenDropdownId(null);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"click\", handleClickOutside); // changed from \"mousedown\" to \"click\"\r\n    return () => {\r\n      document.removeEventListener(\"click\", handleClickOutside); // changed from \"mousedown\" to \"click\"\r\n    };\r\n  }, [openDropdownId]);\r\n\r\n  useEffect(() => {\r\n    setJobs([]);\r\n    setOffset(0);\r\n    setHasMore(true);\r\n    fetchMoreJobs(\"\", 0, true);\r\n  }, []);\r\n\r\n  const fetchMoreJobs = async (searchValue: string, currentOffset = 0, reset = false) => {\r\n    try {\r\n      setLoading(true);\r\n      // If it's a search or reset, clear the jobs first to show the skeleton\r\n      if (reset) {\r\n        setJobs([]);\r\n      }\r\n      const result = await fetchJobsMeta({\r\n        page: currentOffset,\r\n        limit: DEFAULT_LIMIT,\r\n        searchStr: searchValue,\r\n        isActive: true,\r\n        applicationCount: 0, // Assuming you want to filter by application count\r\n      });\r\n\r\n      if (result?.data?.success && Array.isArray(result.data.data)) {\r\n        const jobsFetched = result.data.data;\r\n\r\n        setJobs((prevJobs) => (reset ? jobsFetched : [...prevJobs, ...jobsFetched]));\r\n\r\n        if (jobsFetched.length < DEFAULT_LIMIT) {\r\n          setHasMore(false);\r\n        } else {\r\n          setHasMore(true);\r\n        }\r\n\r\n        setOffset(currentOffset + jobsFetched.length);\r\n      } else {\r\n        setHasMore(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(t(\"error_fetching_jobs\"), error);\r\n      setHasMore(false);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleArchiveJob = async (jobId: number) => {\r\n    setOpenDropdownId(null);\r\n    try {\r\n      await updateJobStatus(jobId, false);\r\n      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));\r\n    } catch {\r\n      console.error(t(\"archive_job_failed\"));\r\n    }\r\n  };\r\n\r\n  const handleSearchInputChange = (event: string) => {\r\n    const searchString = event.trim();\r\n    setSearchStr(searchString);\r\n    // Set loading to true here to show skeleton immediately during search\r\n    setLoading(true);\r\n    fetchMoreJobs(searchString, 0, true);\r\n  };\r\n\r\n  /**\r\n   * Navigates to JobEditor with the selected job data for editing\r\n   * @param {Job} job - The job to be edited\r\n   */\r\n  const handleEditJob = (job: Job) => {\r\n    // Close the dropdown\r\n    setOpenDropdownId(null);\r\n\r\n    // Navigate to JobEditor with job data for editing\r\n    router.push(`${ROUTES.JOBS.JOB_EDITOR}?jobId=${job.id}`);\r\n  };\r\n\r\n  const debouncedHandleSearchInputChange = debounce(handleSearchInputChange, 1000);\r\n\r\n  return (\r\n    <>\r\n      <section className={`${style.resume_page} ${style.candidates_list_page}`}>\r\n        <div className=\"container\">\r\n          <div className=\"common-page-header\">\r\n            <div className=\"common-page-head-section\">\r\n              <div className=\"main-heading\">\r\n                <h2>\r\n                  <BackArrowIcon onClick={() => router.back()} />\r\n                  {t(\"application_for\")} <span> {t(\"active_jobs\")} </span>\r\n                </h2>\r\n                <div className=\"right-action\">\r\n                  <InputWrapper className=\"mb-0 w-100 search-input\">\r\n                    <div className=\"icon-align right\">\r\n                      <Textbox\r\n                        className=\"form-control w-100\"\r\n                        control={control}\r\n                        name=\"search\"\r\n                        type=\"text\"\r\n                        onChange={(e) => debouncedHandleSearchInputChange(e.target.value)}\r\n                        placeholder={t(\"search_using_jobId_jobTitle\")}\r\n                      >\r\n                        <InputWrapper.Icon>\r\n                          <SearchIcon />\r\n                        </InputWrapper.Icon>\r\n                      </Textbox>\r\n                    </div>\r\n                  </InputWrapper>\r\n                  <Button className=\"primary-btn rounded-md button-sm\" onClick={() => router.push(ROUTES.JOBS.HIRING_TYPE)}>\r\n                    {t(\"add_new_job\")}\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={style.candidates_list_section}>\r\n            <div className=\"table-responsive\">\r\n              <InfiniteScroll\r\n                dataLength={jobs.length}\r\n                next={() => fetchMoreJobs(searchStr, offset)}\r\n                hasMore={hasMore}\r\n                height={window.innerHeight - 250}\r\n                loader={\r\n                  loading && (\r\n                    <table className=\"table w-100\">\r\n                      <TableSkeleton rows={3} cols={5} colWidths=\"120,80,100,24,24\" />\r\n                    </table>\r\n                  )\r\n                }\r\n                endMessage={\r\n                  !loading && jobs.length ? (\r\n                    <table className=\"table w-100\">\r\n                      <tbody>\r\n                        <tr>\r\n                          <td colSpan={5} style={{ textAlign: \"center\", backgroundColor: \"#fff\" }}>\r\n                            {t(\"no_more_jobs_to_fetch\")}\r\n                          </td>\r\n                        </tr>\r\n                      </tbody>\r\n                    </table>\r\n                  ) : null\r\n                }\r\n              >\r\n                <table className=\"table w-100 overflow-auto mb-0\">\r\n                  <thead>\r\n                    <tr>\r\n                      <th>{t(\"job_id\")}</th>\r\n                      <th>{t(\"job_title\")}</th>\r\n                      <th>{t(\"posted_on\")}</th>\r\n                      <th className=\"text-center\">#{t(\"application_submitted\")}</th>\r\n                      <th className=\"text-center\">{t(\"actions\")}</th>\r\n                    </tr>\r\n                  </thead>\r\n                  {jobs.length > 0 ? (\r\n                    <tbody>\r\n                      {jobs.map((job) => {\r\n                        // Check if this is an empty job with null values\r\n                        const isEmptyJob =\r\n                          job.id === null &&\r\n                          job.jobId === null &&\r\n                          job.title === null &&\r\n                          job.postedDate === null &&\r\n                          job.isActive === null &&\r\n                          job.finalJobDescriptionHtml === null;\r\n\r\n                        if (isEmptyJob) {\r\n                          return (\r\n                            <tr key=\"empty-job\">\r\n                              <td colSpan={5} style={{ textAlign: \"center\" }}>\r\n                                {t(\"no_active_job_found\")}\r\n                              </td>\r\n                            </tr>\r\n                          );\r\n                        }\r\n\r\n                        const date = job.postedDate ? new Date(job.postedDate) : null;\r\n                        const formattedDate =\r\n                          date && !isNaN(date.getTime())\r\n                            ? date.toLocaleDateString(\"en-US\", {\r\n                                year: \"numeric\",\r\n                                month: \"short\",\r\n                                day: \"numeric\",\r\n                              })\r\n                            : \"—\";\r\n\r\n                        return (\r\n                          <tr key={job.id}>\r\n                            <td>{job?.jobId}</td>\r\n                            <td>{job?.title}</td>\r\n                            <td>{formattedDate}</td>\r\n                            <td align=\"center\">{job?.applicationCount}</td>\r\n                            <td className=\"text-center\">\r\n                              <div className=\"position-relative  \">\r\n                                <Button\r\n                                  className=\"clear-btn p-0 m-auto\"\r\n                                  onClick={() => setOpenDropdownId((prevId) => (prevId === String(job.id) ? null : String(job.id)))}\r\n                                >\r\n                                  <ThreeDotsIcon />\r\n                                </Button>\r\n                                {openDropdownId === String(job.id) && (\r\n                                  <ul\r\n                                    className=\"custom-dropdown\"\r\n                                    ref={(element) => {\r\n                                      if (element) {\r\n                                        dropdownRefs.current[String(job.id)] = element;\r\n                                      }\r\n                                    }}\r\n                                  >\r\n                                    {job && job.applicationCount && job.applicationCount > 0 && (\r\n                                      <li\r\n                                        onClick={() => {\r\n                                          console.log(\"job\", job);\r\n                                          router.push(\r\n                                            `${ROUTES.SCREEN_RESUME.CANDIDATE_LIST}/${job.id}?title=${encodeURIComponent(job.title)}&jobUniqueId=${encodeURIComponent(job.jobId)}`\r\n                                          );\r\n                                        }}\r\n                                      >\r\n                                        {t(\"view_all_candidates\")}\r\n                                      </li>\r\n                                    )}\r\n                                    {hasManualResumeScreeningPermission && (\r\n                                      <li\r\n                                        onClick={() =>\r\n                                          router.push(\r\n                                            `${ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${job.id}?title=${encodeURIComponent(job.title)}&jobUniqueId=${encodeURIComponent(job.jobId)}`\r\n                                          )\r\n                                        }\r\n                                      >\r\n                                        {t(\"screen_resume_manually\")}\r\n                                      </li>\r\n                                    )}\r\n                                    <li onClick={() => setShowApplicationsSourcesModal(true)}>View Application Sources</li>\r\n                                    {job && job.applicationCount && job.applicationCount > 0 && (\r\n                                      <li\r\n                                        onClick={() => {\r\n                                          router.push(\r\n                                            `${ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION}/${job.id}` +\r\n                                              `?title=${job.title}&jobUniqueId=${job.jobId}`\r\n                                          );\r\n                                        }}\r\n                                      >\r\n                                        {t(\"view_panding_action\")}\r\n                                      </li>\r\n                                    )}\r\n                                    {hasArchiveRestoreJobPermission && (\r\n                                      <li onClick={() => handleArchiveJob(job.id)} style={{ cursor: \"pointer\" }}>\r\n                                        {t(\"archive_job\")}\r\n                                      </li>\r\n                                    )}\r\n                                    <li onClick={() => handleEditJob(job)} style={{ cursor: \"pointer\" }}>\r\n                                      {t(\"edit_job\")}\r\n                                    </li>\r\n                                  </ul>\r\n                                )}\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        );\r\n                      })}\r\n                    </tbody>\r\n                  ) : (\r\n                    !loading && (\r\n                      <tbody>\r\n                        <tr>\r\n                          <td colSpan={5} style={{ textAlign: \"center\" }}>\r\n                            {t(\"no_active_job_found\")}\r\n                          </td>\r\n                        </tr>\r\n                      </tbody>\r\n                    )\r\n                  )}\r\n                </table>\r\n              </InfiniteScroll>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {showApplicationsSourcesModal && <ApplicationsSourcesModal onCancel={() => setShowApplicationsSourcesModal(false)} />}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ActiveJobs;\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAG9C,qBAAqB;AACrB;AACA;AACA;AAGA,qBAAqB;AACrB;AACA;AACA;AAEA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,WAAW;AACX;AACA;AAEA,YAAY;AACZ;AAEA,MAAM;AACN;AACA;AAEA;AACA,mDAAmD;AACnD;;;AAnCA;;;;;;;;;;;;;;;;;;;;;;AAqCA,MAAM,aAAa;;IACjB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QAAE,MAAM;IAAW;IAC/C,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,MAAM,CAA6C,CAAC;IAE/E,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW,IAAI,EAAE;;IAEhG,oDAAoD;IACpD,MAAM,qCAAqC,gBAAgB,QAAQ,CAAC,sIAAA,CAAA,aAAU,CAAC,uBAAuB;IACtG,MAAM,iCAAiC,gBAAgB,QAAQ,CAAC,sIAAA,CAAA,aAAU,CAAC,yBAAyB;IAEpG,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;2DAAqB,CAAC;oBAC1B,IACE,kBACA,aAAa,OAAO,CAAC,eAAe,IACpC,CAAC,aAAa,OAAO,CAAC,eAAe,EAAE,SAAS,MAAM,MAAM,KAC5D,CAAC,AAAC,MAAM,MAAM,CAAa,OAAO,CAAC,gCACnC;wBACA,kBAAkB;oBACpB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,SAAS,qBAAqB,sCAAsC;YAC9F;wCAAO;oBACL,SAAS,mBAAmB,CAAC,SAAS,qBAAqB,sCAAsC;gBACnG;;QACF;+BAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,QAAQ,EAAE;YACV,UAAU;YACV,WAAW;YACX,cAAc,IAAI,GAAG;QACvB;+BAAG,EAAE;IAEL,MAAM,gBAAgB,OAAO,aAAqB,gBAAgB,CAAC,EAAE,QAAQ,KAAK;QAChF,IAAI;YACF,WAAW;YACX,uEAAuE;YACvE,IAAI,OAAO;gBACT,QAAQ,EAAE;YACZ;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE;gBACjC,MAAM;gBACN,OAAO,sIAAA,CAAA,gBAAa;gBACpB,WAAW;gBACX,UAAU;gBACV,kBAAkB;YACpB;YAEA,IAAI,QAAQ,MAAM,WAAW,MAAM,OAAO,CAAC,OAAO,IAAI,CAAC,IAAI,GAAG;gBAC5D,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;gBAEpC,QAAQ,CAAC,WAAc,QAAQ,cAAc;2BAAI;2BAAa;qBAAY;gBAE1E,IAAI,YAAY,MAAM,GAAG,sIAAA,CAAA,gBAAa,EAAE;oBACtC,WAAW;gBACb,OAAO;oBACL,WAAW;gBACb;gBAEA,UAAU,gBAAgB,YAAY,MAAM;YAC9C,OAAO;gBACL,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,EAAE,wBAAwB;YACxC,WAAW;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,kBAAkB;QAClB,IAAI;YACF,MAAM,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YAC7B,QAAQ,CAAC,WAAa,SAAS,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;QAC5D,EAAE,OAAM;YACN,QAAQ,KAAK,CAAC,EAAE;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,eAAe,MAAM,IAAI;QAC/B,aAAa;QACb,sEAAsE;QACtE,WAAW;QACX,cAAc,cAAc,GAAG;IACjC;IAEA;;;GAGC,GACD,MAAM,gBAAgB,CAAC;QACrB,qBAAqB;QACrB,kBAAkB;QAElB,kDAAkD;QAClD,OAAO,IAAI,CAAC,GAAG,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;IACzD;IAEA,MAAM,mCAAmC,CAAA,GAAA,qIAAA,CAAA,UAAQ,AAAD,EAAE,yBAAyB;IAE3E,qBACE;;0BACE,6LAAC;gBAAQ,WAAW,GAAG,4JAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC,EAAE,4JAAA,CAAA,UAAK,CAAC,oBAAoB,EAAE;0BACtE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,uJAAA,CAAA,UAAa;oDAAC,SAAS,IAAM,OAAO,IAAI;;;;;;gDACxC,EAAE;gDAAmB;8DAAC,6LAAC;;wDAAK;wDAAE,EAAE;wDAAe;;;;;;;;;;;;;sDAElD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qJAAA,CAAA,UAAY;oDAAC,WAAU;8DACtB,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gJAAA,CAAA,UAAO;4DACN,WAAU;4DACV,SAAS;4DACT,MAAK;4DACL,MAAK;4DACL,UAAU,CAAC,IAAM,iCAAiC,EAAE,MAAM,CAAC,KAAK;4DAChE,aAAa,EAAE;sEAEf,cAAA,6LAAC,qJAAA,CAAA,UAAY,CAAC,IAAI;0EAChB,cAAA,6LAAC,oJAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8DAKnB,6LAAC,+IAAA,CAAA,UAAM;oDAAC,WAAU;oDAAmC,SAAS,IAAM,OAAO,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;8DACpG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOb,6LAAC;4BAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,uBAAuB;sCAC3C,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,kLAAA,CAAA,UAAc;oCACb,YAAY,KAAK,MAAM;oCACvB,MAAM,IAAM,cAAc,WAAW;oCACrC,SAAS;oCACT,QAAQ,OAAO,WAAW,GAAG;oCAC7B,QACE,yBACE,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC,4JAAA,CAAA,UAAa;4CAAC,MAAM;4CAAG,MAAM;4CAAG,WAAU;;;;;;;;;;;oCAIjD,YACE,CAAC,WAAW,KAAK,MAAM,iBACrB,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;sDACC,cAAA,6LAAC;0DACC,cAAA,6LAAC;oDAAG,SAAS;oDAAG,OAAO;wDAAE,WAAW;wDAAU,iBAAiB;oDAAO;8DACnE,EAAE;;;;;;;;;;;;;;;;;;;;iDAKT;8CAGN,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;0DACC,cAAA,6LAAC;;sEACC,6LAAC;sEAAI,EAAE;;;;;;sEACP,6LAAC;sEAAI,EAAE;;;;;;sEACP,6LAAC;sEAAI,EAAE;;;;;;sEACP,6LAAC;4DAAG,WAAU;;gEAAc;gEAAE,EAAE;;;;;;;sEAChC,6LAAC;4DAAG,WAAU;sEAAe,EAAE;;;;;;;;;;;;;;;;;4CAGlC,KAAK,MAAM,GAAG,kBACb,6LAAC;0DACE,KAAK,GAAG,CAAC,CAAC;oDACT,iDAAiD;oDACjD,MAAM,aACJ,IAAI,EAAE,KAAK,QACX,IAAI,KAAK,KAAK,QACd,IAAI,KAAK,KAAK,QACd,IAAI,UAAU,KAAK,QACnB,IAAI,QAAQ,KAAK,QACjB,IAAI,uBAAuB,KAAK;oDAElC,IAAI,YAAY;wDACd,qBACE,6LAAC;sEACC,cAAA,6LAAC;gEAAG,SAAS;gEAAG,OAAO;oEAAE,WAAW;gEAAS;0EAC1C,EAAE;;;;;;2DAFC;;;;;oDAMZ;oDAEA,MAAM,OAAO,IAAI,UAAU,GAAG,IAAI,KAAK,IAAI,UAAU,IAAI;oDACzD,MAAM,gBACJ,QAAQ,CAAC,MAAM,KAAK,OAAO,MACvB,KAAK,kBAAkB,CAAC,SAAS;wDAC/B,MAAM;wDACN,OAAO;wDACP,KAAK;oDACP,KACA;oDAEN,qBACE,6LAAC;;0EACC,6LAAC;0EAAI,KAAK;;;;;;0EACV,6LAAC;0EAAI,KAAK;;;;;;0EACV,6LAAC;0EAAI;;;;;;0EACL,6LAAC;gEAAG,OAAM;0EAAU,KAAK;;;;;;0EACzB,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,+IAAA,CAAA,UAAM;4EACL,WAAU;4EACV,SAAS,IAAM,kBAAkB,CAAC,SAAY,WAAW,OAAO,IAAI,EAAE,IAAI,OAAO,OAAO,IAAI,EAAE;sFAE9F,cAAA,6LAAC,uJAAA,CAAA,UAAa;;;;;;;;;;wEAEf,mBAAmB,OAAO,IAAI,EAAE,mBAC/B,6LAAC;4EACC,WAAU;4EACV,KAAK,CAAC;gFACJ,IAAI,SAAS;oFACX,aAAa,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,GAAG;gFACzC;4EACF;;gFAEC,OAAO,IAAI,gBAAgB,IAAI,IAAI,gBAAgB,GAAG,mBACrD,6LAAC;oFACC,SAAS;wFACP,QAAQ,GAAG,CAAC,OAAO;wFACnB,OAAO,IAAI,CACT,GAAG,6HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,mBAAmB,IAAI,KAAK,EAAE,aAAa,EAAE,mBAAmB,IAAI,KAAK,GAAG;oFAE1I;8FAEC,EAAE;;;;;;gFAGN,oDACC,6LAAC;oFACC,SAAS,IACP,OAAO,IAAI,CACT,GAAG,6HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,mBAAmB,IAAI,KAAK,EAAE,aAAa,EAAE,mBAAmB,IAAI,KAAK,GAAG;8FAIlJ,EAAE;;;;;;8FAGP,6LAAC;oFAAG,SAAS,IAAM,gCAAgC;8FAAO;;;;;;gFACzD,OAAO,IAAI,gBAAgB,IAAI,IAAI,gBAAgB,GAAG,mBACrD,6LAAC;oFACC,SAAS;wFACP,OAAO,IAAI,CACT,GAAG,6HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,GACzD,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,aAAa,EAAE,IAAI,KAAK,EAAE;oFAEpD;8FAEC,EAAE;;;;;;gFAGN,gDACC,6LAAC;oFAAG,SAAS,IAAM,iBAAiB,IAAI,EAAE;oFAAG,OAAO;wFAAE,QAAQ;oFAAU;8FACrE,EAAE;;;;;;8FAGP,6LAAC;oFAAG,SAAS,IAAM,cAAc;oFAAM,OAAO;wFAAE,QAAQ;oFAAU;8FAC/D,EAAE;;;;;;;;;;;;;;;;;;;;;;;;uDAhEN,IAAI,EAAE;;;;;gDAwEnB;;;;;uDAGF,CAAC,yBACC,6LAAC;0DACC,cAAA,6LAAC;8DACC,cAAA,6LAAC;wDAAG,SAAS;wDAAG,OAAO;4DAAE,WAAW;wDAAS;kEAC1C,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAaxB,8CAAgC,6LAAC,iKAAA,CAAA,UAAwB;gBAAC,UAAU,IAAM,gCAAgC;;;;;;;;AAGjH;GAhUM;;QACW,qIAAA,CAAA,YAAS;QACJ,iKAAA,CAAA,UAAO;QAQjB,yMAAA,CAAA,kBAAe;QAMD,4JAAA,CAAA,cAAW;;;KAhB/B;uCAkUS", "debugId": null}}, {"offset": {"line": 1424, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1430, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/active-jobs/page.tsx"], "sourcesContent": ["\"use client\";\nimport ActiveJobs from \"@/components/views/resume/ActiveJobs\";\nimport React from \"react\";\n\nconst page = () => {\n  return (\n    <div>\n      <ActiveJobs />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO;IACX,qBACE,6LAAC;kBACC,cAAA,6LAAC,sJAAA,CAAA,UAAU;;;;;;;;;;AAGjB;uCAEe", "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}