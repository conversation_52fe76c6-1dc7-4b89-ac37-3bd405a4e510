/* [project]/src/styles/footer.module.scss.module.css [app-client] (css) */
.footer-module-scss-module__CiQmEa__footer {
  background: #111;
  padding: 3rem 0;
}

@media screen and (width <= 767px) {
  .footer-module-scss-module__CiQmEa__footer {
    text-align: center;
  }
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top {
  border-bottom: 1px solid #fff3;
  padding-bottom: 2.2rem;
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_left .footer-module-scss-module__CiQmEa__logo_footer {
  width: 180px;
  height: 50px;
  object-fit: contain;
}

@media (width >= 768px) and (width <= 991px) {
  .footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_left .footer-module-scss-module__CiQmEa__logo_footer {
    width: 180px;
    height: 50px;
    margin-top: -5px;
    margin-bottom: 1.5rem;
  }
}

@media screen and (width <= 767px) {
  .footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_left .footer-module-scss-module__CiQmEa__logo_footer {
    width: 125px;
    height: 35px;
    margin-top: 0;
  }
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_left .footer-module-scss-module__CiQmEa__social_media {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

@media screen and (width <= 767px) {
  .footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_left .footer-module-scss-module__CiQmEa__social_media {
    justify-content: center;
    margin-bottom: 10px;
  }
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_left .footer-module-scss-module__CiQmEa__social_media svg {
  width: 35px;
  height: 35px;
  object-fit: contain;
  cursor: pointer;
  transition: all .5s ease-in;
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_left .footer-module-scss-module__CiQmEa__social_media svg:hover {
  fill: #cb9932;
}

@media screen and (width <= 767px) {
  .footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_left {
    margin-bottom: 20px;
  }
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_right .footer-module-scss-module__CiQmEa__links_heading {
  color: #fff;
  margin-bottom: 1rem;
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_right .footer-module-scss-module__CiQmEa__app_links {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_right .footer-module-scss-module__CiQmEa__app_links img {
  width: 150px;
  height: 45px;
  object-fit: contain;
}

@media screen and (width <= 767px) {
  .footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_right .footer-module-scss-module__CiQmEa__app_links {
    justify-content: center;
  }

  .footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_top .footer-module-scss-module__CiQmEa__footer_right .footer-module-scss-module__CiQmEa__app_links img {
    width: 120px;
  }
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 2rem;
  gap: 1rem;
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_bottom p {
  color: #fff;
  font-weight: 400;
  font-size: 1.3rem;
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_bottom .footer-module-scss-module__CiQmEa__terms_policy {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #fff;
}

.footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_bottom .footer-module-scss-module__CiQmEa__terms_policy a {
  color: #fff;
  font-size: 1.3rem;
}

@media screen and (width <= 767px) {
  .footer-module-scss-module__CiQmEa__footer .footer-module-scss-module__CiQmEa__footer_bottom {
    flex-flow: column;
  }
}

/*# sourceMappingURL=src_styles_footer_module_scss_module_b52d8e88.css.map*/