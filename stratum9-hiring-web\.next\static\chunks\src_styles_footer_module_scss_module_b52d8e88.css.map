{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/footer.module.scss.module.css"], "sourcesContent": [".footer{background:#111;padding:3rem 0}@media screen and (max-width: 767px){.footer{text-align:center}}.footer .footer_top{border-bottom:solid 1px hsla(0,0%,100%,.2);padding-bottom:2.2rem}.footer .footer_top .footer_left .logo_footer{width:180px;height:50px;object-fit:contain}@media(min-width: 768px)and (max-width: 991px){.footer .footer_top .footer_left .logo_footer{width:180px;height:50px;margin-top:-5px;margin-bottom:1.5rem}}@media screen and (max-width: 767px){.footer .footer_top .footer_left .logo_footer{width:125px;height:35px;margin-top:0}}.footer .footer_top .footer_left .social_media{display:flex;align-items:center;gap:1rem;margin-top:1.5rem}@media screen and (max-width: 767px){.footer .footer_top .footer_left .social_media{justify-content:center;margin-bottom:10px}}.footer .footer_top .footer_left .social_media svg{width:35px;height:35px;object-fit:contain;cursor:pointer;transition:all .5s ease-in 0s}.footer .footer_top .footer_left .social_media svg:hover{fill:#cb9932}@media screen and (max-width: 767px){.footer .footer_top .footer_left{margin-bottom:20px}}.footer .footer_top .footer_right .links_heading{color:#fff;margin-bottom:1rem}.footer .footer_top .footer_right .app_links{display:flex;gap:1rem;justify-content:flex-start}.footer .footer_top .footer_right .app_links img{width:150px;height:45px;object-fit:contain}@media screen and (max-width: 767px){.footer .footer_top .footer_right .app_links{justify-content:center}.footer .footer_top .footer_right .app_links img{width:120px}}.footer .footer_bottom{display:flex;align-items:center;justify-content:space-between;padding-top:2rem;gap:1rem}.footer .footer_bottom p{color:#fff;font-weight:400;font-size:1.3rem}.footer .footer_bottom .terms_policy{display:flex;align-items:center;gap:1rem;color:#fff}.footer .footer_bottom .terms_policy a{color:#fff;font-size:1.3rem}@media screen and (max-width: 767px){.footer .footer_bottom{flex-flow:column}}"], "names": [], "mappings": "AAAA;;;;;AAAuC;EAAqC;;;;;AAA2B;;;;;AAAqF;;;;;;AAAyF;EAA+C;;;;;;;;AAA4G;EAAqC;;;;;;;AAAoF;;;;;;;AAA0G;EAAqC;;;;;;AAA0F;;;;;;;;AAA0I;;;;AAAsE;EAAqC;;;;;AAAqD;;;;;AAA+E;;;;;;AAA8F;;;;;;AAA4F;EAAqC;;;;EAAoE;;;;;AAA8D;;;;;;;;AAA+G;;;;;;AAAqE;;;;;;;AAAyF;;;;;AAAmE;EAAqC"}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}