import React, { use, useEffect, useMemo, useState } from "react";

import { Bar, Radar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  RadialLinearScale,
  CategoryScale,
  PointElement,
  LinearScale,
  BarElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { buildStyles, CircularProgressbar } from "react-circular-progressbar";
import Image from "next/image";
import Avatar from "react-avatar";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { useTranslations } from "use-intl";

import AiMarkIcon from "@/components/svgComponents/AiMarkIcon";
import AIVerifiedIcon from "@/components/svgComponents/AIVerifiedIcon";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import CheckSecondaryIcon from "@/components/svgComponents/CheckSecondaryIcon";
import PreviewResumeIcon from "@/components/svgComponents/PreviewResumeIcon";
import StarIcon from "@/components/svgComponents/StarIcon";
import Button from "@/components/formElements/Button";
import Loader from "@/components/loader/Loader";
import ResumeModal from "@/components/commonModals/ResumeModal";
import ConfirmationModal from "@/components/commonModals/ConfirmationModal";

import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";
import type {
  BarChartData,
  CandidateProfileProps,
  CandidateProfileResponse,
  ICandidateInterviewHistory,
  IFinalAssessment,
  ISkillSpecificAssessment,
  RadarChartData,
} from "@/interfaces/candidatesInterface";
import { AuthState } from "@/redux/slices/authSlice";
import {
  fetchCandidateProfile,
  getApplicationFinalSummary,
  getCandidateInterviewHistory,
  updateJobApplicationStatus,
  generateFinalSummary,
} from "@/services/CandidatesServices/candidatesApplicationServices";
import { toastMessageError, toastMessageSuccess, toTitleCase } from "@/utils/helper";

import noImageFound from "../../../../public/assets/images/noImageFound.svg";

import "react-circular-progressbar/dist/styles.css";
import FinalAssessmentIcon from "@/components/svgComponents/FinalAssessmentIcon";
import { createFinalAssessment, getAssessmentStatus } from "@/services/assessmentService";
import ROUTES from "@/constants/routes";
import QuestionGeneratorLoader from "@/components/loader/QuestionGeneratorLoader";
import FinalAssessmentConfirmModal from "@/components/commonModals/FinalAssessmentConfirmModal";
import Skeleton from "react-loading-skeleton";
import style from "../../../styles/conductInterview.module.scss";
import "react-loading-skeleton/dist/skeleton.css";
// ============================================================================
// CANDIDATE PROFILE COMPONENT
// ============================================================================

/**
 * @fileoverview CandidateProfile Component
 *
 * A comprehensive React component for displaying detailed candidate information
 * during the interview process. This component provides a complete view of
 * candidate data including profile information, skill assessments, interview
 * history, and hiring/rejection functionality.
 *
 *
 * Key Features:
 * - Tabbed interface for skill assessment and interview history
 * - Interactive charts for skill visualization (Bar and Radar charts)
 * - Real-time data loading with proper loading states
 * - Hire/Reject candidate functionality with status updates
 * - AI-powered analysis and recommendations display
 * - Responsive design with Bootstrap grid system
 * - Comprehensive error handling and user feedback
 *
 * Data Sources:
 * - Candidate profile information
 * - Skill-specific assessment data
 * - Final assessment summary
 * - Interview history across all rounds
 * - AI interviewer performance analysis
 *
 * Dependencies:
 * - Chart.js for data visualization
 * - Next.js for routing and image optimization
 * - Redux for state management
 * - React Intl for internationalization
 *
 * Performance Considerations:
 * - Memoized computed values to prevent unnecessary re-renders
 * - Lazy loading of tab-specific data
 * - Optimized API calls with loading state management
 * - Efficient chart data generation
 */

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export interface IAssessmentStatus {
  exists: boolean;
  isAssessmentShared: boolean;
  isAssessmentSubmitted: boolean;
  assessmentId: number | null;
}
const BUTTON_STATES = {
  IDLE: "idle",
  LOADING: "loading",
  GENERATING: "generating",
};

/**
 * CandidateProfile Component
 *
 * A comprehensive candidate profile view that displays candidate information,
 * skill assessments, interview history, and provides hiring/rejection functionality.
 *
 * Features:
 * - Tabbed interface for skill assessment and interview history
 * - Interactive charts for skill visualization
 * - Real-time data loading with loading states
 * - Hire/Reject candidate functionality
 * - AI-powered analysis and recommendations
 *
 * @param props - Component props containing candidate ID parameter
 * @returns JSX.Element - The rendered candidate profile component
 */
const CandidateProfile: React.FC<CandidateProfileProps> = ({ params }) => {
  // ============================================================================
  // HOOKS AND EXTERNAL DATA
  // ============================================================================

  const router = useRouter();
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const t = useTranslations();

  // Extract candidate ID from params
  const paramsPromise = use(params);
  const jobApplicationId = paramsPromise.jobApplicationId;

  // ============================================================================
  // STATE DECLARATIONS
  // ============================================================================

  // UI State - Controls tab selection and active skill display
  /** Controls which main tab is active (true = Skill Assessment, false = Interview History) */
  const [selectedTab, setSelectedTab] = useState<boolean>(false);
  /** Currently selected skill tab for detailed view */
  const [activeSkillTab, setActiveSkillTab] = useState<string>("");
  /** Animation value for circular progress bar (0-100) */
  const [animationValue, setAnimationValue] = useState<number>(0);

  // Data State - Stores fetched candidate information
  /** Main candidate profile data from API */
  const [candidateProfileData, setCandidateProfileData] = useState<CandidateProfileResponse | null>(null);
  /** Candidate's interview history across all rounds */
  const [candidateInterviewHistory, setCandidateInterviewHistory] = useState<ICandidateInterviewHistory[]>([]);
  /** Detailed skill-specific assessment data */
  const [skillSpecificAssessment, setSkillSpecificAssessment] = useState<ISkillSpecificAssessment | null>(null);
  /** Final assessment summary and recommendations */
  const [finalAssessment, setFinalAssessment] = useState<IFinalAssessment | null>(null);

  // Loading State - Tracks API call progress
  /** Loading state for final assessment and skill assessment API call (combined) */
  const [isLoadingFinalAssessment, setIsLoadingFinalAssessment] = useState<boolean>(false);
  /** Loading state for interview history API call */
  const [isLoadingInterviewHistory, setIsLoadingInterviewHistory] = useState<boolean>(false);
  /** Processing state for hire/reject actions */
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  /** Loading state for generate final summary action */
  const [isGeneratingFinalSummary, setIsGeneratingFinalSummary] = useState<boolean>(false);
  /** Flag to track if user has generated final summary */
  const [hasFinalSummaryBeenGenerated, setHasFinalSummaryBeenGenerated] = useState<boolean>(false);

  // Data Loaded Flags - Prevents unnecessary API calls
  /** Flag indicating if skill assessment data has been loaded */
  const [skillAssessmentLoaded, setSkillAssessmentLoaded] = useState<boolean>(false);
  /** Flag indicating if final assessment data has been loaded */
  const [finalAssessmentLoaded, setFinalAssessmentLoaded] = useState<boolean>(false);
  /** Flag indicating if interview history data has been loaded */
  const [interviewHistoryLoaded, setInterviewHistoryLoaded] = useState<boolean>(false);
  /** Flag incdicating if candidate profile data has been loaded */
  const [candidateProfileLoaded, setCandidateProfileLoaded] = useState<boolean>(false);

  type ButtonState = (typeof BUTTON_STATES)[keyof typeof BUTTON_STATES];
  // const searchParams = useSearchParams() || new URLSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [buttonState, setButtonState] = useState<ButtonState>(BUTTON_STATES.IDLE);
  const [assessmentStatus, setAssessmentStatus] = useState<IAssessmentStatus>();
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  // Confirmation modal states for different actions
  const [confirmationModal, setConfirmationModal] = useState({
    isOpen: false,
    action: "",
    title: "",
    message: "",
    confirmButtonText: "",
    onConfirm: () => {},
    loading: false,
  });

  console.log(skillAssessmentLoaded, finalAssessmentLoaded, interviewHistoryLoaded);

  const [resumeModal, setResumeModal] = useState<{ isOpen: boolean; resumeLink: string | null }>({ isOpen: false, resumeLink: null });

  // const showFinalAssessmentConfirmation = () => {
  //   if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {
  //     toastMessageError(t("job_id_and_job_application_id_are_required"));
  //     return;
  //   }
  //   setShowConfirmModal(true);
  // };

  // Helper functions to show confirmation modals for different actions
  const showCreateFinalAssessmentConfirmation = () => {
    setConfirmationModal({
      isOpen: true,
      action: "createFinalAssessment",
      title: t("create_final_assessment"),
      message: t("are_you_sure_you_want_to_create_final_assessment"),
      confirmButtonText: t("create"),
      onConfirm: handleCreateFinalAssessmentConfirmed,
      loading: false,
    });
  };

  const showGenerateFinalSummaryConfirmation = () => {
    setConfirmationModal({
      isOpen: true,
      action: "generateFinalSummary",
      title: t("generate_final_summary"),
      message:
        "Once the final summary is generated for this candidate, they will no longer be eligible to participate in any additional interview rounds. If any interview round remains incomplete, please ensure that it is conducted before proceeding with the final summary for this candidate.",
      confirmButtonText: t("generate"),
      onConfirm: handleGenerateFinalSummaryConfirmed,
      loading: false,
    });
  };

  const showHireConfirmation = () => {
    setConfirmationModal({
      isOpen: true,
      action: "hire",
      title: t("hire_candidate"),
      message: t("are_you_sure_you_want_to_hire_this_candidate"),
      confirmButtonText: t("hire"),
      onConfirm: () => handleHireRejectCandidateConfirmed(APPLICATION_STATUS.HIRED),
      loading: false,
    });
  };

  const showRejectConfirmation = () => {
    setConfirmationModal({
      isOpen: true,
      action: "reject",
      title: t("reject_candidate"),
      message: t("are_you_sure_you_want_to_reject_this_candidate"),
      confirmButtonText: t("reject"),
      onConfirm: () => handleHireRejectCandidateConfirmed(APPLICATION_STATUS.FINAL_REJECT),
      loading: false,
    });
  };

  const closeConfirmationModal = () => {
    setConfirmationModal({
      isOpen: false,
      action: "",
      title: "",
      message: "",
      confirmButtonText: "",
      onConfirm: () => {},
      loading: false,
    });
  };

  /**
   * Handle creating final assessment (confirmed action)
   */
  const handleCreateFinalAssessmentConfirmed = async () => {
    if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {
      toastMessageError(t("job_id_and_job_application_id_are_required"));
      closeConfirmationModal();
      return;
    }

    try {
      // Update modal loading state
      setConfirmationModal((prev) => ({ ...prev, loading: true }));
      setIsLoading(true);
      setButtonState(BUTTON_STATES.GENERATING);

      const response = await createFinalAssessment({ jobId: candidateProfileData?.jobId, jobApplicationId: candidateProfileData?.jobApplicationId });
      if (response && response.data && response.data.success) {
        toastMessageSuccess(t(response?.data?.message || "final_assessment_created_successfully"));

        // Get the finalAssessmentId from the response data
        const finalAssessmentId = response.data.data?.assessmentId;

        if (finalAssessmentId) {
          // Redirect to the final assessment page with the finalAssessmentId and default status values
          // For a newly created assessment, both isShared and isSubmitted will be false
          router.push(
            `${ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${finalAssessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`
          );
        }
      } else {
        toastMessageError(t(response?.data?.message || "failed_to_create_final_assessment"));
      }
    } catch (error) {
      console.error("Error creating final assessment:", error);
      toastMessageError(t("an_error_occurred_while_creating_the_final_assessment"));
    } finally {
      setIsLoading(false);
      setButtonState(BUTTON_STATES.IDLE);
      closeConfirmationModal();
    }
  };

  /**
   * Handle creating final assessment (original function for backward compatibility)
   */
  const handleCreateFinalAssessment = async () => {
    if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {
      toastMessageError(t("job_id_and_job_application_id_are_required"));
      return;
    }

    try {
      setIsLoading(true);
      setButtonState(BUTTON_STATES.GENERATING);
      setShowConfirmModal(false);
      const response = await createFinalAssessment({ jobId: candidateProfileData?.jobId, jobApplicationId: candidateProfileData?.jobApplicationId });
      if (response && response.data && response.data.success) {
        toastMessageSuccess(t(response?.data?.message || "final_assessment_created_successfully"));

        // Get the finalAssessmentId from the response data
        const finalAssessmentId = response.data.data?.assessmentId;

        if (finalAssessmentId) {
          // Redirect to the final assessment page with the finalAssessmentId and default status values
          // For a newly created assessment, both isShared and isSubmitted will be false
          router.push(
            `${ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${finalAssessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`
          );
        }
      } else {
        toastMessageError(t(response?.data?.message || "failed_to_create_final_assessment"));
      }
    } catch (error) {
      console.error("Error creating final assessment:", error);
      toastMessageError(t("an_error_occurred_while_creating_the_final_assessment"));
    } finally {
      setIsLoading(false);
      setButtonState(BUTTON_STATES.IDLE);
    }
  };

  /**
   * Close the confirmation modal
   */
  const handleCancelConfirmModal = () => {
    setShowConfirmModal(false);
  };

  // Effect to check assessment status when component mounts
  useEffect(() => {
    // Automatically call getAssessmentStatus when the component mounts
    const checkStatus = async () => {
      if (!jobApplicationId) {
        return;
      }

      try {
        setIsLoading(true);
        const response = await getAssessmentStatus(jobApplicationId);

        if (response?.data) {
          const assessmentData = response?.data?.data;

          // Update assessment status state
          setAssessmentStatus({
            exists: !!assessmentData.assessmentId, //exists: assessmentData.id ? true : false
            isAssessmentShared: assessmentData.isAssessmentShared || false,
            isAssessmentSubmitted: assessmentData.isAssessmentSubmitted || false,
            assessmentId: assessmentData.assessmentId || null,
          });
        } else {
          toastMessageError(t(response?.data?.message || "failed_to_get_assessment_status"));
        }
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    };

    checkStatus();
  }, [t]);
  // ============================================================================
  // CHART CONFIGURATION
  // ============================================================================

  // Register Chart.js components
  ChartJS.register(RadialLinearScale, CategoryScale, LinearScale, BarElement, PointElement, LineElement, Title, Tooltip, Legend);

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  /**
   * Generates bar chart data for skill visualization
   * @returns BarChartData object configured for skill scores display
   */
  const generateBarChartData = (): BarChartData => {
    const labels = skillSpecificAssessment?.skillsScores.map((item) => item.skill_name) || [];
    const values = skillSpecificAssessment?.skillsScores.map((item) => item.skill_marks * 10) || [];

    return {
      labels,
      datasets: [
        {
          data: values,
          backgroundColor: labels.map((label, index) => {
            const skillScore = values[index];
            // Color changes only for perfect scores (100/100)
            return skillScore === 100 ? "#ffc107" : "rgba(119,167,255,0.8)";
          }),
          borderRadius: 8,
          borderSkipped: false,
          barPercentage: 0.5,
        },
      ],
    };
  };

  /**
   * Generates radar chart data for behavioral assessment visualization
   * @returns RadarChartData object configured for behavioral metrics
   */
  const generateRadarChartData = (): RadarChartData => {
    return {
      labels: ["Body Language", "Affability", "Posture", "Eye-Contact", "Confidence"], // Dynamic portion
      datasets: [
        {
          label: "Behavioral Assessment",
          data: [28, 48, 40, 19, 96],
          fill: true,
          backgroundColor: "rgba(49, 65, 75, 0.2)",
          borderColor: "rgb(54, 162, 235)",
          pointBackgroundColor: "rgb(54, 162, 235)",
          pointBorderColor: "#fff",
          pointHoverBackgroundColor: "#fff",
          pointHoverBorderColor: "rgb(54, 162, 235)",
        },
      ],
    };
  };

  /**
   * Calculates the number of filled bars for skill success probability visualization
   * @param probability - Success probability percentage (0-100)
   * @returns Number of bars to fill (0-10)
   */
  const calculateFilledBars = (probability: number): number => {
    return Math.round(probability / 10);
  };

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  /** Chart data for bar chart visualization */
  const barChartData = generateBarChartData();

  /** Chart data for radar chart visualization */
  const radarChartData = generateRadarChartData();

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  /**
   * Handles switching to Interview History view
   * Loads interview history data if not already loaded
   *
   * @async
   * @function handleInterviewHistoryClick
   * @returns {Promise<void>}
   */
  const handleInterviewHistoryClick = async (): Promise<void> => {
    setSelectedTab(false);

    // Load interview history data if not already loaded

    await loadCandidateInterviewHistory();
  };

  /**
   * Handles switching to Skill Specific Assessment view
   * Only works if final summary has been generated
   *
   * @async
   * @function handleSkillAssessmentClick
   * @returns {Promise<void>}
   */
  const handleSkillAssessmentClick = async (): Promise<void> => {
    if (!hasFinalSummaryBeenGenerated) return;

    setSelectedTab(true);

    // Load assessment data if not already loaded (both final assessment and skill data)
    if (!skillAssessmentLoaded || !finalAssessmentLoaded) {
      await loadFinalAssessment();
    }
  };

  /**
   * Handles hire or reject candidate action (confirmed action)
   * Updates the job application status and refreshes all data
   *
   * @async
   * @function handleHireRejectCandidateConfirmed
   * @param {string} status - The new application status (HIRED or FINAL_REJECT)
   * @returns {Promise<void>}
   */
  const handleHireRejectCandidateConfirmed = async (status: string): Promise<void> => {
    if (!candidateProfileData) {
      closeConfirmationModal();
      return;
    }

    try {
      // Update modal loading state
      setConfirmationModal((prev) => ({ ...prev, loading: true }));
      setIsProcessing(true);

      const response = await updateJobApplicationStatus(Number(candidateProfileData.jobApplicationId), status);

      if (response && response.data && response.data.success) {
        toastMessageSuccess(t(response.data.message));
        await refreshAllData();
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch (error) {
      console.error("Error updating job application status:", error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setIsProcessing(false);
      closeConfirmationModal();
    }
  };

  /**
   * Handles hire or reject candidate action (original function for backward compatibility)
   * Updates the job application status and refreshes all data
   *
   * @async
   * @function handleHireRejectCandidate
   * @param {string} status - The new application status (HIRED or FINAL_REJECT)
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isProcessing state during operation
   * - Shows success/error toast messages
   * - Refreshes all candidate data on success
   *
   * @example
   * // Hire the candidate
   * handleHireRejectCandidate(APPLICATION_STATUS.HIRED);
   *
   * // Reject the candidate
   * handleHireRejectCandidate(APPLICATION_STATUS.FINAL_REJECT);
   */
  // const handleHireRejectCandidate = async (status: string): Promise<void> => {
  //   if (!candidateProfileData) return;

  //   setIsProcessing(true);
  //   try {
  //     const response = await updateJobApplicationStatus(Number(candidateProfileData.jobApplicationId), status);

  //     if (response && response.data && response.data.success) {
  //       toastMessageSuccess(t(response.data.message));
  //       await refreshAllData();
  //     } else {
  //       toastMessageError(t(response?.data?.message));
  //     }
  //   } catch (error) {
  //     console.error("Error updating job application status:", error);
  //     toastMessageError(t("something_went_wrong"));
  //   } finally {
  //     setIsProcessing(false);
  //   }
  // };

  // ============================================================================
  // API FUNCTIONS
  // ============================================================================

  /**
   * Loads candidate interview history from the API
   * Prevents multiple simultaneous calls using loading state
   *
   * @async
   * @function loadCandidateInterviewHistory
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isLoadingInterviewHistory state
   * - Updates candidateInterviewHistory state on success
   * - Updates interviewHistoryLoaded flag
   * - Shows error toast on failure
   */
  const loadCandidateInterviewHistory = async (): Promise<void> => {
    if (isLoadingInterviewHistory) return;

    setIsLoadingInterviewHistory(true);
    try {
      const response = await getCandidateInterviewHistory(jobApplicationId);
      if (response?.data?.success) {
        setCandidateInterviewHistory(response.data.data);
        setInterviewHistoryLoaded(true);
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch (error) {
      console.error("Error loading interview history:", error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setIsLoadingInterviewHistory(false);
    }
  };

  /**
   * Loads final assessment and skill score data from the API
   * Prevents multiple simultaneous calls using loading state
   * Now loads both final assessment and skill score data in a single API call
   *
   * @async
   * @function loadFinalAssessment
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isLoadingFinalAssessment state
   * - Updates finalAssessment state on success
   * - Updates skillSpecificAssessment state on success
   * - Updates finalAssessmentLoaded and skillAssessmentLoaded flags
   * - Shows error toast on failure
   */
  const loadFinalAssessment = async (): Promise<void> => {
    if (isLoadingFinalAssessment || (finalAssessmentLoaded && skillAssessmentLoaded)) return;

    setIsLoadingFinalAssessment(true);
    try {
      const response = await getApplicationFinalSummary(jobApplicationId);
      if (response?.data?.success) {
        // Extract final assessment data
        if (response.data.data.formattedFinalAssessment) {
          setFinalAssessment(response.data.data.formattedFinalAssessment);
          set
          setFinalAssessmentLoaded(true);
        }

        // Extract skill score data
        if (response.data.data.candidateProfileSkillScoreData) {
          setSkillSpecificAssessment(response.data.data.candidateProfileSkillScoreData);
          setSkillAssessmentLoaded(true);
        }
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch (error) {
      console.error("Error loading final assessment:", error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setIsLoadingFinalAssessment(false);
    }
  };



  /**
   * Loads candidate profile data from the API
   *
   * @async
   * @function loadCandidateProfile
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates candidateProfileData state on success
   * - Shows error toast on failure
   */
  const loadCandidateProfile = async (): Promise<void> => {
    setCandidateProfileLoaded(true);

    try {
      const response = await fetchCandidateProfile(jobApplicationId);
      if (response?.data?.success) {
        setCandidateProfileData(response.data.data);
        setCandidateProfileLoaded(false);
      } else {
        toastMessageError(t(response?.data?.message));
        setCandidateProfileLoaded(false);
      }
    } catch (error) {
      console.error("Error loading candidate profile:", error);
      toastMessageError(t("something_went_wrong"));
      setCandidateProfileLoaded(false);
    }
  };

  /**
   * Refreshes all candidate data by resetting loaded flags and reloading data
   * Used after status updates to ensure data consistency
   *
   * @async
   * @function refreshAllData
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Resets all data loaded flags
   * - Reloads candidate profile
   * - Reloads tab-specific data based on current tab
   */
  const refreshAllData = async (): Promise<void> => {
    // Reset loaded flags to force refresh
    setSkillAssessmentLoaded(false);
    setFinalAssessmentLoaded(false);
    setInterviewHistoryLoaded(false);

    // Refresh candidate profile
    await loadCandidateProfile();

    // Refresh assessment data based on current tab
    if (selectedTab) {
      await Promise.all([loadSkillSpecificAssessment(), loadFinalAssessment()]);
    } else {
      await loadCandidateInterviewHistory();
    }
  };

  /**
   * Handles generating final summary for the candidate (confirmed action)
   * Triggers API call to generate comprehensive final summary based on interview data
   *
   * @async
   * @function handleGenerateFinalSummaryConfirmed
   * @returns {Promise<void>}
   */
  const handleGenerateFinalSummaryConfirmed = async (): Promise<void> => {
    if (!candidateProfileData?.jobApplicationId || isGeneratingFinalSummary) {
      closeConfirmationModal();
      return;
    }

    try {
      // Update modal loading state
      setConfirmationModal((prev) => ({ ...prev, loading: true }));
      setIsGeneratingFinalSummary(true);

      const response = await generateFinalSummary(candidateProfileData.jobApplicationId);
      if (response?.data?.success) {
        toastMessageSuccess(t(response?.data?.message || "final_summary_generated_successfully"));
        // Set flag to indicate final summary has been generated by user
        setHasFinalSummaryBeenGenerated(true);
        // Switch to Skill Specific Assessment tab automatically
        setSelectedTab(true);
        // Load skill-specific assessment data
        await Promise.all([loadFinalAssessment(), loadSkillSpecificAssessment()]);
      } else {
        toastMessageError(t(response?.data?.message || "failed_to_generate_final_summary"));
      }
    } catch (error) {
      console.error("Error generating final summary:", error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setIsGeneratingFinalSummary(false);
      closeConfirmationModal();
    }
  };

  /**
   * Handles generating final summary for the candidate (original function for backward compatibility)
   * Triggers API call to generate comprehensive final summary based on interview data
   *
   * @async
   * @function handleGenerateFinalSummary
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isGeneratingFinalSummary state
   * - Shows success/error toast messages
   * - Refreshes final assessment data on success
   */
  // const handleGenerateFinalSummary = async (): Promise<void> => {
  //   if (!candidateProfileData?.jobApplicationId || isGeneratingFinalSummary) return;

  //   setIsGeneratingFinalSummary(true);
  //   try {
  //     const response = await generateFinalSummary(candidateId, candidateProfileData.jobApplicationId);
  //     if (response?.data?.success) {
  //       toastMessageSuccess(t(response?.data?.message || "final_summary_generated_successfully"));
  //       // Set flag to indicate final summary has been generated by user
  //       setHasFinalSummaryBeenGenerated(true);
  //       // Switch to Skill Specific Assessment tab automatically
  //       setSelectedTab(true);
  //       // Load skill-specific assessment data
  //       await Promise.all([loadFinalAssessment(), loadSkillSpecificAssessment()]);
  //     } else {
  //       toastMessageError(t(response?.data?.message || "failed_to_generate_final_summary"));
  //     }
  //   } catch (error) {
  //     console.error("Error generating final summary:", error);
  //     toastMessageError(t("something_went_wrong"));
  //   } finally {
  //     setIsGeneratingFinalSummary(false);
  //   }
  // };

  // ============================================================================
  // COMPUTED VALUES AND MEMOIZED DATA
  // ============================================================================

  /**
   * Memoized AI interviewer analysis for the current user
   * Finds analysis data where the interviewer ID matches the current user
   */
  const aiInterviewerAnalysis = useMemo(
    () =>
      candidateInterviewHistory?.find((record) => record.interviewerId === authData?.id && record.interviewerPerformanceAiAnalysis)
        ?.interviewerPerformanceAiAnalysis,
    [candidateInterviewHistory, authData?.id]
  );

  /**
   * Memoized all interviewer analysis data for admin users
   * Returns all interviewer performance AI analysis data for admin users
   */
  const allInterviewerAnalysis = useMemo(() => {
    if (!candidateInterviewHistory || !authData || authData.account_type !== "admin") return [];

    return candidateInterviewHistory
      .filter((record) => record.interviewerPerformanceAiAnalysis)
      .map((record) => ({
        interviewerId: record.interviewerId,
        interviewerName: record.interviewerName,
        interviewerImage: record.interviewerImage,
        roundNumber: record.roundNumber,
        analysis: record.interviewerPerformanceAiAnalysis,
      }));
  }, [candidateInterviewHistory, authData]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  /**
   * Initial data loading effect
   * Loads candidate profile and interview history data in parallel when component mounts or candidateId changes
   */
  useEffect(() => {
    const initializeData = async () => {
      // Set loading states immediately to prevent "No data" messages
      setCandidateProfileLoaded(true);
      setIsLoadingInterviewHistory(true);

      // Load candidate profile and interview history data in parallel
      await Promise.all([loadCandidateProfile(), loadCandidateInterviewHistory()]);
    };

    initializeData();
  }, [jobApplicationId]);

  /**
   * Effect to set the first skill as active when skill assessment data is loaded
   * Ensures there's always an active skill tab when data is available
   */
  useEffect(() => {
    if (skillSpecificAssessment?.skillsScores && skillSpecificAssessment.skillsScores.length > 0 && !activeSkillTab) {
      setActiveSkillTab(skillSpecificAssessment.skillsScores[0].skill_name);
    }
  }, [skillSpecificAssessment, activeSkillTab]);

  /**
   * Effect to animate the circular progress bar
   * Creates a smooth animation from 0 to the final success probability value
   */
  useEffect(() => {
    const timer = requestAnimationFrame(() => setAnimationValue(finalAssessment?.overallSuccessProbability || 0));
    return () => cancelAnimationFrame(timer);
  }, [finalAssessment?.overallSuccessProbability]);

  // ============================================================================
  // RENDER LOGIC
  // ============================================================================

  /**
   * Renders the skill details section for the selected skill
   * Shows strengths, potential gaps, and success probability
   */
  const renderSkillDetails = () => {
    if (!skillSpecificAssessment?.skillsScores || !activeSkillTab) return null;

    const selectedSkill = skillSpecificAssessment.skillsScores.find((skill) => skill.skill_name === activeSkillTab);

    if (!selectedSkill) return null;

    const probability = selectedSkill.probability_of_success_in_this_skill?.probabilityOfSuccessInSkill || 0;
    const filledBars = calculateFilledBars(probability);

    return (
      <div className="row">
        <div className="col-md-7">
          <h4 className="skill-sub-title">Strengths</h4>
          <ul className="strengths">
            {selectedSkill.strengths?.strengths?.map((strength, index) => (
              <li key={index} className="strength-item">
                {strength}
              </li>
            ))}
          </ul>
          <h4 className="skill-sub-title">Potential Gaps</h4>
          <ul className="strengths">
            {selectedSkill.potentials_gaps?.potentialGaps?.map((gap, index) => (
              <li key={index} className="strength-item">
                {gap}
              </li>
            ))}
          </ul>
        </div>
        <div className="col-md-5">
          <div className="probability-card">
            <h4 className="skill-sub-title">Skilssssl Success Probability</h4>
            <div className="progress-container">
              <h3 className="ms-2 fw-bold">{probability}%</h3>
              <div className="probability-bar">
                {Array.from({ length: 10 }, (_, index) => (
                  <div key={index} className={`bar ${index < filledBars ? "filled" : ""}`} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <QuestionGeneratorLoader show={buttonState === BUTTON_STATES.GENERATING} />
      {showConfirmModal && (
        <FinalAssessmentConfirmModal onClickCancel={handleCancelConfirmModal} onClickGenerate={handleCreateFinalAssessment} disabled={isLoading} />
      )}

      {/* Confirmation Modal for various actions */}
      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        onClose={closeConfirmationModal}
        onConfirm={confirmationModal.onConfirm}
        title={confirmationModal.title}
        message={confirmationModal.message}
        confirmButtonText={confirmationModal.confirmButtonText}
        loading={confirmationModal.loading}
        loadingText={t("processing")}
      />

      <div className={style.conduct_interview_page}>
        <div className="container">
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  <Button className="clear-btn p-0 m-0">
                    <BackArrowIcon
                      onClick={() => {
                        router.back();
                      }}
                    />
                  </Button>{" "}
                  {t("candidate")} <span>{t("profile")} </span>
                </h2>

                <Button
                  className="theme-btn clear-btn text-btn primary p-0 m-0"
                  onClick={() => {
                    if (!isLoading) {
                      if (!assessmentStatus?.exists) {
                        showCreateFinalAssessmentConfirmation();
                      } else if (assessmentStatus?.assessmentId) {
                        // Redirect to the final assessment page with the existing assessment ID
                        router.push(
                          `${ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${assessmentStatus.assessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`
                        );
                      }
                    }
                  }}
                  disabled={isLoading}
                >
                  {buttonState === "loading" || (isLoading && buttonState !== "generating") ? "" : <FinalAssessmentIcon />}
                  {buttonState === "loading" || (isLoading && buttonState !== "generating") ? (
                    <Skeleton width={100} height={30} borderRadius={12} />
                  ) : buttonState === "generating" ? (
                    t("generating_questions")
                  ) : !assessmentStatus?.exists ? (
                    t("create_final_assessment")
                  ) : assessmentStatus.isAssessmentSubmitted ? (
                    t("view_final_assessment_result")
                  ) : (
                    t("view_final_assessment")
                  )}
                </Button>

                {/* Conditionally render resume preview button if resumeLink exists */}
                {candidateProfileData && candidateProfileData.resumeLink && (
                  <>
                    <Button
                      onClick={() => setResumeModal({ isOpen: true, resumeLink: candidateProfileData?.resumeLink })}
                      className="theme-btn clear-btn text-btn primary p-0 m-0"
                    >
                      <PreviewResumeIcon className="me-2" />
                      {t("preview_candidate_resume")}
                    </Button>
                    <ResumeModal
                      isOpen={resumeModal.isOpen}
                      onClose={() => setResumeModal({ ...resumeModal, isOpen: false })}
                      resumeLink={resumeModal.resumeLink}
                    />
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="inner-section profile-section">
            <div className="candidate-profile">
              {candidateProfileLoaded ? (
                <Skeleton height={100} width={100} borderRadius={12} />
              ) : (
                <Avatar
                  src={candidateProfileData?.imageUrl || undefined}
                  name={candidateProfileData?.candidateName || ""}
                  size="100"
                  round={true}
                  className="candidate-image"
                />
              )}
              <div className="candidate-info">
                {/*  Use candidateName from API */}
                <h3 className="candidate-name">
                  {candidateProfileLoaded ? <Skeleton width={150} height={25} /> : toTitleCase(candidateProfileData?.candidateName || "-")}
                </h3>

                <div className="info-container">
                  <div className="info-item">
                    <p className="info-title">{t("post_applied_for")}</p>
                    {/* Use jobTitle from API */}
                    <p className="info-value">
                      {candidateProfileLoaded ? <Skeleton width={100} height={25} /> : candidateProfileData?.jobTitle || "-"}
                    </p>
                  </div>
                  <div className="info-item">
                    <p className="info-title">{t("department")}</p>
                    <p className="info-value">
                      {candidateProfileLoaded ? <Skeleton width={100} height={25} /> : candidateProfileData?.department || "-"}
                    </p>
                  </div>
                  <div className="info-item">
                    <p className="info-title">{t("current_round")}</p>
                    <p className="info-value">
                      {candidateProfileLoaded ? <Skeleton width={20} height={18} /> : candidateProfileData?.roundNumber || 0}
                    </p>
                  </div>
                  <div className="info-item">
                    <p className="info-title">{t("resume_approved_by")}</p>
                    <p className="info-value with-img">
                      {candidateProfileLoaded ? (
                        <Skeleton width={20} height={20} />
                      ) : (
                        <Image src={candidateProfileData?.interviewerImage || noImageFound} alt="Interviewer avatar" height={20} width={20} />
                      )}
                      {/*  Use interviewerName from API */}
                      {candidateProfileLoaded ? <Skeleton width={100} height={25} /> : candidateProfileData?.interviewerName || "-"}
                    </p>
                  </div>
                  {candidateProfileLoaded ? (
                    <Skeleton height={40} width={100} borderRadius={12} />
                  ) : candidateProfileData ? (
                    <div className="button-align">
                      {/* Show hire/reject buttons only when user has generated final summary */}
                      {hasFinalSummaryBeenGenerated && !isGeneratingFinalSummary && assessmentStatus?.exists ? (
                        <>
                          <Button
                            className="primary-btn rounded-md minWidth"
                            onClick={showHireConfirmation}
                            disabled={isProcessing || !candidateProfileData}
                          >
                            {isProcessing ? <Loader /> : t("hire")}
                          </Button>
                          <Button
                            className="dark-outline-btn rounded-md minWidth"
                            onClick={showRejectConfirmation}
                            disabled={isProcessing || !candidateProfileData}
                          >
                            {isProcessing ? <Loader /> : t("reject")}
                          </Button>
                        </>
                      ) : (
                        /* Show generate final summary button when user hasn't generated final summary yet */
                        <Button
                          className="primary-btn rounded-md minWidth"
                          onClick={showGenerateFinalSummaryConfirmation}
                          disabled={isGeneratingFinalSummary || !candidateProfileData?.jobApplicationId}
                        >
                          {isGeneratingFinalSummary ? <Loader /> : t("generate_final_summary")}
                        </Button>
                      )}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </div>
            </div>
            {/* Interview History Button - Always visible */}
            {!hasFinalSummaryBeenGenerated && (
              <div className="mb-4" onClick={handleInterviewHistoryClick}>
                <h2>{t("interview_history")}</h2>
              </div>
            )}

            {/* Skill Specific Assessment Tab - Only visible after final summary generation */}
            {hasFinalSummaryBeenGenerated && (
              <div className="common-tab mb-5">
                <li className={selectedTab ? "active" : ""} onClick={handleSkillAssessmentClick}>
                  {t("skill_specific_assessment")}
                </li>
                <li className={!selectedTab ? "active" : ""} onClick={handleInterviewHistoryClick}>
                  {t("interview_history")}
                </li>
              </div>
            )}
            {selectedTab && (
              <div className="assessment-content">
                {!finalAssessmentLoaded ? (
                  // Loading state - show skeleton loaders
                  <div className="row g-4">
                    <div className="col-md-4">
                      <div className="improvement-areas-card">
                        <Skeleton height={200} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                    <div className="col-md-8">
                      <div className="improvement-areas-card">
                        <Skeleton height={200} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                  </div>
                ) : finalAssessmentLoaded && finalAssessment ? (
                  // Data available - show actual content
                  <div className="row g-4">
                    {finalAssessment && finalAssessment.overallSuccessProbability && finalAssessment.overallSuccessProbability && (
                      <div className="col-md-4">
                        <div className="circular-progress-card">
                          <CircularProgressbar
                            className="circular-progress-bar"
                            value={animationValue}
                            text={`${animationValue}%`} // ⇐ plain string
                            circleRatio={0.5}
                            strokeWidth={16}
                            styles={buildStyles({
                              rotation: 0.749,
                              strokeLinecap: "butt",
                              trailColor: "#eee",
                              textColor: "#333",
                              textSize: "1.6rem",
                              pathColor: "#9ebff7",
                              pathTransitionDuration: 0.5,
                            })}
                          />
                        </div>
                      </div>
                    )}
                    <div className="col-md-8">
                      <div className="summary-text-card row">
                        {finalAssessment &&
                          finalAssessment.skillSummary &&
                          finalAssessment.skillSummary &&
                          finalAssessment.skillSummary.finalSummary.length > 0 && (
                            <div className="col-md-9">
                              <h3 className="sub-tittle mt-0">
                                <AiMarkIcon className="me-2" /> {t("ai_summary")}
                              </h3>
                              <ul className="check-list">
                                {finalAssessment?.skillSummary?.finalSummary?.map((item: string, index) => {
                                  return (
                                    <li key={index}>
                                      <CheckSecondaryIcon className="me-2" /> {item}
                                    </li>
                                  );
                                })}
                              </ul>
                            </div>
                          )}
                        <div className="col-md-3">
                          <AIVerifiedIcon />
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // No data available - show message
                  <div className="no-final-assessment">
                    <p>{t("no_final_assessment_data_found")}</p>
                  </div>
                )}
                <div className="row g-4">
                  <div className="col-12 col-md-6 d-flex">
                    <div className="skills-score-card skills-graph-card flex-grow-1">
                      {!skillAssessmentLoaded ? (
                        <Skeleton height={300} width={"100%"} borderRadius={20} />
                      ) : (
                        <>
                          {/* <h3 className="sub-tittle mt-0">
                            <AiMarkIcon className="me-2" /> AI Summary
                          </h3> */}
                          <Radar data={radarChartData} options={{ maintainAspectRatio: false }} />
                        </>
                      )}
                    </div>
                  </div>

                  <div className="col-12 col-md-6 d-flex">
                    <div className="skills-score-card skills-graph-card flex-grow-1">
                      {!skillAssessmentLoaded ? (
                        <Skeleton height={300} width={"100%"} borderRadius={20} />
                      ) : (
                        <Bar
                          data={barChartData}
                          options={{
                            maintainAspectRatio: false,
                            plugins: { legend: { display: false }, title: { display: false } },
                            scales: { x: { grid: { display: false } } },
                          }}
                        />
                      )}
                    </div>
                  </div>
                </div>
                {/* next design */}
                <div className="row g-4">
                  {!skillAssessmentLoaded ? (
                    <div className="col-md-4">
                      <div className="improvement-areas-card">
                        <Skeleton height={200} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                  ) : skillSpecificAssessment && skillSpecificAssessment.skillsScores && skillSpecificAssessment.skillsScores.length > 0 ? (
                    <div className="col-lg-4">
                      <div className="summary-text-card skills-score-card">
                        <h3 className="sub-tittle mt-0">{t("skills_score")}</h3>
                        <ul className="skills-list">
                          <li className="skills-item">
                            <span className="skill-name">{t("career_based_skills")}</span>
                            <span className="skill-rating">
                              <StarIcon /> {skillSpecificAssessment.careerBasedSkillsScore}/10
                            </span>
                          </li>
                          {skillSpecificAssessment &&
                            skillSpecificAssessment.skillsScores &&
                            skillSpecificAssessment.skillsScores.length > 0 &&
                            skillSpecificAssessment.skillsScores.map((item, index) => {
                              return (
                                <li key={index} className="skills-item">
                                  <span className="skill-name">{item.skill_name}</span>
                                  <span className="skill-rating">
                                    {item.skill_marks === 10 ? (
                                      <span className="skill-badge">{t("extreme")}</span>
                                    ) : (
                                      <>
                                        <StarIcon /> {item.skill_marks}/10
                                      </>
                                    )}
                                  </span>
                                </li>
                              );
                            })}
                        </ul>
                      </div>
                    </div>
                  ) : (
                    ""
                  )}
                  {!skillAssessmentLoaded ? (
                    <div className="col-lg-8">
                      <div className="improvement-areas-card">
                        <Skeleton height={200} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                  ) : (
                    skillSpecificAssessment &&
                    skillSpecificAssessment.skillsScores && (
                      <div className="col-lg-8">
                        <div className="summary-text-card skills-summary-card">
                          <h3 className="sub-tittle mt-0">{t("skills_summary")}</h3>
                          <div className="skills-tags">
                            {skillSpecificAssessment &&
                              skillSpecificAssessment.skillsScores &&
                              skillSpecificAssessment.skillsScores.map((skill, index) => (
                                <span
                                  key={index}
                                  className={`skill-tag ${activeSkillTab === skill.skill_name ? "active" : ""}`}
                                  onClick={() => setActiveSkillTab(skill.skill_name)}
                                >
                                  {skill.skill_name}
                                </span>
                              ))}
                          </div>

                          <div className="strengths-gaps">{renderSkillDetails()}</div>
                        </div>
                      </div>
                    )
                  )}
                  <div className="col-12">
                    {!finalAssessmentLoaded ? (
                      // Loading state - show skeleton loaders
                      <div className="improvement-areas-card">
                        <Skeleton height={18} width={"20%"} borderRadius={6} />
                        <div className="row g-3 mt-3">
                          <div className="col-md-4">
                            <Skeleton height={177} width={"100%"} borderRadius={24} />
                          </div>
                          <div className="col-md-4">
                            <Skeleton height={177} width={"100%"} borderRadius={24} />
                          </div>
                          <div className="col-md-4">
                            <Skeleton height={177} width={"100%"} borderRadius={24} />
                          </div>
                        </div>
                      </div>
                    ) : finalAssessmentLoaded &&
                      finalAssessment &&
                      finalAssessment.developmentRecommendations &&
                      finalAssessment.developmentRecommendations.recommendations &&
                      finalAssessment.developmentRecommendations.recommendations.length > 0 ? (
                      // Data available - show actual content
                      <div className="summary-text-card improvement-areas-card">
                        <h3 className="sub-tittle mt-0">{t("improvement_areas")}</h3>
                        <div className="row g-4">
                          {finalAssessment.developmentRecommendations.recommendations.map((recommendation, index) => (
                            <div key={index} className="col-md-4">
                              <div className="improvement-card h-100">
                                <h4 className="title">{recommendation.title}</h4>
                                <p className="description">{recommendation.description}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      // No data available - show message (this covers both no finalAssessment and no developmentRecommendations)
                      <div className="no-improvement-areas">
                        <p>{t("no_improvement_areas_data_found")}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* end next design */}
              </div>
            )}
            {!selectedTab &&
              (isLoadingInterviewHistory ? (
                <div className="history-content">
                  <div className="interview-summary">
                    <div className="summary-header">
                      <Skeleton height={21} width={"20%"} borderRadius={6} />
                    </div>
                    <div className="interviewer">
                      <Skeleton height={21} width={"20%"} className="mb-4" borderRadius={6} />

                      <div className="interviewer-info">
                        <Skeleton height={45} width={45} borderRadius={100} />
                        <Skeleton height={18} width={100} borderRadius={6} />
                      </div>
                    </div>
                    <div className="summary-scores">
                      <Skeleton height={18} width={100} className="mb-4" borderRadius={6} />
                      <div className="score-btns mt-2">
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                      </div>
                    </div>
                    <div className="summary-highlights">
                      <Skeleton height={18} width={100} className="mb-4" borderRadius={6} />
                      <ul className="highlight-list p-0">
                        <Skeleton height={16} width={"80%"} className="mb-3" borderRadius={6} count={4} />
                      </ul>
                    </div>
                  </div>
                  <div className="interview-summary">
                    <div className="summary-header">
                      <Skeleton height={21} width={"20%"} borderRadius={6} />
                    </div>
                    <div className="interviewer">
                      <div className="interviewer-info large">
                        <Skeleton height={45} width={45} borderRadius={100} />
                        <Skeleton height={18} width={100} borderRadius={6} />
                      </div>
                    </div>
                    <div className="summary-highlights">
                      <Skeleton height={18} width={100} borderRadius={6} className="mb-4" />
                      <ul className="highlight-list p-0">
                        <Skeleton height={16} width={"80%"} className="mb-3" borderRadius={6} count={4} />
                      </ul>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="history-content">
                  {candidateInterviewHistory && candidateInterviewHistory.length > 0 ? (
                    <>
                      {candidateInterviewHistory.map((historyItem, index) => (
                        <div key={index} className="interview-summary">
                          <div className="summary-header">
                            <h1 className="summary-heading">
                              {t("round")} {historyItem.roundNumber} {t("summary")}
                            </h1>
                          </div>
                          <div className="interviewer">
                            <h2 className="summary-title">{t("interview_by")}</h2>
                            <div className="interviewer-info">
                              <Image
                                src={historyItem.interviewerImage || noImageFound}
                                alt={t("interviewer_avatar")}
                                className="interviewer-avatar"
                                width={50}
                                height={50}
                              />
                              <span className="interviewer-name">{historyItem.interviewerName}</span>
                              {historyItem.endTime ? (
                                <div className="text-muted small">
                                  <p>
                                    {" "}
                                    {new Date(historyItem.endTime).toLocaleString(undefined, {
                                      year: "numeric",
                                      month: "short",
                                      day: "numeric",
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })}
                                  </p>
                                </div>
                              ) : null}
                            </div>
                          </div>
                          <div className="summary-scores">
                            <h2 className="summary-title">{t("scores")}</h2>
                            <div className="score-btns">
                              <Button className="secondary-btn rounded-md px-3 py-3">
                                {t("hard_skills")} : {historyItem.hardSkillMarks}
                              </Button>
                              {Object.entries(historyItem.skillScores).map(([skillName, score]) => (
                                <Button key={skillName} className="secondary-btn rounded-md px-3 py-3">
                                  {skillName} : {score === 10 ? t("extreme") : score}
                                </Button>
                              ))}
                            </div>
                          </div>
                          <div className="summary-highlights">
                            <h2 className="summary-title">{t("highlights")}</h2>
                            <ul className="highlight-list">
                              {historyItem.interviewSummary?.highlight.map((item: string, index) => {
                                return (
                                  <li key={index} className="highlight-item">
                                    {item}
                                  </li>
                                );
                              })}
                            </ul>
                          </div>
                        </div>
                      ))}
                      {/* Show current user's performance feedback */}
                      {aiInterviewerAnalysis && (
                        <div className="interview-summary">
                          <div className="summary-header">
                            <h1 className="summary-heading">{t("your_performance_feedback")}</h1>
                          </div>
                          <div className="interviewer">
                            <div className="interviewer-info large">
                              <Image
                                src={authData?.image || noImageFound}
                                alt={t("interviewer_avatar")}
                                className="interviewer-avatar"
                                width={50}
                                height={50}
                              />
                              <span className="interviewer-name">
                                {authData?.first_name} {authData?.last_name}
                              </span>
                            </div>
                          </div>
                          <div className="summary-highlights">
                            <h2 className="summary-title">{t("highlights")}</h2>
                            <ul className="highlight-list">
                              {aiInterviewerAnalysis.highlights?.map((item: string, index: number) => {
                                return (
                                  <li key={index} className="highlight-item">
                                    {item}
                                  </li>
                                );
                              })}
                            </ul>
                          </div>
                        </div>
                      )}

                      {/* Show all interviewer performance feedback for admin users */}
                      {authData?.account_type === "admin" && allInterviewerAnalysis.length > 0 && (
                        <div className="admin-interviewer-analysis">
                          <div className="summary-header">
                            <h1 className="summary-heading">{t("all_interviewer_performance_feedback")}</h1>
                          </div>
                          {allInterviewerAnalysis.map((analysisData) => (
                            <div key={`${analysisData.interviewerId}-${analysisData.roundNumber}`} className="interview-summary">
                              <div className="interviewer">
                                <div className="interviewer-info large">
                                  <Image
                                    src={analysisData.interviewerImage || noImageFound}
                                    alt={t("interviewer_avatar")}
                                    className="interviewer-avatar"
                                    width={50}
                                    height={50}
                                  />
                                  <span className="interviewer-name">
                                    {analysisData.interviewerName} - Round {analysisData.roundNumber}
                                  </span>
                                </div>
                              </div>
                              {analysisData.analysis?.highlights && (
                                <div className="summary-highlights">
                                  <h2 className="summary-title">{t("highlights")}</h2>
                                  <ul className="highlight-list">
                                    {analysisData.analysis.highlights.map((item: string, highlightIndex: number) => (
                                      <li key={highlightIndex} className="highlight-item">
                                        {item}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="no-interview-history">
                      <p>{t("no_interview_history_found")}</p>
                    </div>
                  )}
                </div>
              ))}
          </div>
        </div>
      </div>
    </>
  );
};
export default CandidateProfile;
